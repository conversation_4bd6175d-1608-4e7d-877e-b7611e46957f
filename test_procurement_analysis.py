"""test_procurement_analysis.py
Тест анализа закупок для определения корпоративных email
"""

import os
import sys
import asyncio
import pandas as pd

# Устанавливаем API ключи для тестирования
openai_api_key = os.getenv("OPENAI_API_KEY")

if not openai_api_key:
    print("❌ Установите OPENAI_API_KEY для тестирования")
    sys.exit(1)

# Предотвращаем запуск CLI
sys.argv = ['test']

def create_test_excel():
    """Создаем тестовый Excel файл с закупками"""
    print("📁 Создание тестового Excel файла с закупками...")
    
    # Тестовые данные закупок
    test_data = {
        'A': ['Закупка 1', 'Закупка 2', 'Закупка 3', 'Закупка 4', 'Закупка 5'],
        'B': ['Заказчик 1', 'Российский Фонд Информации по Природным Ресурсам', 'Заказчик 3', 'РФИ МНР', 'Заказчик 5'],
        'C': ['Поставщик 1', 'Поставщик 2', 'Поставщик 3', 'Поставщик 4', 'Поставщик 5'],
        'V': [
            'Контакт: Иванов И.И., тел: +7(495)123-45-67',
            'Ответственный: Стройков Валерий Александрович, тел: +7(495)987-65-43, email: <EMAIL>',
            'Менеджер: Петров П.П., email: <EMAIL>',
            'Контакт: Иванова Наталия, тел: +7(495)555-12-34, email: <EMAIL>, доп. email: <EMAIL>',
            'Связь: Сидоров С.С., email: <EMAIL>'
        ]
    }
    
    df = pd.DataFrame(test_data)
    excel_path = "test_procurement_data.xlsx"
    df.to_excel(excel_path, index=False)
    
    print(f"✅ Создан тестовый файл: {excel_path}")
    print(f"📊 Строк данных: {len(df)}")
    
    return excel_path

def test_email_extraction():
    """Тест извлечения email из текста"""
    print("\n🧪 ТЕСТ ИЗВЛЕЧЕНИЯ EMAIL ИЗ ТЕКСТА")
    print("=" * 60)
    
    try:
        from leadgen_enhanced import extract_emails_from_text
        
        test_cases = [
            {
                "text": "Контакт: Иванов И.И., email: <EMAIL>, тел: +7(495)123-45-67",
                "expected": ["<EMAIL>"]
            },
            {
                "text": "Ответственный: Петров, email: <EMAIL>, доп: <EMAIL>",
                "expected": ["<EMAIL>", "<EMAIL>"]
            },
            {
                "text": "Связь: <EMAIL> и <EMAIL>",
                "expected": ["<EMAIL>", "<EMAIL>"]
            },
            {
                "text": "Нет email адресов в этом тексте",
                "expected": []
            }
        ]
        
        all_correct = True
        for i, test_case in enumerate(test_cases, 1):
            print(f"\n📝 Тест {i}:")
            print(f"  Текст: {test_case['text']}")
            
            result = extract_emails_from_text(test_case['text'])
            expected = test_case['expected']
            
            # Сортируем для сравнения
            result_sorted = sorted(result)
            expected_sorted = sorted(expected)
            
            correct = result_sorted == expected_sorted
            
            print(f"  Ожидаемые: {expected_sorted}")
            print(f"  Найденные: {result_sorted}")
            print(f"  {'✅ Корректно' if correct else '❌ Ошибка'}")
            
            if not correct:
                all_correct = False
        
        return all_correct
        
    except Exception as e:
        print(f"❌ Ошибка теста: {e}")
        return False

def test_corporate_email_filtering():
    """Тест фильтрации корпоративных email"""
    print("\n🧪 ТЕСТ ФИЛЬТРАЦИИ КОРПОРАТИВНЫХ EMAIL")
    print("=" * 60)
    
    try:
        from leadgen_enhanced import filter_corporate_emails
        
        test_emails = [
            "<EMAIL>",  # Корпоративный
            "<EMAIL>",          # Публичный
            "<EMAIL>",         # Корпоративный
            "<EMAIL>",          # Публичный
            "<EMAIL>",       # Публичный
            "<EMAIL>"  # Корпоративный
        ]
        
        expected_corporate = [
            "<EMAIL>",
            "<EMAIL>", 
            "<EMAIL>"
        ]
        
        print(f"📧 Тестовые email: {test_emails}")
        
        result = filter_corporate_emails(test_emails)
        
        # Сортируем для сравнения
        result_sorted = sorted(result)
        expected_sorted = sorted(expected_corporate)
        
        correct = result_sorted == expected_sorted
        
        print(f"\n📊 Результат фильтрации:")
        print(f"  Ожидаемые корпоративные: {expected_sorted}")
        print(f"  Найденные корпоративные: {result_sorted}")
        print(f"  {'✅ Корректно' if correct else '❌ Ошибка'}")
        
        return correct
        
    except Exception as e:
        print(f"❌ Ошибка теста: {e}")
        return False

async def test_procurement_email_analysis():
    """Тест анализа корпоративных email через GPT"""
    print("\n🧪 ТЕСТ АНАЛИЗА КОРПОРАТИВНЫХ EMAIL ЧЕРЕЗ GPT")
    print("=" * 60)
    
    try:
        from leadgen_enhanced import analyze_procurement_emails
        import openai
        
        client = openai.AsyncClient(api_key=openai_api_key)
        
        # Тестовые корпоративные email
        test_emails = [
            "<EMAIL>",
            "<EMAIL>", 
            "<EMAIL>"
        ]
        
        print(f"📧 Тестовые корпоративные email: {test_emails}")
        
        result = await analyze_procurement_emails(client, test_emails)
        
        print(f"\n📊 Результат анализа:")
        print(f"  Домен: {result.get('domain', '')}")
        print(f"  Паттерн: {result.get('pattern', '')}")
        print(f"  Пример: {result.get('example', '')}")
        
        # Проверяем результат
        has_domain = bool(result.get('domain'))
        has_example = bool(result.get('example'))
        correct_domain = 'rfimnr.ru' in result.get('domain', '')
        
        print(f"\n✅ Домен найден: {has_domain}")
        print(f"✅ Пример найден: {has_example}")
        print(f"✅ Правильный домен: {correct_domain}")
        
        return has_domain and has_example and correct_domain
        
    except Exception as e:
        print(f"❌ Ошибка теста: {e}")
        return False

async def test_email_generation_by_example():
    """Тест генерации email по примеру"""
    print("\n🧪 ТЕСТ ГЕНЕРАЦИИ EMAIL ПО ПРИМЕРУ")
    print("=" * 60)
    
    try:
        from leadgen_enhanced import generate_email_by_example
        import openai
        
        client = openai.AsyncClient(api_key=openai_api_key)
        
        test_cases = [
            {
                "first_name": "Алексей",
                "last_name": "Петров",
                "example": "<EMAIL>",
                "domain": "rfimnr.ru",
                "expected_pattern": "<EMAIL>"
            },
            {
                "first_name": "Мария",
                "last_name": "Сидорова",
                "example": "<EMAIL>",
                "domain": "rfimnr.ru",
                "expected_pattern": "<EMAIL>"
            }
        ]
        
        all_correct = True
        for i, test_case in enumerate(test_cases, 1):
            print(f"\n📝 Тест {i}:")
            print(f"  Имя: {test_case['first_name']} {test_case['last_name']}")
            print(f"  Пример: {test_case['example']}")
            print(f"  Домен: {test_case['domain']}")
            
            result = await generate_email_by_example(
                client,
                test_case['first_name'],
                test_case['last_name'],
                test_case['example'],
                test_case['domain']
            )
            
            print(f"  Сгенерированный email: {result}")
            
            # Проверяем, что email содержит правильный домен
            has_correct_domain = test_case['domain'] in result
            has_email_format = '@' in result and '.' in result
            
            print(f"  Правильный домен: {'✅' if has_correct_domain else '❌'}")
            print(f"  Формат email: {'✅' if has_email_format else '❌'}")
            
            if not (has_correct_domain and has_email_format):
                all_correct = False
        
        return all_correct
        
    except Exception as e:
        print(f"❌ Ошибка теста: {e}")
        return False

async def test_full_procurement_analysis():
    """Тест полного анализа закупок"""
    print("\n🧪 ТЕСТ ПОЛНОГО АНАЛИЗА ЗАКУПОК")
    print("=" * 60)
    
    try:
        from leadgen_enhanced import analyze_procurement_data
        import openai
        
        client = openai.AsyncClient(api_key=openai_api_key)
        
        # Создаем тестовый Excel файл
        excel_path = create_test_excel()
        
        company_name = "Российский Фонд Информации по Природным Ресурсам"
        
        print(f"🏢 Компания: {company_name}")
        print(f"📁 Excel файл: {excel_path}")
        
        result = await analyze_procurement_data(client, company_name, excel_path)
        
        print(f"\n📊 Результат полного анализа:")
        print(f"  Домен: {result.get('domain', '')}")
        print(f"  Паттерн: {result.get('pattern', '')}")
        print(f"  Пример: {result.get('example', '')}")
        
        # Проверяем результат
        has_domain = bool(result.get('domain'))
        has_example = bool(result.get('example'))
        correct_domain = 'rfimnr.ru' in result.get('domain', '')
        
        print(f"\n✅ Домен найден: {has_domain}")
        print(f"✅ Пример найден: {has_example}")
        print(f"✅ Правильный домен: {correct_domain}")
        
        # Удаляем тестовый файл
        if os.path.exists(excel_path):
            os.remove(excel_path)
            print(f"🗑️ Удален тестовый файл: {excel_path}")
        
        return has_domain and has_example and correct_domain
        
    except Exception as e:
        print(f"❌ Ошибка теста: {e}")
        return False

def test_imports():
    """Тест импортов функций анализа закупок"""
    print("\n🧪 ТЕСТ ИМПОРТОВ ФУНКЦИЙ АНАЛИЗА ЗАКУПОК")
    print("=" * 60)
    
    try:
        from leadgen_enhanced import (
            PROCUREMENT_ENABLE,
            PROCUREMENT_COLUMN,
            PUBLIC_DOMAINS,
            extract_emails_from_text,
            filter_corporate_emails,
            analyze_procurement_emails,
            generate_email_by_example,
            analyze_procurement_data
        )
        
        print("✅ Все функции анализа закупок импортированы успешно:")
        print("  • PROCUREMENT_ENABLE (настройка)")
        print("  • PROCUREMENT_COLUMN (настройка)")
        print("  • PUBLIC_DOMAINS (список)")
        print("  • extract_emails_from_text (извлечение email)")
        print("  • filter_corporate_emails (фильтрация)")
        print("  • analyze_procurement_emails (анализ через GPT)")
        print("  • generate_email_by_example (генерация по примеру)")
        print("  • analyze_procurement_data (полный анализ)")
        
        print(f"\n⚙️ Настройки:")
        print(f"  Анализ закупок включен: {PROCUREMENT_ENABLE}")
        print(f"  Столбец контактов: {PROCUREMENT_COLUMN}")
        print(f"  Публичных доменов: {len(PUBLIC_DOMAINS)}")
        
        return True
        
    except Exception as e:
        print(f"❌ Ошибка импорта: {e}")
        return False

async def main():
    """Главная функция тестирования"""
    print("🧪 ТЕСТИРОВАНИЕ АНАЛИЗА ЗАКУПОК ДЛЯ ОПРЕДЕЛЕНИЯ EMAIL")
    print("=" * 80)
    
    # Сначала тестируем импорты
    imports_ok = test_imports()
    
    if not imports_ok:
        print("❌ Проблемы с импортами, остальные тесты пропущены")
        return
    
    # Синхронные тесты
    sync_tests = [
        ("Извлечение email из текста", test_email_extraction()),
        ("Фильтрация корпоративных email", test_corporate_email_filtering())
    ]
    
    # Асинхронные тесты
    async_tests = [
        ("Анализ корпоративных email через GPT", test_procurement_email_analysis()),
        ("Генерация email по примеру", test_email_generation_by_example()),
        ("Полный анализ закупок", test_full_procurement_analysis())
    ]
    
    print("\n📊 РЕЗУЛЬТАТЫ ТЕСТИРОВАНИЯ:")
    print("=" * 80)
    
    passed = 1 if imports_ok else 0  # Импорты уже протестированы
    print(f"  Импорты функций анализа закупок: {'✅ ПРОЙДЕН' if imports_ok else '❌ НЕ ПРОЙДЕН'}")
    
    # Синхронные тесты
    for test_name, result in sync_tests:
        status = "✅ ПРОЙДЕН" if result else "❌ НЕ ПРОЙДЕН"
        print(f"  {test_name}: {status}")
        if result:
            passed += 1
    
    # Асинхронные тесты
    for test_name, test_coro in async_tests:
        try:
            result = await test_coro
            status = "✅ ПРОЙДЕН" if result else "❌ НЕ ПРОЙДЕН"
            print(f"  {test_name}: {status}")
            if result:
                passed += 1
        except Exception as e:
            print(f"  {test_name}: ❌ НЕ ПРОЙДЕН ({e})")
    
    total_tests = len(sync_tests) + len(async_tests) + 1
    print(f"\n🎯 ИТОГО: {passed}/{total_tests} тестов пройдено")
    
    if passed == total_tests:
        print("🎉 ВСЕ ТЕСТЫ ПРОЙДЕНЫ!")
        print("\n✅ АНАЛИЗ ЗАКУПОК РАБОТАЕТ:")
        print("  • Извлечение email из текста закупок")
        print("  • Фильтрация корпоративных адресов")
        print("  • Умный анализ паттернов через GPT")
        print("  • Генерация email по сложным примерам")
        print("  • Полный анализ Excel файлов с закупками")
        print("  • ПРИОРИТЕТНЫЙ метод определения email")
    elif passed > 3:
        print("⚠️  Частично работает - проверьте API ключи")
    else:
        print("❌ Есть серьезные проблемы с анализом закупок")

if __name__ == "__main__":
    asyncio.run(main())
