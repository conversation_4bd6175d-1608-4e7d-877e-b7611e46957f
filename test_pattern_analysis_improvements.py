"""test_pattern_analysis_improvements.py
Тест улучшенного анализа email паттернов для простых примеров
"""

import os
import sys
import asyncio

# Устанавливаем API ключи для тестирования
openai_api_key = os.getenv("OPENAI_API_KEY")

if not openai_api_key:
    print("❌ Установите OPENAI_API_KEY для тестирования")
    sys.exit(1)

# Предотвращаем запуск CLI
sys.argv = ['test']

async def test_simple_surname_pattern():
    """Тест анализа простого паттерна с фамилией"""
    print("\n🧪 ТЕСТ ПРОСТОГО ПАТТЕРНА С ФАМИЛИЕЙ")
    print("=" * 60)
    
    try:
        from leadgen_enhanced import analyze_procurement_emails
        import openai
        
        client = openai.AsyncClient(api_key=openai_api_key)
        
        # Тестовые email с простыми фамилиями
        test_emails = [
            "<EMAIL>",
            "<EMAIL>"
        ]
        
        print(f"📧 Тестовые email: {test_emails}")
        print(f"🎯 Ожидаем: паттерн 'только фамилия'")
        
        for email in test_emails:
            print(f"\n  Анализ: {email}")
            result = await analyze_procurement_emails(client, [email])
            
            domain = result.get("domain", "")
            pattern = result.get("pattern", "")
            example = result.get("example", "")
            
            print(f"    Домен: {domain}")
            print(f"    Паттерн: {pattern}")
            print(f"    Пример: {example}")
            
            # Проверяем результат
            has_domain = bool(domain)
            pattern_mentions_surname = "фамилия" in pattern.lower() or "surname" in pattern.lower()
            example_is_simple = len(example.split('.')) == 1 and len(example.split('-')) == 1  # Нет точек и дефисов
            
            print(f"    ✅ Домен найден: {has_domain}")
            print(f"    ✅ Паттерн упоминает фамилию: {pattern_mentions_surname}")
            print(f"    ✅ Пример простой: {example_is_simple}")
        
        return True
        
    except Exception as e:
        print(f"❌ Ошибка теста: {e}")
        return False

async def test_email_generation_simple_pattern():
    """Тест генерации email по простому паттерну"""
    print("\n🧪 ТЕСТ ГЕНЕРАЦИИ EMAIL ПО ПРОСТОМУ ПАТТЕРНУ")
    print("=" * 60)
    
    try:
        from leadgen_enhanced import generate_email_by_example
        import openai
        
        client = openai.AsyncClient(api_key=openai_api_key)
        
        # Тестовые случаи с простыми примерами
        test_cases = [
            {
                "first_name": "Александр",
                "last_name": "Головко", 
                "example": "lebedev",
                "domain": "tgmu.ru",
                "expected_pattern": "только фамилия",
                "expected_result": "<EMAIL>"
            },
            {
                "first_name": "Дмитрий",
                "last_name": "Шульгин",
                "example": "zhilin", 
                "domain": "vzavod.ru",
                "expected_pattern": "только фамилия",
                "expected_result": "<EMAIL>"
            }
        ]
        
        for i, case in enumerate(test_cases, 1):
            print(f"\n  Тест {i}: {case['first_name']} {case['last_name']}")
            print(f"    Пример: {case['example']}")
            print(f"    Домен: {case['domain']}")
            print(f"    Ожидаемый паттерн: {case['expected_pattern']}")
            print(f"    Ожидаемый результат: {case['expected_result']}")
            
            result = await generate_email_by_example(
                client, 
                case['first_name'], 
                case['last_name'], 
                case['example'], 
                case['domain']
            )
            
            print(f"    Фактический результат: {result}")
            
            # Проверяем результат
            has_result = bool(result)
            correct_domain = case['domain'] in result
            is_surname_only = case['last_name'].lower() in result.lower() and case['first_name'].lower() not in result.lower()
            
            print(f"    ✅ Email сгенерирован: {has_result}")
            print(f"    ✅ Правильный домен: {correct_domain}")
            print(f"    ✅ Только фамилия (без имени): {is_surname_only}")
        
        return True
        
    except Exception as e:
        print(f"❌ Ошибка теста: {e}")
        return False

async def test_complex_vs_simple_patterns():
    """Тест различения сложных и простых паттернов"""
    print("\n🧪 ТЕСТ РАЗЛИЧЕНИЯ СЛОЖНЫХ И ПРОСТЫХ ПАТТЕРНОВ")
    print("=" * 60)
    
    try:
        from leadgen_enhanced import analyze_procurement_emails
        import openai
        
        client = openai.AsyncClient(api_key=openai_api_key)
        
        # Тестовые случаи
        test_cases = [
            {
                "emails": ["<EMAIL>"],
                "expected_type": "простой",
                "description": "Одно слово - фамилия"
            },
            {
                "emails": ["<EMAIL>"],
                "expected_type": "сложный", 
                "description": "Буква + точка + фамилия"
            },
            {
                "emails": ["<EMAIL>"],
                "expected_type": "сложный",
                "description": "Имя + точка + фамилия"
            },
            {
                "emails": ["<EMAIL>"],
                "expected_type": "сложный",
                "description": "Имя + фамилия слитно"
            }
        ]
        
        for i, case in enumerate(test_cases, 1):
            print(f"\n  Тест {i}: {case['description']}")
            print(f"    Email: {case['emails'][0]}")
            print(f"    Ожидаемый тип: {case['expected_type']}")
            
            result = await analyze_procurement_emails(client, case['emails'])
            
            pattern = result.get("pattern", "").lower()
            example = result.get("example", "")
            
            print(f"    Паттерн: {pattern}")
            print(f"    Пример: {example}")
            
            # Определяем тип паттерна
            is_simple = (
                "только фамилия" in pattern or 
                "surname only" in pattern or
                (len(example.split('.')) == 1 and len(example.split('-')) == 1)
            )
            
            is_complex = not is_simple
            
            actual_type = "простой" if is_simple else "сложный"
            correct_classification = actual_type == case['expected_type']
            
            print(f"    Фактический тип: {actual_type}")
            print(f"    ✅ Правильная классификация: {correct_classification}")
        
        return True
        
    except Exception as e:
        print(f"❌ Ошибка теста: {e}")
        return False

async def test_full_workflow_with_simple_pattern():
    """Тест полного workflow с простым паттерном"""
    print("\n🧪 ТЕСТ ПОЛНОГО WORKFLOW С ПРОСТЫМ ПАТТЕРНОМ")
    print("=" * 60)
    
    try:
        from leadgen_enhanced import process_single_contact_with_example
        import openai
        
        client = openai.AsyncClient(api_key=openai_api_key)
        
        # Тестовые контакты
        test_contacts = [
            {"first_name": "Александр", "last_name": "Головко", "position": "Руководитель проектов"},
            {"first_name": "Дмитрий", "last_name": "Шульгин", "position": "Руководитель службы эксплуатации"}
        ]
        
        # Простой пример (только фамилия)
        domain = "tgmu.ru"
        email_example = "lebedev"  # Только фамилия
        
        print(f"🏢 Домен: {domain}")
        print(f"📧 Пример: {email_example}")
        print(f"👥 Контактов: {len(test_contacts)}")
        print(f"🎯 Ожидаем: email только с фамилиями")
        
        results = []
        for contact in test_contacts:
            print(f"\n  Обработка: {contact['first_name']} {contact['last_name']}")
            
            result = await process_single_contact_with_example(
                client, contact, domain, email_example
            )
            
            results.append(result)
            print(f"    Результат: {result}")
            
            # Проверяем результат
            has_email = "@" in result
            correct_domain = domain in result
            surname_in_email = contact['last_name'].lower() in result.lower()
            name_not_in_email = contact['first_name'].lower() not in result.lower()
            
            print(f"    ✅ Содержит email: {has_email}")
            print(f"    ✅ Правильный домен: {correct_domain}")
            print(f"    ✅ Фамилия в email: {surname_in_email}")
            print(f"    ✅ Имя НЕ в email: {name_not_in_email}")
        
        print(f"\n📊 Итоговые результаты:")
        for result in results:
            print(f"  • {result}")
        
        return True
        
    except Exception as e:
        print(f"❌ Ошибка теста: {e}")
        return False

def test_imports():
    """Тест импортов улучшенных функций"""
    print("\n🧪 ТЕСТ ИМПОРТОВ УЛУЧШЕННЫХ ФУНКЦИЙ")
    print("=" * 60)
    
    try:
        from leadgen_enhanced import (
            analyze_procurement_emails,
            generate_email_by_example,
            process_single_contact_with_example
        )
        
        print("✅ Все улучшенные функции импортированы успешно:")
        print("  • analyze_procurement_emails (улучшенный анализ паттернов)")
        print("  • generate_email_by_example (улучшенная генерация)")
        print("  • process_single_contact_with_example (обновленная обработка)")
        
        return True
        
    except Exception as e:
        print(f"❌ Ошибка импорта: {e}")
        return False

async def main():
    """Главная функция тестирования"""
    print("🧪 ТЕСТИРОВАНИЕ УЛУЧШЕННОГО АНАЛИЗА EMAIL ПАТТЕРНОВ")
    print("=" * 80)
    
    # Сначала тестируем импорты
    imports_ok = test_imports()
    
    if not imports_ok:
        print("❌ Проблемы с импортами, остальные тесты пропущены")
        return
    
    # Асинхронные тесты
    async_tests = [
        ("Анализ простых паттернов с фамилией", test_simple_surname_pattern()),
        ("Генерация email по простому паттерну", test_email_generation_simple_pattern()),
        ("Различение сложных и простых паттернов", test_complex_vs_simple_patterns()),
        ("Полный workflow с простым паттерном", test_full_workflow_with_simple_pattern())
    ]
    
    print("\n📊 РЕЗУЛЬТАТЫ ТЕСТИРОВАНИЯ:")
    print("=" * 80)
    
    passed = 1 if imports_ok else 0  # Импорты уже протестированы
    print(f"  Импорты улучшенных функций: {'✅ ПРОЙДЕН' if imports_ok else '❌ НЕ ПРОЙДЕН'}")
    
    # Асинхронные тесты
    for test_name, test_coro in async_tests:
        try:
            result = await test_coro
            status = "✅ ПРОЙДЕН" if result else "❌ НЕ ПРОЙДЕН"
            print(f"  {test_name}: {status}")
            if result:
                passed += 1
        except Exception as e:
            print(f"  {test_name}: ❌ НЕ ПРОЙДЕН ({e})")
    
    total_tests = len(async_tests) + 1
    print(f"\n🎯 ИТОГО: {passed}/{total_tests} тестов пройдено")
    
    if passed == total_tests:
        print("🎉 ВСЕ ТЕСТЫ ПРОЙДЕНЫ!")
        print("\n✅ УЛУЧШЕННЫЙ АНАЛИЗ ПАТТЕРНОВ:")
        print("  • Правильное определение простых паттернов (только фамилия)")
        print("  • Различение простых и сложных случаев")
        print("  • Точная генерация email по простым примерам")
        print("  • Консистентность стиля во всех генерируемых email")
        print("  • Исправлена проблема с aleksandr.golovko вместо golovko")
    elif passed > 2:
        print("⚠️  Частично работает - проверьте промпты и логику")
    else:
        print("❌ Есть серьезные проблемы с анализом паттернов")

if __name__ == "__main__":
    asyncio.run(main())
