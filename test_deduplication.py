#!/usr/bin/env python3
"""
Тест для проверки функции дедупликации компаний
"""

import pandas as pd
import tempfile
import os
from leadgen_enhanced import read_companies_from_excel

def test_deduplication():
    """Тест дедупликации компаний"""
    
    # Создаем тестовые данные с дубликатами
    test_data = {
        'A': ['Строка1', 'Строка2', 'Строка3', 'Строка4', 'Строка5', 'Строка6'],
        'B': ['Данные1', 'Данные2', 'Данные3', 'Данные4', 'Данные5', 'Данные6'],
        'C': ['Инфо1', 'Инфо2', 'Инфо3', 'Инфо4', 'Инфо5', 'Инфо6'],
        'D': ['ООО "Компания А"', 'ООО "Компания Б"', 'ООО "Компания А"', 'ООО "Компания В"', 'ООО "Компания Б"', 'ООО "Компания Г"'],  # Дубликаты
        'E': ['1234567890', '0987654321', '1234567890', '1111111111', '0987654321', '2222222222'],  # ИНН с дубликатами (как строки)
        'AM': ['Менеджер1', 'Менеджер2', 'Менеджер1', 'Менеджер3', 'Менеджер2', 'Менеджер1']
    }
    
    # Создаем временный Excel файл
    with tempfile.NamedTemporaryFile(suffix='.xlsx', delete=False) as tmp_file:
        df = pd.DataFrame(test_data)
        # Убеждаемся, что ИНН сохраняются как строки
        df['E'] = df['E'].astype(str)

        # Используем openpyxl для правильного сохранения ИНН как текста
        from openpyxl import Workbook
        from openpyxl.utils.dataframe import dataframe_to_rows

        wb = Workbook()
        ws = wb.active

        # Добавляем данные
        for r in dataframe_to_rows(df, index=False, header=True):
            ws.append(r)

        # Форматируем столбец E как текст
        for cell in ws['E']:
            cell.number_format = '@'  # Текстовый формат

        wb.save(tmp_file.name)
        tmp_path = tmp_file.name
    
    try:
        print("🧪 Тестирование дедупликации компаний...")
        print(f"📊 Исходные данные:")
        for i, (company, inn, am) in enumerate(zip(test_data['D'], test_data['E'], test_data['AM'])):
            print(f"  {i+1}. {company} (ИНН: {inn}) - {am}")
        
        print(f"\n📋 Ожидается:")
        print(f"  - Уникальных компаний: 4")
        print(f"  - Дубликатов: 2")
        
        # Тестируем функцию дедупликации
        companies = read_companies_from_excel(tmp_path, "D", "E", "AM")
        
        print(f"\n✅ Результат:")
        print(f"  - Получено уникальных компаний: {len(companies)}")
        
        # Проверяем результат
        expected_companies = {
            ('ООО "Компания А"', '1234567890'),
            ('ООО "Компания Б"', '0987654321'),
            ('ООО "Компания В"', '1111111111'),
            ('ООО "Компания Г"', '2222222222')
        }
        
        actual_companies = {(company.lower(), inn) for company, inn, am in companies}
        expected_companies_lower = {(company.lower(), inn) for company, inn in expected_companies}
        
        if actual_companies == expected_companies_lower:
            print("  ✅ Дедупликация работает корректно!")
            print(f"\n📝 Обработанные компании:")
            for i, (company, inn, am) in enumerate(companies, 1):
                print(f"  {i}. {company} (ИНН: {inn}) - {am}")
            return True
        else:
            print("  ❌ Ошибка дедупликации!")
            print(f"  Ожидалось: {expected_companies_lower}")
            print(f"  Получено: {actual_companies}")
            return False
            
    finally:
        # Удаляем временный файл
        os.unlink(tmp_path)

def test_deduplication_without_inn():
    """Тест дедупликации без ИНН"""
    
    # Создаем тестовые данные без ИНН
    test_data = {
        'D': ['ООО "Компания А"', 'ООО "Компания Б"', 'ООО "КОМПАНИЯ А"', 'ООО "Компания В"'],  # Дубликат с разным регистром
        'E': ['', '', '', ''],  # Пустые ИНН
        'AM': ['Менеджер1', 'Менеджер2', 'Менеджер1', 'Менеджер3']
    }
    
    # Создаем временный Excel файл
    with tempfile.NamedTemporaryFile(suffix='.xlsx', delete=False) as tmp_file:
        # Используем openpyxl для создания файла напрямую
        from openpyxl import Workbook

        wb = Workbook()
        ws = wb.active

        # Добавляем заголовки (A, B, C, D, E, ...)
        ws.append(['A', 'B', 'C', 'D', 'E', 'AM'])

        # Добавляем данные (заполняем столбцы A, B, C пустыми значениями, D - компании, E - ИНН, AM - менеджеры)
        for company, inn, am in zip(test_data['D'], test_data['E'], test_data['AM']):
            ws.append(['', '', '', company, inn, am])

        wb.save(tmp_file.name)
        tmp_path = tmp_file.name
    
    try:
        print("\n🧪 Тестирование дедупликации без ИНН...")
        print(f"📊 Исходные данные:")
        for i, (company, inn, am) in enumerate(zip(test_data['D'], test_data['E'], test_data['AM'])):
            print(f"  {i+1}. {company} (ИНН: '{inn}') - {am}")

        companies = read_companies_from_excel(tmp_path, "D", "E", "AM")

        print(f"✅ Результат: получено {len(companies)} уникальных компаний")
        if companies:
            for i, (company, inn, am) in enumerate(companies, 1):
                print(f"  {i}. {company} (ИНН: '{inn}') - {am}")
        
        # Должно быть 3 уникальные компании (А, Б, В), дубликат "КОМПАНИЯ А" должен быть исключен
        if len(companies) == 3:
            print("  ✅ Дедупликация без ИНН работает корректно!")
            return True
        else:
            print("  ❌ Ошибка дедупликации без ИНН!")
            return False
            
    finally:
        os.unlink(tmp_path)

if __name__ == "__main__":
    print("🚀 Запуск тестов дедупликации...")
    
    try:
        test1_result = test_deduplication()
        test2_result = test_deduplication_without_inn()
        
        if test1_result and test2_result:
            print("\n🎉 Все тесты пройдены успешно!")
        else:
            print("\n❌ Некоторые тесты не пройдены!")
            
    except Exception as e:
        print(f"\n💥 Ошибка при выполнении тестов: {e}")
        import traceback
        traceback.print_exc()
