#!/usr/bin/env python3
"""
Тест для проверки проблемы с фильтрацией результатов поиска
"""

from leadgen_enhanced import _filter_by_company

def test_filtering_issue():
    """Тест проблемы с чрезмерной фильтрацией"""
    
    print("🧪 Тестирование проблемы с фильтрацией результатов...")
    
    # Симулируем ответ от GPT, который может содержать полезную информацию
    # но не всегда упоминает точное название компании
    gpt_response_lines = [
        "Компания основана в 2010 году и специализируется на разработке ИИ-решений",
        "Основные направления деятельности включают машинное обучение и анализ данных", 
        "В штате работает более 50 специалистов по data science",
        "Офис расположен в Москве, ул. Тверская, 15",
        "Генеральный директор - Иванов Иван Иванович",
        "Контактный телефон: +7(495)123-45-67",
        "Email: <EMAIL>",
        "Сайт: https://testcompany.ru",
        "ООО Тестовая Компания участвует в государственных закупках",
        "Компания имеет сертификаты ISO 9001 и ISO 27001",
        "Выручка за 2023 год составила 150 млн рублей",
        "Основные клиенты - крупные банки и телеком операторы"
    ]
    
    company_name = 'ООО "Тестовая Компания"'
    
    print(f"📊 Исходные данные от GPT ({len(gpt_response_lines)} строк):")
    for i, line in enumerate(gpt_response_lines, 1):
        print(f"  {i}. {line}")
    
    # Тестируем текущую фильтрацию
    filtered_results = _filter_by_company(gpt_response_lines, company_name)
    
    print(f"\n🔍 Результат текущей фильтрации ({len(filtered_results)} строк):")
    if filtered_results:
        for i, line in enumerate(filtered_results, 1):
            print(f"  {i}. {line}")
    else:
        print("  (Нет результатов)")
    
    # Анализируем проблему
    print(f"\n📈 Анализ:")
    print(f"  - Исходных строк: {len(gpt_response_lines)}")
    print(f"  - После фильтрации: {len(filtered_results)}")
    print(f"  - Потеряно информации: {len(gpt_response_lines) - len(filtered_results)} строк ({((len(gpt_response_lines) - len(filtered_results)) / len(gpt_response_lines) * 100):.1f}%)")
    
    # Показываем, какая информация потеряна
    lost_info = [line for line in gpt_response_lines if line not in filtered_results]
    if lost_info:
        print(f"\n❌ Потерянная полезная информация:")
        for i, line in enumerate(lost_info, 1):
            print(f"  {i}. {line}")
    
    # Проверяем, есть ли в потерянной информации полезные данные
    useful_keywords = ["директор", "телефон", "email", "сайт", "адрес", "офис", "выручка", "сотрудник", "специалист", "сертификат"]
    useful_lost = []
    for line in lost_info:
        if any(keyword in line.lower() for keyword in useful_keywords):
            useful_lost.append(line)
    
    if useful_lost:
        print(f"\n⚠️  Особенно важная потерянная информация:")
        for i, line in enumerate(useful_lost, 1):
            print(f"  {i}. {line}")
    
    return len(filtered_results), len(gpt_response_lines), len(useful_lost)

def test_alternative_filtering():
    """Тест альтернативного подхода к фильтрации"""
    
    print(f"\n🔧 Тестирование альтернативного подхода...")
    
    # Симулируем тот же ответ
    gpt_response_lines = [
        "Компания основана в 2010 году и специализируется на разработке ИИ-решений",
        "Основные направления деятельности включают машинное обучение и анализ данных", 
        "В штате работает более 50 специалистов по data science",
        "Офис расположен в Москве, ул. Тверская, 15",
        "Генеральный директор - Иванов Иван Иванович",
        "Контактный телефон: +7(495)123-45-67",
        "Email: <EMAIL>",
        "Сайт: https://testcompany.ru",
        "ООО Тестовая Компания участвует в государственных закупках",
        "Компания имеет сертификаты ISO 9001 и ISO 27001",
        "Выручка за 2023 год составила 150 млн рублей",
        "Основные клиенты - крупные банки и телеком операторы",
        "Информация о другой компании, не связанной с запросом",
        "Реклама услуг консалтинговой фирмы"
    ]
    
    # Альтернативный подход: минимальная фильтрация
    # Убираем только явно нерелевантные строки
    def alternative_filter(lines):
        # Ключевые слова, которые указывают на нерелевантную информацию
        irrelevant_keywords = [
            "реклама", "консалтинг", "другой компании", "не связанной",
            "advertisement", "sponsored", "unrelated"
        ]
        
        filtered = []
        for line in lines:
            line_lower = line.lower()
            # Пропускаем только явно нерелевантные строки
            if not any(keyword in line_lower for keyword in irrelevant_keywords):
                filtered.append(line)
        
        return filtered
    
    alternative_results = alternative_filter(gpt_response_lines)
    
    print(f"📊 Альтернативная фильтрация ({len(alternative_results)} строк):")
    for i, line in enumerate(alternative_results, 1):
        print(f"  {i}. {line}")
    
    print(f"\n📈 Сравнение подходов:")
    current_results = _filter_by_company(gpt_response_lines, 'ООО "Тестовая Компания"')
    print(f"  - Текущий подход: {len(current_results)} строк")
    print(f"  - Альтернативный подход: {len(alternative_results)} строк")
    print(f"  - Разница: +{len(alternative_results) - len(current_results)} строк полезной информации")
    
    return len(alternative_results)

if __name__ == "__main__":
    print("🚀 Диагностика проблемы с фильтрацией результатов поиска...")
    
    try:
        filtered_count, total_count, useful_lost_count = test_filtering_issue()
        alternative_count = test_alternative_filtering()
        
        print(f"\n📋 Итоговый диагноз:")
        if filtered_count < total_count * 0.3:  # Если остается менее 30% информации
            print(f"❌ ПРОБЛЕМА ОБНАРУЖЕНА: Слишком агрессивная фильтрация!")
            print(f"   - Теряется {total_count - filtered_count} из {total_count} строк информации")
            if useful_lost_count > 0:
                print(f"   - Включая {useful_lost_count} строк с важной информацией")
            print(f"   - Рекомендация: Ослабить фильтрацию или убрать её совсем")
        else:
            print(f"✅ Фильтрация работает нормально")
            
    except Exception as e:
        print(f"💥 Ошибка при диагностике: {e}")
        import traceback
        traceback.print_exc()
