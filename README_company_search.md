# Функция поиска сотрудников компании по должностям

## Описание

Функция `search_company_employees` выполняет последовательный поиск сотрудников компании через API Setka.ru с фильтрацией по ключевым словам в должностях и создает отчет в формате Word документа.

## Процесс работы функции

### 1. Поиск по переменной {company}
- Выполняется запрос к API Setka.ru для поиска компании по названию
- Используется endpoint: `GET https://api.setka.ru/v1/search/all?text={company}`

### 2. Проверка наличия Networks
- Из результатов поиска извлекаются Networks (корпоративные сети)
- Если Networks найдены, открывается первая из них

### 3. Получение списка members
- Выполняется запрос для получения участников сети
- Используется endpoint: `GET https://api.setka.ru/v2/networks/{network_id}/members/search`
- Собираются данные: имя, фамилия, должность

### 4. Фильтрация по ключевым словам
Фильтрация выполняется по следующим ключевым словам в названии должности:
- `директор`
- `ИТ` / `IT`
- `CIO`
- `CTO`
- `информационных технологий`
- `цифровых технологий`
- `цифровизации`
- `автоматизации`
- `разработки`

### 5. Создание отчета в Word документе
- Создается Word документ с отфильтрованным списком сотрудников
- Документ содержит таблицу с колонками: Фамилия, Имя, Должность
- Файл сохраняется в указанной директории

## Установка зависимостей

```bash
pip install -r requirements.txt
```

Основные зависимости:
- `aiohttp>=3.8.0` - для асинхронных HTTP запросов
- `python-docx>=0.8.11` - для создания Word документов

## Использование

### Асинхронный вызов

```python
import asyncio
from company_search import search_company_employees

async def main():
    # Ваш refresh токен от API Setka.ru
    refresh_token = "your_refresh_token_here"

    # Поиск сотрудников
    employees, new_refresh_token = await search_company_employees(
        company_name="Автоваз",
        refresh_token=refresh_token,
        output_dir="./reports",
        keywords=["директор", "ИТ", "CIO", "CTO"]  # опционально
    )

    # Результат: список кортежей (фамилия, имя, должность)
    for last_name, first_name, position in employees:
        print(f"{last_name} {first_name}: {position}")

    # Сохраните новый refresh токен для следующего использования
    print(f"Новый refresh токен: {new_refresh_token}")

# Запуск
asyncio.run(main())
```

### Синхронный вызов

```python
from company_search import search_company_employees_sync

# Поиск сотрудников (синхронно)
employees, new_refresh_token = search_company_employees_sync(
    company_name="Автоваз",
    refresh_token="your_refresh_token_here",
    output_dir="./reports"
)

print(f"Найдено {len(employees)} сотрудников")
print(f"Новый refresh токен: {new_refresh_token}")
```

### Командная строка

```bash
python company_search.py "Автоваз" "your_refresh_token_here"
```

## Параметры функции

### `search_company_employees()`

- **company_name** (str): Название компании для поиска
- **refresh_token** (str): Refresh токен для API Setka.ru
- **output_dir** (str, optional): Директория для сохранения отчета (по умолчанию: "./reports")
- **keywords** (List[str], optional): Список ключевых слов для фильтрации должностей

### Возвращаемое значение

Кортеж `Tuple[List[Tuple[str, str, str]], str]` в формате:
```python
(
    [  # Список сотрудников
        ("Фамилия", "Имя", "Должность"),
        ("Антонов", "Родион", "Руководитель проекта ИТ"),
        ("Петрова", "Анна", "Директор по информационным технологиям"),
        # ...
    ],
    "new_refresh_token_here"  # Обновленный refresh токен
)
```

## Структура отчета

Создаваемый Word документ содержит:

1. **Заголовок**: "Отчет по сотрудникам компании [Название]"
2. **Информация о поиске**:
   - Дата создания отчета
   - Критерии фильтрации
   - Количество найденных сотрудников
3. **Таблица сотрудников** с колонками:
   - Фамилия
   - Имя
   - Должность

## Обработка ошибок

Функция обрабатывает следующие типы ошибок:

- **Ошибки API**: Неверный токен, недоступность сервиса
- **Отсутствие данных**: Компания не найдена, Networks отсутствуют
- **Ошибки файловой системы**: Проблемы с созданием директорий или файлов

## Тестирование

Запуск тестов:

```bash
python test_company_search.py --test
```

Запуск примера:

```bash
python test_company_search.py --example
```

## Получение и использование токенов

Функция автоматически управляет токенами:

1. **Входной параметр**: refresh_token
2. **Автоматическое обновление**: функция сама получает access_token через API
3. **Возвращаемый токен**: новый refresh_token для следующего использования

### Процесс работы с токенами:
1. Функция принимает refresh_token
2. Автоматически обновляет токены через:
   ```
   POST https://api.setka.ru/v1/oauth/refresh
   ```
3. Использует полученный access_token для API запросов
4. Возвращает новый refresh_token

**Важно**: Всегда сохраняйте возвращаемый refresh_token для следующего использования!

Пример получения первоначального refresh_token см. в файле `API Setka.md`.

## Примеры результатов

### Успешный поиск
```
🔍 Начинаем поиск сотрудников компании: Автоваз
  1. Поиск компании...
  2. Поиск Networks...
  ✓ Найдено 1 Networks
  3. Получение участников Network 'Автоваз' (ID: e9791ecb-f7bb-470a-9b80-f36236f5cdfd)
     Общее количество участников: 9551
  4. Извлечение данных сотрудников...
     Извлечено 1250 сотрудников с полными данными
  5. Фильтрация по ключевым словам...
     После фильтрации: 15 сотрудников
  6. Создание отчета...
  ✓ Отчет сохранен: ./reports/employees_report_Автоваз.docx

✅ Поиск завершен! Найдено 15 сотрудников:
  - Антонов Родион: Руководитель проекта ИТ
  - Петрова Анна: Директор по информационным технологиям
  - Козлова Мария: CTO
  ...
```

### Компания без Networks
```
🔍 Начинаем поиск сотрудников компании: Малая компания
  1. Поиск компании...
  2. Поиск Networks...
  ❌ Networks не найдены для данной компании
```

## Ограничения

- Функция работает только с первой найденной Network компании
- Максимальное количество участников за один запрос: 1000
- Требуется действующий токен доступа к API Setka.ru
- Фильтрация выполняется только по названию должности
