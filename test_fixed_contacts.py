"""test_fixed_contacts.py
Тест исправленного функционала контактов
"""

import os
import sys

# Предотвращаем запуск CLI
sys.argv = ['test']

def test_contact_parsing():
    """Тест парсинга контактов из текста"""
    print("🧪 ТЕСТ ПАРСИНГА КОНТАКТОВ")
    print("=" * 50)
    
    try:
        from leadgen_enhanced import parse_contacts_from_text
        
        # Тестовый текст в новом формате
        test_text = """
        CONTACT_START
        FIRST_NAME: Александр
        LAST_NAME: Иванов
        POSITION: CTO
        CONFIDENCE: high
        CONTACT_END
        
        CONTACT_START
        FIRST_NAME: А****
        LAST_NAME: П****
        POSITION: CIO
        CONFIDENCE: medium
        CONTACT_END
        """
        
        contacts = parse_contacts_from_text(test_text)
        
        print(f"📊 Найдено контактов: {len(contacts)}")
        
        for i, contact in enumerate(contacts, 1):
            print(f"  {i}. {contact.get('last_name', '')} {contact.get('first_name', '')} - {contact.get('position', '')}")
            print(f"     Уверенность: {contact.get('confidence', 'unknown')}")
        
        return len(contacts) == 2
        
    except Exception as e:
        print(f"❌ Ошибка теста: {e}")
        return False

def test_email_generation():
    """Тест генерации email вариантов"""
    print("\n🧪 ТЕСТ ГЕНЕРАЦИИ EMAIL")
    print("=" * 50)
    
    try:
        from leadgen_enhanced import generate_email_variants
        
        first_name = "Александр"
        last_name = "Иванов"
        domain = "company.ru"
        patterns = ["name.surname@domain", "n.surname@domain"]
        
        emails = generate_email_variants(first_name, last_name, domain, patterns)
        
        print(f"👤 {first_name} {last_name}")
        print(f"🌐 Домен: {domain}")
        print(f"📧 Сгенерированные email:")
        
        for i, email in enumerate(emails, 1):
            print(f"  {i}. {email}")
        
        return len(emails) > 0
        
    except Exception as e:
        print(f"❌ Ошибка теста: {e}")
        return False

def test_refresh_token_file():
    """Тест сохранения refresh токена"""
    print("\n🧪 ТЕСТ СОХРАНЕНИЯ REFRESH ТОКЕНА")
    print("=" * 50)
    
    try:
        token_file = "/tmp/setka_refresh_token.txt"
        test_token = "test_refresh_token_12345"
        
        # Записываем тестовый токен
        with open(token_file, 'w') as f:
            f.write(test_token)
        
        # Читаем обратно
        with open(token_file, 'r') as f:
            saved_token = f.read().strip()
        
        print(f"📁 Файл токена: {token_file}")
        print(f"💾 Сохраненный токен: {saved_token}")
        
        # Удаляем тестовый файл
        os.remove(token_file)
        
        return saved_token == test_token
        
    except Exception as e:
        print(f"❌ Ошибка теста: {e}")
        return False

def check_imports():
    """Проверка импортов"""
    print("\n🧪 ПРОВЕРКА ИМПОРТОВ")
    print("=" * 50)
    
    try:
        from leadgen_enhanced import (
            parse_contacts_from_text,
            generate_email_variants,
            validate_email,
            search_email_domain_pattern,
            resolve_partial_name,
            get_contacts_from_setka,
            process_contacts_with_emails
        )
        
        print("✅ Все функции импортированы успешно")
        return True
        
    except Exception as e:
        print(f"❌ Ошибка импорта: {e}")
        return False

def main():
    """Главная функция тестирования"""
    print("🧪 ТЕСТИРОВАНИЕ ИСПРАВЛЕННОГО ФУНКЦИОНАЛА КОНТАКТОВ")
    print("=" * 80)
    
    tests = [
        ("Проверка импортов", check_imports()),
        ("Парсинг контактов", test_contact_parsing()),
        ("Генерация email", test_email_generation()),
        ("Сохранение refresh токена", test_refresh_token_file())
    ]
    
    print("\n📊 РЕЗУЛЬТАТЫ ТЕСТИРОВАНИЯ:")
    print("=" * 80)
    
    passed = 0
    for test_name, result in tests:
        status = "✅ ПРОЙДЕН" if result else "❌ НЕ ПРОЙДЕН"
        print(f"  {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n🎯 ИТОГО: {passed}/{len(tests)} тестов пройдено")
    
    if passed == len(tests):
        print("🎉 ВСЕ ТЕСТЫ ПРОЙДЕНЫ!")
        print("\n✅ ИСПРАВЛЕНИЯ ПРИМЕНЕНЫ УСПЕШНО:")
        print("  • Убран JSON формат (не поддерживается с web_search)")
        print("  • Добавлен парсер структурированного текста")
        print("  • Refresh токен сохраняется в файл /tmp/setka_refresh_token.txt")
        print("  • Полный токен выводится в консоль")
    else:
        print("⚠️  Есть проблемы, требующие внимания.")

if __name__ == "__main__":
    main()
