#!/usr/bin/env python3
"""
Комплексный тест для проверки чистого поиска без ИНН
"""

import pandas as pd
import tempfile
import os
from leadgen_enhanced import _scan_tenders, _extract_company_name_from_quotes, _filter_by_company

def test_clean_search_integration():
    """Комплексный тест поиска с чистыми названиями компаний"""
    
    print("🧪 Комплексное тестирование чистого поиска...")
    
    # Тестовые данные с различными форматами названий
    test_companies = [
        'ООО "Тестовая Компания"',
        'АО «Рога и Копыта»',
        'ИП "Иванов И.И."',
        'Компания без кавычек',
        'ООО "Компания" с дополнительным текстом',
    ]
    
    # Создаем тестовые данные закупок
    test_data = []
    
    # Создаем 30 столбцов (A-AD), столбец AB (27) содержит названия компаний
    for i in range(len(test_companies) + 2):  # +2 для заголовка и пустой строки
        row = [''] * 30  # 30 пустых столбцов
        if i == 0:
            # Заголовки
            row[27] = "Заказчик"  # Столбец AB
        elif i == 1:
            # Пустая строка
            pass
        else:
            # Данные компаний - используем ЧИСТЫЕ названия (как они должны быть в закупках)
            company_idx = i - 2
            if company_idx < len(test_companies):
                clean_name = _extract_company_name_from_quotes(test_companies[company_idx])
                row[27] = clean_name
                
                # Добавляем данные в другие столбцы
                row[2] = f"Закупка {company_idx + 1}"  # Столбец C - название закупки
                row[3] = f"{1000000 * (company_idx + 1)}"  # Столбец D - цена
                row[11] = f"2024-01-{(company_idx + 1):02d}"  # Столбец L - дата
        
        test_data.append(row)
    
    # Создаем временный Excel файл
    with tempfile.NamedTemporaryFile(suffix='.xlsx', delete=False) as tmp_file:
        df = pd.DataFrame(test_data)
        df.to_excel(tmp_file.name, index=False, header=False, engine='openpyxl')
        tmp_path = tmp_file.name
    
    # Создаем временную директорию для тендеров
    with tempfile.TemporaryDirectory() as tmp_dir:
        # Перемещаем файл в директорию
        final_path = os.path.join(tmp_dir, "tenders.xlsx")
        os.rename(tmp_path, final_path)
        
        try:
            print("\n📋 Тестирование поиска по закупкам с различными форматами названий...")
            
            all_passed = True
            
            for i, company in enumerate(test_companies, 1):
                print(f"\n🔍 Тест {i}: Поиск '{company}'")
                
                # Извлекаем чистое название
                clean_name = _extract_company_name_from_quotes(company)
                print(f"   Чистое название: '{clean_name}'")
                
                # Тестируем поиск по закупкам
                result = _scan_tenders(tmp_dir, company)
                
                if "Не найдено" in result:
                    print(f"   ❌ Не найдено в закупках")
                    all_passed = False
                else:
                    print(f"   ✅ Найдено в закупках: {result.split('—')[0].strip()}")
                
                # Тестируем фильтрацию
                test_lines = [
                    f"Какая-то информация о {clean_name} и её деятельности",
                    f"Другая компания не связанная с {clean_name.upper()}",
                    "Совершенно не связанная информация",
                    f"Еще один факт про {clean_name.lower()} с другим регистром"
                ]
                
                filtered = _filter_by_company(test_lines, company)
                expected_count = 3  # Должно найти 3 строки с упоминанием компании
                
                if len(filtered) == expected_count:
                    print(f"   ✅ Фильтрация работает: найдено {len(filtered)} упоминаний")
                else:
                    print(f"   ❌ Ошибка фильтрации: ожидалось {expected_count}, найдено {len(filtered)}")
                    all_passed = False
            
            # Тест с компанией, которой нет в закупках
            print(f"\n🔍 Тест отсутствующей компании")
            missing_result = _scan_tenders(tmp_dir, 'ООО "Несуществующая Компания"')
            if "Не найдено" in missing_result:
                print(f"   ✅ Правильно: несуществующая компания не найдена")
            else:
                print(f"   ❌ Ошибка: найдена несуществующая компания")
                all_passed = False
            
            return all_passed
            
        except Exception as e:
            print(f"💥 Ошибка при выполнении теста: {e}")
            return False

def test_no_inn_in_search():
    """Тест того, что ИНН не попадает в поисковые запросы"""
    
    print("\n🚫 Тестирование отсутствия ИНН в поисковых запросах...")
    
    from leadgen_enhanced import _build_user_prompt
    
    test_cases = [
        ('ООО "Тестовая Компания"', '1234567890'),
        ('АО «Рога и Копыта»', '0987654321'),
        ('Компания без кавычек', '1111111111'),
    ]
    
    test_instruction = "Найди информацию о компании {company} в интернете"
    
    all_passed = True
    
    for company, inn in test_cases:
        result = _build_user_prompt(company, inn, test_instruction)
        
        # Проверяем, что ИНН НЕ попал в результат
        if inn in result:
            print(f"❌ ИНН {inn} найден в запросе: '{result}'")
            all_passed = False
        else:
            clean_name = _extract_company_name_from_quotes(company)
            expected = f"Найди информацию о компании {clean_name} в интернете"
            if result == expected:
                print(f"✅ ИНН корректно исключен для '{company}'")
                print(f"   Результат: '{result}'")
            else:
                print(f"❌ Неожиданный результат для '{company}'")
                print(f"   Ожидалось: '{expected}'")
                print(f"   Получено: '{result}'")
                all_passed = False
    
    return all_passed

if __name__ == "__main__":
    print("🚀 Запуск комплексного теста чистого поиска...")
    
    try:
        test1_result = test_clean_search_integration()
        test2_result = test_no_inn_in_search()
        
        if test1_result and test2_result:
            print("\n🎉 Все комплексные тесты пройдены успешно!")
            print("✅ Поиск работает с чистыми названиями компаний")
            print("✅ ИНН корректно исключен из поисковых запросов")
            print("✅ Извлечение из кавычек работает правильно")
        else:
            print("\n❌ Некоторые комплексные тесты не пройдены!")
            
    except Exception as e:
        print(f"\n💥 Ошибка при выполнении тестов: {e}")
        import traceback
        traceback.print_exc()
