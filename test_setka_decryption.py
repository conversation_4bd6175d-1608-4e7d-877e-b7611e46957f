"""test_setka_decryption.py
Тест расшифровки зашифрованных контактов из Setka API
"""

import os
import sys
import asyncio

# Устанавливаем API ключи для тестирования
openai_api_key = os.getenv("OPENAI_API_KEY")

if not openai_api_key:
    print("❌ Установите OPENAI_API_KEY для тестирования")
    sys.exit(1)

# Предотвращаем запуск CLI
sys.argv = ['test']

async def test_decrypt_setka_contacts():
    """Тест расшифровки зашифрованных контактов"""
    print("\n🧪 ТЕСТ РАСШИФРОВКИ ЗАШИФРОВАННЫХ КОНТАКТОВ")
    print("=" * 60)
    
    try:
        from leadgen_enhanced import decrypt_setka_contacts
        import openai
        
        client = openai.AsyncClient(api_key=openai_api_key)
        
        # Тестовые зашифрованные контакты
        encrypted_contacts = [
            ("И****", "А****", "CTO"),  # Зашифрованные имя и фамилия
            ("Петров", "А****", "Директор ИТ"),  # Зашифрованное имя
            ("С****", "Мария", "Руководитель отдела"),  # Зашифрованная фамилия
            ("Сидоров", "Иван", "Менеджер"),  # Обычный контакт
        ]
        
        company_name = "Сбербанк"
        
        print(f"🏢 Компания: {company_name}")
        print(f"👥 Зашифрованных контактов: {len(encrypted_contacts)}")
        
        for i, (last_name, first_name, position) in enumerate(encrypted_contacts, 1):
            print(f"  {i}. {last_name} {first_name} - {position}")
        
        print(f"\n🔍 Запуск расшифровки...")
        decrypted_contacts = await decrypt_setka_contacts(client, company_name, encrypted_contacts)
        
        print(f"\n📊 Результаты расшифровки:")
        print(f"  Входных контактов: {len(encrypted_contacts)}")
        print(f"  Расшифрованных контактов: {len(decrypted_contacts)}")
        
        print(f"\n✅ Расшифрованные контакты:")
        for i, contact in enumerate(decrypted_contacts, 1):
            first_name = contact.get("first_name", "")
            last_name = contact.get("last_name", "")
            position = contact.get("position", "")
            confidence = contact.get("confidence", "")
            source = contact.get("source", "")
            print(f"  {i}. {last_name} {first_name} - {position}")
            print(f"     Уверенность: {confidence}, Источник: {source}")
        
        # Проверяем результаты
        has_results = len(decrypted_contacts) > 0
        has_normal_contact = any(c.get("source") == "setka_api" for c in decrypted_contacts)
        has_decrypted_contact = any(c.get("source") == "setka_api_decrypted" for c in decrypted_contacts)
        no_encrypted_in_results = all("*" not in c.get("first_name", "") and "*" not in c.get("last_name", "") for c in decrypted_contacts)
        
        print(f"\n📋 Проверки:")
        print(f"  ✅ Есть результаты: {has_results}")
        print(f"  ✅ Есть обычные контакты: {has_normal_contact}")
        print(f"  ✅ Есть расшифрованные контакты: {has_decrypted_contact}")
        print(f"  ✅ Нет зашифрованных данных в результатах: {no_encrypted_in_results}")
        
        return has_results and no_encrypted_in_results
        
    except Exception as e:
        print(f"❌ Ошибка теста: {e}")
        return False

async def test_encrypted_contact_filtering():
    """Тест фильтрации зашифрованных контактов"""
    print("\n🧪 ТЕСТ ФИЛЬТРАЦИИ ЗАШИФРОВАННЫХ КОНТАКТОВ")
    print("=" * 60)
    
    try:
        from leadgen_enhanced import decrypt_setka_contacts
        import openai
        
        client = openai.AsyncClient(api_key=openai_api_key)
        
        # Только зашифрованные контакты (которые не удастся расшифровать)
        encrypted_only_contacts = [
            ("А****", "Б****", "Неизвестная должность"),  # Сложно расшифровать
            ("Х****", "У****", "Секретная позиция"),  # Сложно расшифровать
        ]
        
        company_name = "Неизвестная Компания"
        
        print(f"🏢 Компания: {company_name}")
        print(f"👥 Только зашифрованных контактов: {len(encrypted_only_contacts)}")
        
        for i, (last_name, first_name, position) in enumerate(encrypted_only_contacts, 1):
            print(f"  {i}. {last_name} {first_name} - {position}")
        
        print(f"\n🔍 Запуск расшифровки (ожидаем неудачу)...")
        decrypted_contacts = await decrypt_setka_contacts(client, company_name, encrypted_only_contacts)
        
        print(f"\n📊 Результаты фильтрации:")
        print(f"  Входных контактов: {len(encrypted_only_contacts)}")
        print(f"  Расшифрованных контактов: {len(decrypted_contacts)}")
        
        # Проверяем, что нерасшифрованные контакты отфильтрованы
        filtered_correctly = len(decrypted_contacts) == 0
        
        print(f"\n✅ Проверки:")
        print(f"  ✅ Нерасшифрованные контакты отфильтрованы: {filtered_correctly}")
        
        return filtered_correctly
        
    except Exception as e:
        print(f"❌ Ошибка теста: {e}")
        return False

async def test_mixed_contacts():
    """Тест смешанных контактов (обычные + зашифрованные)"""
    print("\n🧪 ТЕСТ СМЕШАННЫХ КОНТАКТОВ")
    print("=" * 60)
    
    try:
        from leadgen_enhanced import decrypt_setka_contacts
        import openai
        
        client = openai.AsyncClient(api_key=openai_api_key)
        
        # Смешанные контакты
        mixed_contacts = [
            ("Иванов", "Петр", "Директор"),  # Обычный
            ("С****", "А****", "CTO"),  # Зашифрованный
            ("Сидорова", "Мария", "Менеджер"),  # Обычный
            ("К****", "Е****", "Руководитель ИТ"),  # Зашифрованный
        ]
        
        company_name = "Сбербанк"
        
        print(f"🏢 Компания: {company_name}")
        print(f"👥 Смешанных контактов: {len(mixed_contacts)}")
        
        for i, (last_name, first_name, position) in enumerate(mixed_contacts, 1):
            is_encrypted = "*" in first_name or "*" in last_name
            status = "🔒 зашифрован" if is_encrypted else "🔓 обычный"
            print(f"  {i}. {last_name} {first_name} - {position} ({status})")
        
        print(f"\n🔍 Запуск обработки смешанных контактов...")
        decrypted_contacts = await decrypt_setka_contacts(client, company_name, mixed_contacts)
        
        print(f"\n📊 Результаты обработки:")
        print(f"  Входных контактов: {len(mixed_contacts)}")
        print(f"  Обработанных контактов: {len(decrypted_contacts)}")
        
        print(f"\n✅ Обработанные контакты:")
        normal_count = 0
        decrypted_count = 0
        
        for i, contact in enumerate(decrypted_contacts, 1):
            first_name = contact.get("first_name", "")
            last_name = contact.get("last_name", "")
            position = contact.get("position", "")
            source = contact.get("source", "")
            
            if source == "setka_api":
                normal_count += 1
                status = "🔓 обычный"
            elif source == "setka_api_decrypted":
                decrypted_count += 1
                status = "🔓 расшифрован"
            else:
                status = "❓ неизвестно"
                
            print(f"  {i}. {last_name} {first_name} - {position} ({status})")
        
        # Проверяем результаты
        has_normal = normal_count >= 2  # Ожидаем минимум 2 обычных контакта
        has_decrypted = decrypted_count >= 0  # Может быть 0 если расшифровка не удалась
        no_encrypted_in_results = all("*" not in c.get("first_name", "") and "*" not in c.get("last_name", "") for c in decrypted_contacts)
        
        print(f"\n📋 Проверки:")
        print(f"  ✅ Обычные контакты сохранены: {has_normal} ({normal_count} из 2)")
        print(f"  ✅ Есть попытки расшифровки: {has_decrypted} ({decrypted_count} расшифровано)")
        print(f"  ✅ Нет зашифрованных данных в результатах: {no_encrypted_in_results}")
        
        return has_normal and no_encrypted_in_results
        
    except Exception as e:
        print(f"❌ Ошибка теста: {e}")
        return False

def test_imports():
    """Тест импортов обновленных функций"""
    print("\n🧪 ТЕСТ ИМПОРТОВ ОБНОВЛЕННЫХ ФУНКЦИЙ")
    print("=" * 60)
    
    try:
        from leadgen_enhanced import (
            decrypt_setka_contacts,
            get_contacts_from_setka,
            resolve_partial_name
        )
        
        print("✅ Все обновленные функции импортированы успешно:")
        print("  • decrypt_setka_contacts (новая функция расшифровки)")
        print("  • get_contacts_from_setka (обновленная функция)")
        print("  • resolve_partial_name (функция расшифровки имен)")
        
        return True
        
    except Exception as e:
        print(f"❌ Ошибка импорта: {e}")
        return False

async def main():
    """Главная функция тестирования"""
    print("🧪 ТЕСТИРОВАНИЕ РАСШИФРОВКИ SETKA КОНТАКТОВ")
    print("=" * 80)
    
    # Сначала тестируем импорты
    imports_ok = test_imports()
    
    if not imports_ok:
        print("❌ Проблемы с импортами, остальные тесты пропущены")
        return
    
    # Асинхронные тесты
    async_tests = [
        ("Расшифровка зашифрованных контактов", test_decrypt_setka_contacts()),
        ("Фильтрация нерасшифрованных контактов", test_encrypted_contact_filtering()),
        ("Обработка смешанных контактов", test_mixed_contacts())
    ]
    
    print("\n📊 РЕЗУЛЬТАТЫ ТЕСТИРОВАНИЯ:")
    print("=" * 80)
    
    passed = 1 if imports_ok else 0  # Импорты уже протестированы
    print(f"  Импорты обновленных функций: {'✅ ПРОЙДЕН' if imports_ok else '❌ НЕ ПРОЙДЕН'}")
    
    # Асинхронные тесты
    for test_name, test_coro in async_tests:
        try:
            result = await test_coro
            status = "✅ ПРОЙДЕН" if result else "❌ НЕ ПРОЙДЕН"
            print(f"  {test_name}: {status}")
            if result:
                passed += 1
        except Exception as e:
            print(f"  {test_name}: ❌ НЕ ПРОЙДЕН ({e})")
    
    total_tests = len(async_tests) + 1
    print(f"\n🎯 ИТОГО: {passed}/{total_tests} тестов пройдено")
    
    if passed == total_tests:
        print("🎉 ВСЕ ТЕСТЫ ПРОЙДЕНЫ!")
        print("\n✅ РАСШИФРОВКА SETKA КОНТАКТОВ:")
        print("  • Зашифрованные контакты автоматически обнаруживаются")
        print("  • GPT пытается расшифровать по компании и должности")
        print("  • Успешно расшифрованные контакты добавляются в список")
        print("  • Нерасшифрованные контакты отфильтровываются")
        print("  • Обычные контакты проходят без изменений")
        print("  • Строгая логика: только качественные данные в результатах")
    elif passed > 2:
        print("⚠️  Частично работает - проверьте логику расшифровки")
    else:
        print("❌ Есть серьезные проблемы с расшифровкой контактов")

if __name__ == "__main__":
    asyncio.run(main())
