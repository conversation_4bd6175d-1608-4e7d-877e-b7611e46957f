"""test_gpt_email_generation.py
Тест новой логики генерации email через GPT
"""

import os
import sys
import asyncio

# Устанавливаем API ключ для тестирования
api_key = os.getenv("OPENAI_API_KEY")
if not api_key:
    print("❌ Установите OPENAI_API_KEY для тестирования")
    sys.exit(1)

# Предотвращаем запуск CLI
sys.argv = ['test']

async def test_company_email_search():
    """Тест поиска информации о корпоративной почте компании"""
    print("🧪 ТЕСТ ПОИСКА ИНФОРМАЦИИ О КОРПОРАТИВНОЙ ПОЧТЕ")
    print("=" * 60)
    
    try:
        from leadgen_enhanced import search_company_email_info
        import openai
        
        client = openai.AsyncClient(api_key=api_key)
        
        company = "Сбербанк"
        print(f"🏢 Компания: {company}")
        
        result = await search_company_email_info(client, company)
        
        print(f"📧 Результат поиска информации о почте:")
        print(f"{'='*50}")
        print(result)
        print(f"{'='*50}")
        
        # Проверяем, что результат содержит полезную информацию
        has_domain = "@" in result and "." in result
        has_examples = "пример" in result.lower() or "example" in result.lower()
        
        print(f"✅ Содержит домен: {has_domain}")
        print(f"✅ Содержит примеры: {has_examples}")
        
        return has_domain or has_examples
        
    except Exception as e:
        print(f"❌ Ошибка теста: {e}")
        return False

async def test_personal_email_generation():
    """Тест генерации персонального email"""
    print("\n🧪 ТЕСТ ГЕНЕРАЦИИ ПЕРСОНАЛЬНОГО EMAIL")
    print("=" * 60)
    
    try:
        from leadgen_enhanced import generate_personal_email
        import openai
        
        client = openai.AsyncClient(api_key=api_key)
        
        # Тестовые данные
        first_name = "Александр"
        last_name = "Иванов"
        company_email_info = """ДОМЕН: sberbank.ru
ПРИМЕРЫ:
- <EMAIL>
- <EMAIL>
- <EMAIL>"""
        
        print(f"👤 Имя: {first_name}")
        print(f"👤 Фамилия: {last_name}")
        print(f"📧 Информация о почте компании:")
        print(company_email_info)
        
        result = await generate_personal_email(client, first_name, last_name, company_email_info)
        
        print(f"\n🎯 Сгенерированный email: {result}")
        
        # Проверяем результат
        is_valid_format = "@" in result and "." in result
        has_english_name = any(char in result for char in "aleksandr")
        has_correct_domain = "sberbank.ru" in result
        
        print(f"✅ Валидный формат: {is_valid_format}")
        print(f"✅ Английское имя: {has_english_name}")
        print(f"✅ Правильный домен: {has_correct_domain}")
        
        return is_valid_format and has_english_name
        
    except Exception as e:
        print(f"❌ Ошибка теста: {e}")
        return False

async def test_full_contact_processing():
    """Тест полной обработки контакта"""
    print("\n🧪 ТЕСТ ПОЛНОЙ ОБРАБОТКИ КОНТАКТА")
    print("=" * 60)
    
    try:
        from leadgen_enhanced import process_single_contact_with_email, search_company_email_info
        import openai
        
        client = openai.AsyncClient(api_key=api_key)
        
        # Тестовые данные
        company_name = "Сбербанк"
        contact = {
            "first_name": "Александр",
            "last_name": "Иванов",
            "position": "CTO",
            "confidence": "high",
            "source": "test"
        }
        
        print(f"🏢 Компания: {company_name}")
        print(f"👤 Контакт: {contact['last_name']} {contact['first_name']} - {contact['position']}")
        
        # Сначала получаем информацию о почте компании
        print("\n📧 Получение информации о корпоративной почте...")
        company_email_info = await search_company_email_info(client, company_name)
        
        # Затем обрабатываем контакт
        print("\n👤 Обработка контакта...")
        result = await process_single_contact_with_email(client, company_name, contact, company_email_info)
        
        print(f"\n🎯 Итоговый результат:")
        print(result)
        
        # Проверяем результат
        has_name = contact['first_name'] in result and contact['last_name'] in result
        has_position = contact['position'] in result
        has_email = "@" in result and "." in result
        
        print(f"\n✅ Содержит имя: {has_name}")
        print(f"✅ Содержит должность: {has_position}")
        print(f"✅ Содержит email: {has_email}")
        
        return has_name and has_position
        
    except Exception as e:
        print(f"❌ Ошибка теста: {e}")
        return False

def test_imports():
    """Тест импортов новых функций"""
    print("\n🧪 ТЕСТ ИМПОРТОВ НОВЫХ ФУНКЦИЙ")
    print("=" * 60)
    
    try:
        from leadgen_enhanced import (
            search_company_email_info,
            generate_personal_email,
            process_single_contact_with_email,
            transliterate_to_english
        )
        
        print("✅ Все новые функции импортированы успешно")
        
        # Тест транслитерации
        test_name = "Александр"
        transliterated = transliterate_to_english(test_name)
        print(f"✅ Транслитерация работает: {test_name} → {transliterated}")
        
        return True
        
    except Exception as e:
        print(f"❌ Ошибка импорта: {e}")
        return False

async def main():
    """Главная функция тестирования"""
    print("🧪 ТЕСТИРОВАНИЕ НОВОЙ ЛОГИКИ ГЕНЕРАЦИИ EMAIL ЧЕРЕЗ GPT")
    print("=" * 80)
    
    # Сначала тестируем импорты
    imports_ok = test_imports()
    
    if not imports_ok:
        print("❌ Проблемы с импортами, остальные тесты пропущены")
        return
    
    # Затем API тесты
    tests = [
        ("Поиск информации о корпоративной почте", test_company_email_search()),
        ("Генерация персонального email", test_personal_email_generation()),
        ("Полная обработка контакта", test_full_contact_processing())
    ]
    
    results = []
    for test_name, test_coro in tests:
        try:
            result = await test_coro
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ Ошибка в тесте '{test_name}': {e}")
            results.append((test_name, False))
    
    print("\n📊 РЕЗУЛЬТАТЫ ТЕСТИРОВАНИЯ:")
    print("=" * 80)
    
    passed = 1 if imports_ok else 0  # Импорты уже протестированы
    print(f"  Импорты новых функций: {'✅ ПРОЙДЕН' if imports_ok else '❌ НЕ ПРОЙДЕН'}")
    
    for test_name, result in results:
        status = "✅ ПРОЙДЕН" if result else "❌ НЕ ПРОЙДЕН"
        print(f"  {test_name}: {status}")
        if result:
            passed += 1
    
    total_tests = len(results) + 1
    print(f"\n🎯 ИТОГО: {passed}/{total_tests} тестов пройдено")
    
    if passed == total_tests:
        print("🎉 ВСЕ ТЕСТЫ ПРОЙДЕНЫ!")
        print("\n✅ НОВАЯ ЛОГИКА РАБОТАЕТ:")
        print("  • GPT ищет домен и примеры корпоративных почт")
        print("  • Для каждого человека GPT генерирует персональный email")
        print("  • Используется контекст найденной информации о компании")
        print("  • Email генерируются на английском с транслитерацией")
    elif passed > 1:
        print("⚠️  Частично работает - проверьте OPENAI_API_KEY")
    else:
        print("❌ Есть серьезные проблемы")

if __name__ == "__main__":
    asyncio.run(main())
