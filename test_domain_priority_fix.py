"""test_domain_priority_fix.py
Тест исправления приоритетов доменов: закупки > GPT > Hunter.io
"""

import os
import sys
import asyncio
import pandas as pd
import tempfile
import shutil

# Устанавливаем API ключи для тестирования
openai_api_key = os.getenv("OPENAI_API_KEY")

if not openai_api_key:
    print("❌ Установите OPENAI_API_KEY для тестирования")
    sys.exit(1)

# Предотвращаем запуск CLI
sys.argv = ['test']

async def test_procurement_domain_priority():
    """Тест приоритета домена из закупок над GPT доменом"""
    print("\n🧪 ТЕСТ ПРИОРИТЕТА ДОМЕНА ИЗ ЗАКУПОК")
    print("=" * 60)
    
    try:
        from leadgen_enhanced import find_email_pattern_for_contacts
        import openai
        
        client = openai.AsyncClient(api_key=openai_api_key)
        
        # Создаем тестовую директорию с закупками
        tenders_dir = tempfile.mkdtemp(prefix="test_domain_priority_")
        
        # Создаем Excel файл с служебными email (без персональных)
        data = []
        header_row = [''] * 30
        header_row[26] = 'Заказчик'  # AA
        data.append(header_row)
        data.append([''] * 30)  # Пустая строка
        
        test_row = [''] * 30
        test_row[21] = 'Контакт: <EMAIL>, <EMAIL>'  # V
        test_row[26] = 'Белгородская область'  # AA
        data.append(test_row)
        
        df = pd.DataFrame(data)
        excel_path = f"{tenders_dir}/test.xlsx"
        df.to_excel(excel_path, index=False, header=False)
        
        # Тестовые контакты
        contacts = [
            {"first_name": "Иван", "last_name": "Петров", "position": "Менеджер"},
            {"first_name": "Мария", "last_name": "Сидорова", "position": "Директор"}
        ]
        
        # Компания, которая может дать GPT другой домен
        company_name = "Белгородская область"
        
        print(f"🏢 Компания: {company_name}")
        print(f"📁 Директория: {tenders_dir}")
        print(f"👥 Контактов: {len(contacts)}")
        print(f"📧 В закупках: служебные email с доменом belregion.ru")
        print(f"🎯 Ожидаем: приоритет домена из закупок")
        
        domain, pattern, example = await find_email_pattern_for_contacts(
            client, company_name, contacts, None, tenders_dir
        )
        
        print(f"\n📊 Результат:")
        print(f"  Домен: {domain}")
        print(f"  Паттерн: {pattern}")
        print(f"  Пример: {example}")
        
        # Проверяем результат
        has_procurement_domain = "belregion.ru" in domain
        not_gpt_domain = "kurganobl.ru" not in domain  # GPT может найти другой домен
        
        print(f"\n✅ Домен из закупок используется: {has_procurement_domain}")
        print(f"✅ GPT домен не перезаписал: {not_gpt_domain}")
        
        # Удаляем тестовую директорию
        shutil.rmtree(tenders_dir)
        print(f"🗑️ Удалена тестовая директория")
        
        return has_procurement_domain and not_gpt_domain
        
    except Exception as e:
        print(f"❌ Ошибка теста: {e}")
        return False

async def test_gpt_domain_fallback():
    """Тест использования GPT домена при отсутствии домена из закупок"""
    print("\n🧪 ТЕСТ FALLBACK НА GPT ДОМЕН")
    print("=" * 60)
    
    try:
        from leadgen_enhanced import find_email_pattern_for_contacts
        import openai
        
        client = openai.AsyncClient(api_key=openai_api_key)
        
        # Тестовые контакты
        contacts = [
            {"first_name": "Алексей", "last_name": "Козлов", "position": "Менеджер"},
            {"first_name": "Елена", "last_name": "Волкова", "position": "Директор"}
        ]
        
        # Известная компания для GPT поиска
        company_name = "Сбербанк"
        
        print(f"🏢 Компания: {company_name}")
        print(f"👥 Контактов: {len(contacts)}")
        print(f"📁 Директория закупок: отсутствует")
        print(f"🎯 Ожидаем: использование GPT домена")
        
        domain, pattern, example = await find_email_pattern_for_contacts(
            client, company_name, contacts, None, None
        )
        
        print(f"\n📊 Результат:")
        print(f"  Домен: {domain}")
        print(f"  Паттерн: {pattern}")
        print(f"  Пример: {example}")
        
        # Проверяем результат
        has_sberbank_domain = "sberbank.ru" in domain
        has_domain = bool(domain)
        
        print(f"\n✅ GPT домен найден: {has_domain}")
        print(f"✅ Правильный домен Сбербанка: {has_sberbank_domain}")
        
        return has_domain and has_sberbank_domain
        
    except Exception as e:
        print(f"❌ Ошибка теста: {e}")
        return False

async def test_hunter_domain_usage():
    """Тест использования Hunter.io домена при отсутствии других"""
    print("\n🧪 ТЕСТ ИСПОЛЬЗОВАНИЯ HUNTER.IO ДОМЕНА")
    print("=" * 60)
    
    try:
        from leadgen_enhanced import find_email_pattern_for_contacts
        import openai
        
        client = openai.AsyncClient(api_key=openai_api_key)
        
        # Тестовые контакты
        contacts = [
            {"first_name": "Дмитрий", "last_name": "Новиков", "position": "Менеджер"},
            {"first_name": "Анна", "last_name": "Морозова", "position": "Директор"}
        ]
        
        # Неизвестная компания (GPT не найдет домен)
        company_name = "Неизвестная Тестовая Компания ООО"
        
        print(f"🏢 Компания: {company_name}")
        print(f"👥 Контактов: {len(contacts)}")
        print(f"📁 Директория закупок: отсутствует")
        print(f"🔑 Hunter.io API: отсутствует (имитация)")
        print(f"🎯 Ожидаем: отсутствие доменов (без API ключей)")
        
        domain, pattern, example = await find_email_pattern_for_contacts(
            client, company_name, contacts, None, None
        )
        
        print(f"\n📊 Результат:")
        print(f"  Домен: {domain}")
        print(f"  Паттерн: {pattern}")
        print(f"  Пример: {example}")
        
        # В этом сценарии ожидаем отсутствие результатов
        no_domain = not bool(domain)
        no_pattern = not bool(pattern)
        no_example = not bool(example)
        
        print(f"\n✅ Домен отсутствует (ожидаемо): {no_domain}")
        print(f"✅ Паттерн отсутствует (ожидаемо): {no_pattern}")
        print(f"✅ Пример отсутствует (ожидаемо): {no_example}")
        
        return no_domain and no_pattern and no_example
        
    except Exception as e:
        print(f"❌ Ошибка теста: {e}")
        return False

async def test_domain_priority_logic():
    """Тест логики приоритетов доменов"""
    print("\n🧪 ТЕСТ ЛОГИКИ ПРИОРИТЕТОВ ДОМЕНОВ")
    print("=" * 60)
    
    try:
        # Имитируем разные сценарии приоритетов
        test_scenarios = [
            {
                "name": "Закупки + GPT",
                "procurement_domain": "belregion.ru",
                "gpt_domain": "kurganobl.ru", 
                "expected": "belregion.ru",
                "reason": "Закупки имеют приоритет"
            },
            {
                "name": "Только GPT",
                "procurement_domain": "",
                "gpt_domain": "sberbank.ru",
                "expected": "sberbank.ru", 
                "reason": "GPT как fallback"
            },
            {
                "name": "Ничего нет",
                "procurement_domain": "",
                "gpt_domain": "",
                "expected": "",
                "reason": "Нет источников доменов"
            }
        ]
        
        print("📊 Тестовые сценарии приоритетов:")
        
        all_correct = True
        for i, scenario in enumerate(test_scenarios, 1):
            print(f"\n  Сценарий {i}: {scenario['name']}")
            print(f"    Домен из закупок: {scenario['procurement_domain'] or 'отсутствует'}")
            print(f"    Домен от GPT: {scenario['gpt_domain'] or 'отсутствует'}")
            print(f"    Ожидаемый результат: {scenario['expected'] or 'отсутствует'}")
            print(f"    Причина: {scenario['reason']}")
            
            # Логика приоритетов (имитация)
            if scenario['procurement_domain']:
                actual = scenario['procurement_domain']
                source = "закупки"
            elif scenario['gpt_domain']:
                actual = scenario['gpt_domain']
                source = "GPT"
            else:
                actual = ""
                source = "нет"
            
            correct = actual == scenario['expected']
            print(f"    Фактический результат: {actual or 'отсутствует'} (источник: {source})")
            print(f"    ✅ Правильный приоритет: {correct}")
            
            if not correct:
                all_correct = False
        
        return all_correct
        
    except Exception as e:
        print(f"❌ Ошибка теста: {e}")
        return False

def test_imports():
    """Тест импортов обновленных функций"""
    print("\n🧪 ТЕСТ ИМПОРТОВ ОБНОВЛЕННЫХ ФУНКЦИЙ")
    print("=" * 60)
    
    try:
        from leadgen_enhanced import find_email_pattern_for_contacts
        
        print("✅ Обновленная функция импортирована успешно:")
        print("  • find_email_pattern_for_contacts (исправленные приоритеты доменов)")
        
        return True
        
    except Exception as e:
        print(f"❌ Ошибка импорта: {e}")
        return False

async def main():
    """Главная функция тестирования"""
    print("🧪 ТЕСТИРОВАНИЕ ИСПРАВЛЕНИЯ ПРИОРИТЕТОВ ДОМЕНОВ")
    print("=" * 80)
    
    # Сначала тестируем импорты
    imports_ok = test_imports()
    
    if not imports_ok:
        print("❌ Проблемы с импортами, остальные тесты пропущены")
        return
    
    # Синхронные тесты
    sync_tests = [
        ("Логика приоритетов доменов", test_domain_priority_logic())
    ]
    
    # Асинхронные тесты
    async_tests = [
        ("Приоритет домена из закупок", test_procurement_domain_priority()),
        ("Fallback на GPT домен", test_gpt_domain_fallback()),
        ("Использование Hunter.io домена", test_hunter_domain_usage())
    ]
    
    print("\n📊 РЕЗУЛЬТАТЫ ТЕСТИРОВАНИЯ:")
    print("=" * 80)
    
    passed = 1 if imports_ok else 0  # Импорты уже протестированы
    print(f"  Импорты обновленных функций: {'✅ ПРОЙДЕН' if imports_ok else '❌ НЕ ПРОЙДЕН'}")
    
    # Синхронные тесты
    for test_name, result in sync_tests:
        status = "✅ ПРОЙДЕН" if result else "❌ НЕ ПРОЙДЕН"
        print(f"  {test_name}: {status}")
        if result:
            passed += 1
    
    # Асинхронные тесты
    for test_name, test_coro in async_tests:
        try:
            result = await test_coro
            status = "✅ ПРОЙДЕН" if result else "❌ НЕ ПРОЙДЕН"
            print(f"  {test_name}: {status}")
            if result:
                passed += 1
        except Exception as e:
            print(f"  {test_name}: ❌ НЕ ПРОЙДЕН ({e})")
    
    total_tests = len(sync_tests) + len(async_tests) + 1
    print(f"\n🎯 ИТОГО: {passed}/{total_tests} тестов пройдено")
    
    if passed == total_tests:
        print("🎉 ВСЕ ТЕСТЫ ПРОЙДЕНЫ!")
        print("\n✅ ИСПРАВЛЕНИЕ ПРИОРИТЕТОВ ДОМЕНОВ:")
        print("  • Домен из закупок имеет наивысший приоритет")
        print("  • GPT домен используется как fallback")
        print("  • Hunter.io домен используется в крайнем случае")
        print("  • Нет перезаписи приоритетного домена менее важным")
        print("  • Логика приоритетов работает корректно во всех сценариях")
    elif passed > 2:
        print("⚠️  Частично работает - проверьте логику приоритетов")
    else:
        print("❌ Есть серьезные проблемы с приоритетами доменов")

if __name__ == "__main__":
    asyncio.run(main())
