# 📧 Анализ процесса поиска и генерации email

## 🔄 Полный процесс (2 промпта):

### **Промпт 1: `search_company_email_info()`**

**🎯 Цель:** Найти реальные корпоративные email адреса сотрудников компании

**📥 Вход:**
- `company_name` - название компании (например: "Сбербанк")

**🧠 Что делает GPT:**
1. Определяет официальный веб-сайт компании
2. Ищет примеры корпоративных адресов на сайте
3. Собирает реальные email адреса из публичных источников:
   - Страницы "Контакты"
   - PDF-отчёты
   - Раздел "О компании"
   - LinkedIn профили
   - Github
   - Конференционные списки
   - Пресс-релизы

**📤 Выход:**
JSON-массив реальных email адресов:
```json
[
  "<EMAIL>",
  "<EMAIL>",
  "<EMAIL>"
]
```

**🔍 Ключевые особенности:**
- Только реально найденные адреса (не генерированные)
- Каждый адрес подтверждён источником
- Никаких шаблонных или вымышленных адресов

---

### **Промпт 2: `generate_personal_email()`**

**🎯 Цель:** Сгенерировать персональный email для конкретного сотрудника

**📥 Вход:**
- `first_name` - имя на русском (например: "Александр")
- `last_name` - фамилия на русском (например: "Иванов")
- `first_en` - имя на английском (например: "aleksandr")
- `last_en` - фамилия на английском (например: "ivanov")
- `company_email_info` - результат первого промпта (JSON-массив реальных email)

**🧠 Что делает GPT:**
1. Анализирует `company_email_info`:
   - Вычленяет используемый домен (всё после "@")
   - Определяет логику построения найденных почт
2. Применяет выявленный шаблон к английским именам
3. Возвращает один наиболее вероятный email

**📤 Выход:**
Один email адрес: `<EMAIL>`

**🔍 Ключевые особенности:**
- Использует только английские имена
- Основывается на реальных примерах из первого промпта
- Возвращает только адрес без лишнего текста
- Если информация неясна - возвращает пустое значение

---

## 🔗 Связь между промптами:

### **Передача данных:**
```
Промпт 1: company_name → JSON-массив реальных email
                ↓
Промпт 2: JSON-массив + имена → персональный email
```

### **Пример передачи:**

**Промпт 1 получает:** `"Сбербанк"`
**Промпт 1 возвращает:**
```json
["<EMAIL>", "<EMAIL>"]
```

**Промпт 2 получает:**
- Имя: `"Александр"` / `"aleksandr"`
- Фамилия: `"Иванов"` / `"ivanov"`
- Примеры: `["<EMAIL>", "<EMAIL>"]`

**Промпт 2 анализирует:**
- Домен: `sberbank.ru`
- Шаблон: `имя.фамилия@домен`

**Промпт 2 возвращает:** `<EMAIL>`

---

## 🧹 Удаленные лишние элементы:

### **❌ Удалена функция `generate_email_variants()`**
**Причина:** Теперь email генерируется через GPT, а не программно

**Что было удалено:**
```python
# Стандартные шаблоны
patterns = [
    "name.surname@domain",
    "n.surname@domain", 
    "name@domain",
    "surname@domain",
    "name_surname@domain",
    "namesurname@domain"
]

# Программная замена шаблонов
for pattern in patterns:
    email = pattern.replace("name", first_en)
    email = email.replace("surname", last_en)
    # и т.д.
```

**Почему удалено:**
- GPT сам анализирует реальные примеры и выводит шаблон
- Программные шаблоны могут не соответствовать реальности
- GPT более гибкий в определении логики построения email

---

## ✅ Преимущества новой логики:

1. **🎯 Реальные данные:** Первый промпт ищет настоящие email адреса
2. **🧠 Умный анализ:** Второй промпт анализирует найденные примеры
3. **🔗 Контекстность:** Каждый email основан на реальных примерах компании
4. **🌐 Правильная транслитерация:** Русские имена корректно переводятся
5. **📏 Точность:** Один email на человека, без множественных вариантов

## 🔄 Полный workflow:

```
1. Веб-поиск контактов → [имя, фамилия, должность]
2. Setka API поиск → [имя, фамилия, должность]
3. Объединение контактов
4. Промпт 1: поиск реальных email компании → JSON-массив
5. Для каждого контакта:
   a. Расшифровка частичных имен (если нужно)
   b. Промпт 2: генерация персонального email → один email
   c. Валидация email
   d. Формирование итоговой строки
```

## 📊 Итоговый результат:

```
• Иванов Александр — CTO (<EMAIL>)
• Петрова Мария — CIO (<EMAIL>)
• Козлов Михаил — IT Director (<EMAIL>)
```

**🎯 Каждый email основан на реальных примерах, найденных в публичных источниках!**
