"""test_service_email_filtering.py
Тест фильтрации служебных email (zak<PERSON><PERSON>@, info@, etc.)
"""

import os
import sys
import asyncio
import pandas as pd
import tempfile
import shutil

# Устанавливаем API ключи для тестирования
openai_api_key = os.getenv("OPENAI_API_KEY")

if not openai_api_key:
    print("❌ Установите OPENAI_API_KEY для тестирования")
    sys.exit(1)

# Предотвращаем запуск CLI
sys.argv = ['test']

def test_filter_personal_emails():
    """Тест фильтрации персональных email"""
    print("\n🧪 ТЕСТ ФИЛЬТРАЦИИ ПЕРСОНАЛЬНЫХ EMAIL")
    print("=" * 60)
    
    try:
        from leadgen_enhanced import filter_personal_emails
        
        # Тестовые случаи
        test_cases = [
            {
                "name": "Смешанные email (персональные + служебные)",
                "emails": [
                    "<EMAIL>",
                    "<EMAIL>", 
                    "<EMAIL>",
                    "<EMAIL>",
                    "<EMAIL>"
                ],
                "expected_personal": 3,
                "expected_domain": "rfi.mnr.gov.ru"
            },
            {
                "name": "Только служебные email",
                "emails": [
                    "<EMAIL>",
                    "<EMAIL>",
                    "<EMAIL>",
                    "<EMAIL>"
                ],
                "expected_personal": 0,
                "expected_domain": "company.ru"
            },
            {
                "name": "Только персональные email",
                "emails": [
                    "<EMAIL>",
                    "<EMAIL>",
                    "<EMAIL>"
                ],
                "expected_personal": 3,
                "expected_domain": "company.ru"
            },
            {
                "name": "Различные служебные варианты",
                "emails": [
                    "<EMAIL>",
                    "<EMAIL>", 
                    "<EMAIL>",
                    "торги@company.ru",
                    "<EMAIL>",
                    "<EMAIL>",
                    "<EMAIL>",
                    "<EMAIL>"
                ],
                "expected_personal": 0,
                "expected_domain": "company.ru"
            }
        ]
        
        passed_tests = 0
        
        for i, test_case in enumerate(test_cases, 1):
            print(f"\n  Тест {i}: {test_case['name']}")
            print(f"    Входные email: {len(test_case['emails'])}")
            
            personal_emails, domain = filter_personal_emails(test_case['emails'])
            
            print(f"    Персональных: {len(personal_emails)}")
            print(f"    Домен: {domain}")
            
            # Проверяем результат
            correct_count = len(personal_emails) == test_case['expected_personal']
            correct_domain = domain == test_case['expected_domain']
            
            if correct_count and correct_domain:
                print(f"    ✅ УСПЕХ: Правильное количество и домен")
                passed_tests += 1
            else:
                print(f"    ❌ НЕУДАЧА:")
                if not correct_count:
                    print(f"       Ожидали {test_case['expected_personal']} персональных, получили {len(personal_emails)}")
                if not correct_domain:
                    print(f"       Ожидали домен {test_case['expected_domain']}, получили {domain}")
        
        print(f"\n📊 Результат: {passed_tests}/{len(test_cases)} тестов пройдено")
        
        return passed_tests == len(test_cases)
        
    except Exception as e:
        print(f"❌ Ошибка теста: {e}")
        return False

def create_test_tenders_with_service_emails():
    """Создаем тестовую директорию с служебными email"""
    print("📁 Создание тестовой директории со служебными email...")
    
    # Создаем временную директорию
    tenders_dir = tempfile.mkdtemp(prefix="test_service_emails_")
    
    # Создаем тестовый Excel файл
    data = []
    columns = [chr(65 + i) for i in range(30)]  # A-Z + AA-AD
    
    # Заголовки
    header_row = [''] * 30
    header_row[26] = 'Заказчик'  # AA
    data.append(header_row)
    data.append([''] * 30)  # Пустая строка
    
    # Тестовые данные с разными типами email
    test_rows = [
        {
            'V': 'Контакт для закупок: <EMAIL>',  # Служебный
            'AA': 'Российский Фонд Информации'
        },
        {
            'V': 'Ответственный: Стройков В.А., email: <EMAIL>',  # Персональный
            'AA': 'Российский Фонд Информации'
        },
        {
            'V': 'Общая информация: <EMAIL>',  # Служебный
            'AA': 'Российский Фонд Информации'
        },
        {
            'V': 'Менеджер: Иванова Н.П., email: <EMAIL>',  # Персональный
            'AA': 'Российский Фонд Информации'
        },
        {
            'V': 'Связь по тендерам: <EMAIL>',  # Служебный
            'AA': 'Российский Фонд Информации'
        }
    ]
    
    for row_data in test_rows:
        row = [''] * 30
        row[21] = row_data['V']   # Столбец V (индекс 21)
        row[26] = row_data['AA']  # Столбец AA (индекс 26)
        data.append(row)
    
    # Создаем DataFrame и сохраняем
    df = pd.DataFrame(data)
    excel_path = os.path.join(tenders_dir, "test_service_emails.xlsx")
    df.to_excel(excel_path, index=False, header=False)
    
    print(f"  ✅ Создан файл: test_service_emails.xlsx ({len(data)} строк)")
    print(f"✅ Создана тестовая директория: {tenders_dir}")
    
    return tenders_dir

async def test_procurement_analysis_with_service_emails():
    """Тест анализа закупок со служебными email"""
    print("\n🧪 ТЕСТ АНАЛИЗА ЗАКУПОК СО СЛУЖЕБНЫМИ EMAIL")
    print("=" * 60)
    
    try:
        from leadgen_enhanced import analyze_procurement_data
        import openai
        
        client = openai.AsyncClient(api_key=openai_api_key)
        
        # Создаем тестовую директорию
        tenders_dir = create_test_tenders_with_service_emails()
        
        company_name = "Российский Фонд Информации"
        
        print(f"🏢 Компания: {company_name}")
        print(f"📁 Директория: {tenders_dir}")
        
        result = await analyze_procurement_data(client, company_name, tenders_dir)
        
        print(f"\n📊 Результат анализа:")
        print(f"  Домен: {result.get('domain', '')}")
        print(f"  Паттерн: {result.get('pattern', '')}")
        print(f"  Пример: {result.get('example', '')}")
        
        # Проверяем результат
        has_domain = bool(result.get('domain'))
        correct_domain = 'rfi.mnr.gov.ru' in result.get('domain', '')
        has_example = bool(result.get('example'))
        
        print(f"\n✅ Домен найден: {has_domain}")
        print(f"✅ Правильный домен: {correct_domain}")
        print(f"✅ Пример найден: {has_example}")
        
        # Удаляем тестовую директорию
        shutil.rmtree(tenders_dir)
        print(f"🗑️ Удалена тестовая директория")
        
        return has_domain and correct_domain
        
    except Exception as e:
        print(f"❌ Ошибка теста: {e}")
        return False

async def test_only_service_emails_scenario():
    """Тест сценария с только служебными email"""
    print("\n🧪 ТЕСТ СЦЕНАРИЯ С ТОЛЬКО СЛУЖЕБНЫМИ EMAIL")
    print("=" * 60)
    
    try:
        from leadgen_enhanced import analyze_procurement_data
        import openai
        
        client = openai.AsyncClient(api_key=openai_api_key)
        
        # Создаем тестовую директорию только со служебными email
        tenders_dir = tempfile.mkdtemp(prefix="test_only_service_")
        
        # Создаем тестовый Excel файл только со служебными email
        data = []
        header_row = [''] * 30
        header_row[26] = 'Заказчик'  # AA
        data.append(header_row)
        data.append([''] * 30)  # Пустая строка
        
        # Только служебные email
        test_rows = [
            {
                'V': 'Закупки: <EMAIL>',
                'AA': 'Тестовая Компания'
            },
            {
                'V': 'Информация: <EMAIL>',
                'AA': 'Тестовая Компания'
            },
            {
                'V': 'Тендеры: <EMAIL>',
                'AA': 'Тестовая Компания'
            }
        ]
        
        for row_data in test_rows:
            row = [''] * 30
            row[21] = row_data['V']   # Столбец V
            row[26] = row_data['AA']  # Столбец AA
            data.append(row)
        
        df = pd.DataFrame(data)
        excel_path = f"{tenders_dir}/test.xlsx"
        df.to_excel(excel_path, index=False, header=False)
        
        company_name = "Тестовая Компания"
        
        print(f"🏢 Компания: {company_name}")
        print(f"📁 Директория: {tenders_dir}")
        print(f"📧 Только служебные email в файле")
        
        result = await analyze_procurement_data(client, company_name, tenders_dir)
        
        print(f"\n📊 Результат анализа:")
        print(f"  Домен: {result.get('domain', '')}")
        print(f"  Паттерн: {result.get('pattern', '')}")
        print(f"  Пример: {result.get('example', '')}")
        
        # Проверяем результат: должен быть домен, но не должно быть паттерна/примера
        has_domain = bool(result.get('domain'))
        correct_domain = 'company.ru' in result.get('domain', '')
        no_pattern = not bool(result.get('pattern'))
        no_example = not bool(result.get('example'))
        
        print(f"\n✅ Домен найден: {has_domain}")
        print(f"✅ Правильный домен: {correct_domain}")
        print(f"✅ Паттерн отсутствует (ожидаемо): {no_pattern}")
        print(f"✅ Пример отсутствует (ожидаемо): {no_example}")
        
        # Удаляем тестовую директорию
        shutil.rmtree(tenders_dir)
        print(f"🗑️ Удалена тестовая директория")
        
        return has_domain and correct_domain and no_pattern and no_example
        
    except Exception as e:
        print(f"❌ Ошибка теста: {e}")
        return False

def test_imports():
    """Тест импортов функций фильтрации"""
    print("\n🧪 ТЕСТ ИМПОРТОВ ФУНКЦИЙ ФИЛЬТРАЦИИ")
    print("=" * 60)
    
    try:
        from leadgen_enhanced import (
            filter_personal_emails,
            filter_corporate_emails,
            analyze_procurement_data
        )
        
        print("✅ Все функции фильтрации импортированы успешно:")
        print("  • filter_personal_emails (новая функция)")
        print("  • filter_corporate_emails (существующая)")
        print("  • analyze_procurement_data (обновленная)")
        
        return True
        
    except Exception as e:
        print(f"❌ Ошибка импорта: {e}")
        return False

async def main():
    """Главная функция тестирования"""
    print("🧪 ТЕСТИРОВАНИЕ ФИЛЬТРАЦИИ СЛУЖЕБНЫХ EMAIL")
    print("=" * 80)
    
    # Сначала тестируем импорты
    imports_ok = test_imports()
    
    if not imports_ok:
        print("❌ Проблемы с импортами, остальные тесты пропущены")
        return
    
    # Синхронные тесты
    sync_tests = [
        ("Фильтрация персональных email", test_filter_personal_emails())
    ]
    
    # Асинхронные тесты
    async_tests = [
        ("Анализ закупок со служебными email", test_procurement_analysis_with_service_emails()),
        ("Сценарий с только служебными email", test_only_service_emails_scenario())
    ]
    
    print("\n📊 РЕЗУЛЬТАТЫ ТЕСТИРОВАНИЯ:")
    print("=" * 80)
    
    passed = 1 if imports_ok else 0  # Импорты уже протестированы
    print(f"  Импорты функций фильтрации: {'✅ ПРОЙДЕН' if imports_ok else '❌ НЕ ПРОЙДЕН'}")
    
    # Синхронные тесты
    for test_name, result in sync_tests:
        status = "✅ ПРОЙДЕН" if result else "❌ НЕ ПРОЙДЕН"
        print(f"  {test_name}: {status}")
        if result:
            passed += 1
    
    # Асинхронные тесты
    for test_name, test_coro in async_tests:
        try:
            result = await test_coro
            status = "✅ ПРОЙДЕН" if result else "❌ НЕ ПРОЙДЕН"
            print(f"  {test_name}: {status}")
            if result:
                passed += 1
        except Exception as e:
            print(f"  {test_name}: ❌ НЕ ПРОЙДЕН ({e})")
    
    total_tests = len(sync_tests) + len(async_tests) + 1
    print(f"\n🎯 ИТОГО: {passed}/{total_tests} тестов пройдено")
    
    if passed == total_tests:
        print("🎉 ВСЕ ТЕСТЫ ПРОЙДЕНЫ!")
        print("\n✅ ФИЛЬТРАЦИЯ СЛУЖЕБНЫХ EMAIL:")
        print("  • Исключает zakupki@, info@, admin@, support@ и другие служебные")
        print("  • Сохраняет домен даже если все email служебные")
        print("  • Анализирует паттерн только для персональных email")
        print("  • Fallback к другим методам если персональных email нет")
        print("  • Предотвращает анализ 'zakupki' как имени и фамилии")
    elif passed > 2:
        print("⚠️  Частично работает - проверьте API ключи и логику")
    else:
        print("❌ Есть серьезные проблемы с фильтрацией служебных email")

if __name__ == "__main__":
    asyncio.run(main())
