# Быстрый старт - Поиск сотрудников компании

## 🚀 Установка

1. **Установите зависимости:**
```bash
pip install -r requirements.txt
```

2. **Получите токен доступа к API Setka.ru** (см. `API Setka.md`)

## 📋 Основные файлы

- `company_search.py` - основная функция поиска
- `test_company_search.py` - тесты
- `example_usage.py` - примеры использования
- `README_company_search.md` - подробная документация

## ⚡ Быстрое использование

### Вариант 1: Командная строка
```bash
python3 company_search.py "Автоваз" "your_refresh_token_here"
```

### Вариант 2: Python код
```python
from company_search import search_company_employees_sync

# Поиск сотрудников
employees, new_refresh_token = search_company_employees_sync(
    company_name="Автоваз",
    refresh_token="your_refresh_token_here",
    output_dir="./reports"
)

print(f"Найдено {len(employees)} сотрудников")
for last_name, first_name, position in employees:
    print(f"{last_name} {first_name}: {position}")

print(f"Новый refresh токен: {new_refresh_token}")
```

### Вариант 3: Интерактивные примеры
```bash
python3 example_usage.py
```

## 🔍 Что делает функция

1. **Поиск компании** по названию через API Setka.ru
2. **Находит данные сотрудников** двумя способами:
   - **Если есть Networks** → получает участников корпоративной сети
   - **Если нет Networks** → парсит Accounts напрямую из поиска
3. **Фильтрует по должностям** с ключевыми словами:
   - директор, ИТ, IT, CIO, CTO
   - информационных технологий
   - цифровых технологий, цифровизации
   - автоматизации, разработки
4. **Создает Word отчет** с таблицей сотрудников
5. **Возвращает обновленный refresh токен**

## 📊 Результат

Функция возвращает кортеж:
```python
(
    [  # Список сотрудников
        ("Антонов", "Родион", "Руководитель проекта ИТ"),
        ("Петрова", "Анна", "Директор по информационным технологиям"),
        ("Козлова", "Мария", "CTO"),
        # ...
    ],
    "new_refresh_token_here"  # Обновленный refresh токен
)
```

И создает Word документ с отчетом в указанной папке.

## 🧪 Тестирование

```bash
# Запуск всех тестов
python3 test_company_search.py --test

# Запуск примера (требует реальный токен)
python3 test_company_search.py --example
```

## ⚙️ Настройка токена

### Через переменную окружения:
```bash
export SETKA_REFRESH_TOKEN="your_refresh_token_here"
```

### Через код:
```python
refresh_token = "your_refresh_token_here"
```

### Важно о токенах:
- Функция принимает **refresh_token** (не access_token)
- Автоматически получает access_token через API
- Возвращает **новый refresh_token** для следующего использования
- Всегда сохраняйте возвращаемый токен!

## 🔧 Кастомизация

### Изменение ключевых слов фильтрации:
```python
custom_keywords = ["директор", "CTO", "архитектор", "руководитель"]

employees, new_token = search_company_employees_sync(
    company_name="Компания",
    refresh_token="token",
    keywords=custom_keywords
)
```

### Изменение папки отчетов:
```python
employees, new_token = search_company_employees_sync(
    company_name="Компания",
    refresh_token="token",
    output_dir="./my_reports"
)
```

## ❗ Возможные ошибки

| Ошибка | Причина | Решение |
|--------|---------|---------|
| `Ошибка поиска компании: 401` | Неверный токен | Обновите токен доступа |
| `Networks не найдены` | У компании нет корпоративной сети | Попробуйте другую компанию |
| `aiohttp не установлен` | Отсутствует зависимость | `pip install aiohttp` |
| `python-docx не установлен` | Отсутствует зависимость | `pip install python-docx` |

## 📝 Примеры компаний для тестирования

- Автоваз
- Сбербанк
- Яндекс
- Mail.ru Group
- Тинькофф

## 🔗 Полезные ссылки

- [API Setka.ru документация](API%20Setka.md)
- [Подробная документация](README_company_search.md)
- [Примеры использования](example_usage.py)

## 📞 Поддержка

При возникновении проблем:
1. Проверьте, что все зависимости установлены
2. Убедитесь, что токен доступа действителен
3. Запустите тесты для проверки работоспособности
4. Изучите подробную документацию в `README_company_search.md`
