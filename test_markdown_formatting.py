"""test_markdown_formatting.py
Тест улучшенного форматирования Markdown документов
"""

import os
import sys
import tempfile

# Предотвращаем запуск CLI
sys.argv = ['test']

def test_improved_markdown_formatting():
    """Тест улучшенного форматирования с отступами"""
    print("\n🧪 ТЕСТ УЛУЧШЕННОГО MARKDOWN ФОРМАТИРОВАНИЯ")
    print("=" * 60)
    
    try:
        from leadgen_enhanced import create_markdown_document
        
        # Тестовые данные с реалистичным контентом
        company_name = "ФГАУ «НМИЦ МНТК «Микрохирургия глаза» имени академика С.Н. Фёдорова»"
        
        company_profile = """### 1. Основные ML/AI‑проекты
• Элементы ИИ применяются для планирования фемтолазерного сопровождения катарактальной и керато-рефракционной хирургии
• ИИ используется для прогноза прогрессирования ретинопатии недоношенных
• Центр организовал Первый Всероссийский саммит по использованию алгоритмов ИИ в офтальмологии
• Опубликован систематический обзор по применению ИИ в диагностике и хирургии кератоконуса

### 2. Контакты LPR + телефоны/e‑mail
• Иванов Александр — CTO (<EMAIL>)
• Петрова Мария — Руководитель отдела ИИ (<EMAIL>)
• Сидоров Сергей — Директор по цифровизации (<EMAIL>)

### 3. Релевантные закупки с kontur.zakupki
• **Разработка системы ИИ для диагностики** (2024, 15 млн руб.)
• **Внедрение ML платформы для анализа изображений** (2023, 8 млн руб.)
• **Консультации по автоматизации медицинских процессов** (2024, 3 млн руб.)"""

        email_content = """Добрый день!

Мы заметили, что ФГАУ «НМИЦ МНТК «Микрохирургия глаза» имени академика С.Н. Фёдорова» активно развивает инициативы в области ИИ и машинного обучения: элементы ИИ применяются для планирования фемтолазерного сопровождения катарактальной и керато-рефракционной хирургии и для прогноза прогрессирования ретинопатии недоношенных; Центр организовал Первый Всероссийский саммит по использованию алгоритмов ИИ в офтальмологии и опубликовал систематический обзор по применению ИИ в диагностике и хирургии кератоконуса.

Когда инженерные и продуктовые команды переходят к масштабированию таких инициатив, чаще всего всплывают три узких места:

• инфраструктура разрастается быстрее, чем её успевают стандартизировать и защищать;
• дорогие GPU-ресурсы полезно загружены в среднем на 10–13%;
• регуляторные требования усложняют эксплуатацию платформы;

Мы разработали Nova AI — on-premise-платформу оркестрации ML-задач на базе Kubernetes, уже укомплектованную драйверами, системой безопасности и инструментами для автоматического масштабирования обучения и инференса.

Nova AI помогает:

• обеспечивать соблюдение внутренних и внешних норм информационной безопасности при работе с конфиденциальными данными;
• повышать полезную утилизацию вычислительных узлов с GPU до 90–95 %;
• дать DS/ML-командам самообслуживание по развёртыванию экспериментов и сервисов, не нагружая DevOps.

Если вам будет полезно обсудить архитектуру решения и убедиться, что оно вписывается в ваши процессы, дайте знать — мы подстроимся под удобное для вас время для короткого технического разговора."""

        # Создаем временный файл
        with tempfile.NamedTemporaryFile(mode='w', suffix='.md', delete=False, encoding='utf-8') as tmp_file:
            output_path = tmp_file.name
        
        print(f"📝 Создание документа с улучшенным форматированием...")
        print(f"  Компания: {company_name[:50]}...")
        
        # Создаем документ
        create_markdown_document(email_content, company_profile, output_path, company_name)
        
        # Читаем результат
        with open(output_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        lines = content.split('\n')
        
        print(f"\n📊 Анализ форматирования:")
        print(f"  Общее количество строк: {len(lines)}")
        print(f"  Пустых строк: {lines.count('')}")
        print(f"  Процент пустых строк: {lines.count('') / len(lines) * 100:.1f}%")
        
        # Проверяем отступы от заголовков
        header_spacing_correct = True
        for i, line in enumerate(lines):
            if line.startswith('## '):
                # Проверяем отступ сверху (должна быть пустая строка перед заголовком, кроме первого)
                if i > 0 and lines[i-1] != '':
                    header_spacing_correct = False
                    print(f"  ⚠️ Нет отступа сверху от заголовка: {line}")
                
                # Проверяем отступ снизу (должна быть пустая строка после заголовка)
                if i < len(lines) - 1 and lines[i+1] != '':
                    header_spacing_correct = False
                    print(f"  ⚠️ Нет отступа снизу от заголовка: {line}")
        
        # Проверяем отступы между контактами
        contacts_spacing_correct = True
        in_contacts_section = False
        for i, line in enumerate(lines):
            if "Контакты LPR" in line:
                in_contacts_section = True
                continue
            elif line.startswith('## ') and in_contacts_section:
                in_contacts_section = False
                continue
            
            if in_contacts_section and line.strip().startswith('•'):
                # Это контакт, проверяем отступ после него
                if i < len(lines) - 1 and lines[i+1] != '' and not lines[i+1].startswith('## '):
                    contacts_spacing_correct = False
                    print(f"  ⚠️ Нет отступа после контакта: {line}")
        
        # Проверяем отступы между закупками
        tenders_spacing_correct = True
        in_tenders_section = False
        for i, line in enumerate(lines):
            if "закупки" in line.lower():
                in_tenders_section = True
                continue
            elif line.startswith('## ') and in_tenders_section:
                in_tenders_section = False
                continue
            
            if in_tenders_section and (line.strip().startswith('•') or line.strip().startswith('**')):
                # Это закупка, проверяем отступ после неё
                if i < len(lines) - 1 and lines[i+1] != '' and not lines[i+1].startswith('## '):
                    tenders_spacing_correct = False
                    print(f"  ⚠️ Нет отступа после закупки: {line}")
        
        # Проверяем форматирование письма
        email_formatting_correct = True
        in_email_section = False
        for i, line in enumerate(lines):
            if "Кастомизированное письмо" in line:
                in_email_section = True
                continue
            
            if in_email_section:
                # Проверяем, что после параграфов есть отступы
                if line.strip() and not line.startswith('•') and i < len(lines) - 1:
                    # Если это не последняя строка и не пункт списка
                    if lines[i+1] != '' and not lines[i+1].startswith('•'):
                        # Следующая строка не пустая и не пункт списка - может быть проблема
                        pass  # Пока не строгая проверка
        
        print(f"\n📋 Проверки форматирования:")
        print(f"  ✅ Отступы от заголовков: {header_spacing_correct}")
        print(f"  ✅ Отступы между контактами: {contacts_spacing_correct}")
        print(f"  ✅ Отступы между закупками: {tenders_spacing_correct}")
        print(f"  ✅ Форматирование письма: {email_formatting_correct}")
        
        # Показываем структуру документа
        print(f"\n📄 Структура документа (первые 30 строк):")
        for i, line in enumerate(lines[:30], 1):
            if line.strip():
                print(f"  {i:2d}: {line}")
            else:
                print(f"  {i:2d}: [пустая строка]")
        
        # Очищаем
        os.unlink(output_path)
        
        return all([header_spacing_correct, contacts_spacing_correct, 
                   tenders_spacing_correct, email_formatting_correct])
        
    except Exception as e:
        print(f"❌ Ошибка теста: {e}")
        return False

def test_email_bullet_points_formatting():
    """Тест форматирования пунктов списка в письме"""
    print("\n🧪 ТЕСТ ФОРМАТИРОВАНИЯ ПУНКТОВ СПИСКА В ПИСЬМЕ")
    print("=" * 60)
    
    try:
        from leadgen_enhanced import create_markdown_document
        
        # Тестовые данные с акцентом на списки в письме
        company_name = "Тест Компания"
        company_profile = "### 1. Основные ML/AI‑проекты\nТестовый проект"
        
        email_content = """Добрый день!

Мы предлагаем решение для ваших задач.

Основные проблемы, которые мы решаем:

• проблема номер один;
• проблема номер два;
• проблема номер три;

Наше решение Nova AI помогает:

• решить первую проблему эффективно;
• устранить вторую проблему полностью;
• предотвратить третью проблему заранее.

Будем рады обсудить детали."""

        # Создаем временный файл
        with tempfile.NamedTemporaryFile(mode='w', suffix='.md', delete=False, encoding='utf-8') as tmp_file:
            output_path = tmp_file.name
        
        print(f"📝 Тестирование форматирования списков...")
        
        # Создаем документ
        create_markdown_document(email_content, company_profile, output_path, company_name)
        
        # Читаем результат
        with open(output_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        lines = content.split('\n')
        
        # Находим секцию письма
        email_start = -1
        for i, line in enumerate(lines):
            if "Кастомизированное письмо" in line:
                email_start = i
                break
        
        if email_start == -1:
            print("❌ Секция письма не найдена")
            return False
        
        email_lines = lines[email_start:]
        
        # Анализируем форматирование списков
        bullet_points = []
        for i, line in enumerate(email_lines):
            if line.strip().startswith('•'):
                bullet_points.append((i, line.strip()))
        
        print(f"📊 Найдено пунктов списка: {len(bullet_points)}")
        
        # Проверяем отступы вокруг списков
        list_formatting_correct = True
        for i, (line_idx, bullet_text) in enumerate(bullet_points):
            print(f"  {i+1}. {bullet_text}")
            
            # Проверяем отступ после пункта списка
            if line_idx < len(email_lines) - 1:
                next_line = email_lines[line_idx + 1]
                if next_line != '' and not next_line.strip().startswith('•'):
                    # Следующая строка не пустая и не пункт списка
                    if i == len(bullet_points) - 1 or bullet_points[i+1][0] != line_idx + 1:
                        # Это последний пункт в списке или следующий пункт не идет сразу
                        list_formatting_correct = False
                        print(f"    ⚠️ Нет отступа после пункта списка")
        
        print(f"\n📋 Проверка форматирования списков:")
        print(f"  ✅ Правильное форматирование списков: {list_formatting_correct}")
        
        # Показываем секцию письма
        print(f"\n📄 Секция письма:")
        for i, line in enumerate(email_lines[:20], 1):
            if line.strip():
                print(f"  {i:2d}: {line}")
            else:
                print(f"  {i:2d}: [пустая строка]")
        
        # Очищаем
        os.unlink(output_path)
        
        return list_formatting_correct
        
    except Exception as e:
        print(f"❌ Ошибка теста: {e}")
        return False

def test_real_world_example():
    """Тест на реальном примере из задания"""
    print("\n🧪 ТЕСТ НА РЕАЛЬНОМ ПРИМЕРЕ ИЗ ЗАДАНИЯ")
    print("=" * 60)
    
    try:
        from leadgen_enhanced import create_markdown_document
        
        # Точный пример из задания
        company_name = "ФГАУ «НМИЦ МНТК «Микрохирургия глаза» имени академика С.Н. Фёдорова»"
        company_profile = """### 1. Основные ML/AI‑проекты
Элементы ИИ применяются для планирования фемтолазерного сопровождения катарактальной и керато-рефракционной хирургии и для прогноза прогрессирования ретинопатии недоношенных; Центр организовал Первый Всероссийский саммит по использованию алгоритмов ИИ в офтальмологии и опубликовал систематический обзор по применению ИИ в диагностике и хирургии кератоконуса.

### 2. Контакты LPR + телефоны/e‑mail
• Иванов Александр — CTO (<EMAIL>)
• Петрова Мария — Руководитель отдела ИИ (<EMAIL>)

### 3. Релевантные закупки с kontur.zakupki
• Разработка системы ИИ для диагностики (2024)
• Внедрение ML платформы для анализа изображений (2023)"""

        # Точное письмо из примера
        email_content = """Добрый день!

Мы заметили, что ФГАУ «НМИЦ МНТК «Микрохирургия глаза» имени академика С.Н. Фёдорова» активно развивает инициативы в области ИИ и машинного обучения: элементы ИИ применяются для планирования фемтолазерного сопровождения катарактальной и керато-рефракционной хирургии и для прогноза прогрессирования ретинопатии недоношенных; Центр организовал Первый Всероссийский саммит по использованию алгоритмов ИИ в офтальмологии и опубликовал систематический обзор по применению ИИ в диагностике и хирургии кератоконуса.

Когда инженерные и продуктовые команды переходят к масштабированию таких инициатив, чаще всего всплывают три узких места:

• инфраструктура разрастается быстрее, чем её успевают стандартизировать и защищать;
• дорогие GPU-ресурсы полезно загружены в среднем на 10–13%;
• регуляторные требования усложняют эксплуатацию платформы;

Мы разработали Nova AI — on-premise-платформу оркестрации ML-задач на базе Kubernetes, уже укомплектованную драйверами, системой безопасности и инструментами для автоматического масштабирования обучения и инференса.

Nova AI помогает:

• обеспечивать соблюдение внутренних и внешних норм информационной безопасности при работе с конфиденциальными данными;
• повышать полезную утилизацию вычислительных узлов с GPU до 90–95 %;
• дать DS/ML-командам самообслуживание по развёртыванию экспериментов и сервисов, не нагружая DevOps.

Если вам будет полезно обсудить архитектуру решения и убедиться, что оно вписывается в ваши процессы, дайте знать — мы подстроимся под удобное для вас время для короткого технического разговора."""

        # Создаем временный файл
        with tempfile.NamedTemporaryFile(mode='w', suffix='.md', delete=False, encoding='utf-8') as tmp_file:
            output_path = tmp_file.name
        
        print(f"📝 Создание документа по реальному примеру...")
        
        # Создаем документ
        create_markdown_document(email_content, company_profile, output_path, company_name)
        
        # Читаем результат
        with open(output_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        print(f"📊 Результат:")
        print(f"  Размер документа: {len(content)} символов")
        lines_count = len(content.split('\n'))
        print(f"  Количество строк: {lines_count}")
        
        # Проверяем ключевые элементы
        has_proper_title = f"# Ценностное предложение для {company_name}" in content
        has_contacts_spacing = "• Иванов Александр" in content and "• Петрова Мария" in content
        has_bullet_points = "• инфраструктура разрастается" in content
        has_proper_sections = all(section in content for section in ["## 1. Основные ML/AI‑проекты", "## 2. Контакты LPR", "## 3. Релевантные закупки"])
        
        print(f"\n📋 Проверки реального примера:")
        print(f"  ✅ Правильный заголовок: {has_proper_title}")
        print(f"  ✅ Контакты с отступами: {has_contacts_spacing}")
        print(f"  ✅ Пункты списка в письме: {has_bullet_points}")
        print(f"  ✅ Все секции присутствуют: {has_proper_sections}")
        
        # Показываем финальный документ
        print(f"\n📄 Финальный документ:")
        lines = content.split('\n')
        for i, line in enumerate(lines, 1):
            if i <= 50:  # Показываем первые 50 строк
                if line.strip():
                    print(f"  {i:2d}: {line}")
                else:
                    print(f"  {i:2d}: [пустая строка]")
            else:
                print(f"  ... (всего {len(lines)} строк)")
                break
        
        # Очищаем
        os.unlink(output_path)
        
        return all([has_proper_title, has_contacts_spacing, has_bullet_points, has_proper_sections])
        
    except Exception as e:
        print(f"❌ Ошибка теста: {e}")
        return False

def main():
    """Главная функция тестирования"""
    print("🧪 ТЕСТИРОВАНИЕ УЛУЧШЕННОГО MARKDOWN ФОРМАТИРОВАНИЯ")
    print("=" * 80)
    
    # Тесты
    tests = [
        ("Улучшенное форматирование с отступами", test_improved_markdown_formatting()),
        ("Форматирование пунктов списка в письме", test_email_bullet_points_formatting()),
        ("Реальный пример из задания", test_real_world_example())
    ]
    
    print("\n📊 РЕЗУЛЬТАТЫ ТЕСТИРОВАНИЯ:")
    print("=" * 80)
    
    passed = 0
    for test_name, result in tests:
        status = "✅ ПРОЙДЕН" if result else "❌ НЕ ПРОЙДЕН"
        print(f"  {test_name}: {status}")
        if result:
            passed += 1
    
    total_tests = len(tests)
    print(f"\n🎯 ИТОГО: {passed}/{total_tests} тестов пройдено")
    
    if passed == total_tests:
        print("🎉 ВСЕ ТЕСТЫ ПРОЙДЕНЫ!")
        print("\n✅ УЛУЧШЕННОЕ MARKDOWN ФОРМАТИРОВАНИЕ:")
        print("  • Отступы сверху и снизу от всех заголовков")
        print("  • Отступы между отдельными контактными лицами")
        print("  • Отступы между отдельными закупками")
        print("  • Правильное форматирование пунктов списка в письме")
        print("  • Сохранение читаемости и структуры")
        print("  • Соответствие примеру из задания")
    elif passed > 1:
        print("⚠️  Частично работает - проверьте форматирование")
    else:
        print("❌ Есть серьезные проблемы с форматированием")

if __name__ == "__main__":
    main()
