"""test_unified_gpt_generation.py
Тест унифицированной GPT генерации email для всех сценариев
"""

import os
import sys
import asyncio
import pandas as pd
import tempfile
import shutil

# Устанавливаем API ключи для тестирования
openai_api_key = os.getenv("OPENAI_API_KEY")

if not openai_api_key:
    print("❌ Установите OPENAI_API_KEY для тестирования")
    sys.exit(1)

# Предотвращаем запуск CLI
sys.argv = ['test']

async def test_scenario_1_procurement_examples():
    """Тест сценария 1: Примеры из закупок"""
    print("\n🧪 СЦЕНАРИЙ 1: ПРИМЕРЫ ИЗ ЗАКУПОК")
    print("=" * 60)
    
    try:
        from leadgen_enhanced import find_email_pattern_for_contacts
        import openai
        
        client = openai.AsyncClient(api_key=openai_api_key)
        
        # Создаем тестовую директорию с закупками
        tenders_dir = tempfile.mkdtemp(prefix="test_scenario1_")
        
        # Создаем Excel файл с персональными email
        data = []
        header_row = [''] * 30
        header_row[26] = 'Заказчик'  # AA
        data.append(header_row)
        data.append([''] * 30)  # Пустая строка
        
        test_row = [''] * 30
        test_row[21] = 'Ответственный: Стройков В.А., email: <EMAIL>'  # V
        test_row[26] = 'Тестовая Компания'  # AA
        data.append(test_row)
        
        df = pd.DataFrame(data)
        excel_path = f"{tenders_dir}/test.xlsx"
        df.to_excel(excel_path, index=False, header=False)
        
        # Тестовые контакты
        contacts = [
            {"first_name": "Иван", "last_name": "Петров", "position": "Менеджер"},
            {"first_name": "Мария", "last_name": "Сидорова", "position": "Директор"}
        ]
        
        company_name = "Тестовая Компания"
        
        print(f"🏢 Компания: {company_name}")
        print(f"📁 Директория: {tenders_dir}")
        print(f"👥 Контактов: {len(contacts)}")
        
        domain, pattern, example = await find_email_pattern_for_contacts(
            client, company_name, contacts, None, tenders_dir
        )
        
        print(f"\n📊 Результат:")
        print(f"  Домен: {domain}")
        print(f"  Паттерн: {pattern}")
        print(f"  Пример: {example}")
        
        # Проверяем результат
        has_domain = bool(domain)
        has_example = bool(example)
        correct_domain = 'company.ru' in domain
        no_pattern = not bool(pattern)  # Паттерн должен быть пустым для GPT генерации
        
        print(f"\n✅ Домен найден: {has_domain}")
        print(f"✅ Пример найден: {has_example}")
        print(f"✅ Правильный домен: {correct_domain}")
        print(f"✅ Паттерн пустой (GPT генерация): {no_pattern}")
        
        # Удаляем тестовую директорию
        shutil.rmtree(tenders_dir)
        print(f"🗑️ Удалена тестовая директория")
        
        return has_domain and has_example and correct_domain and no_pattern
        
    except Exception as e:
        print(f"❌ Ошибка теста: {e}")
        return False

async def test_scenario_2_gpt_examples():
    """Тест сценария 2: Примеры от GPT"""
    print("\n🧪 СЦЕНАРИЙ 2: ПРИМЕРЫ ОТ GPT")
    print("=" * 60)
    
    try:
        from leadgen_enhanced import find_email_pattern_for_contacts
        import openai
        
        client = openai.AsyncClient(api_key=openai_api_key)
        
        # Тестовые контакты
        contacts = [
            {"first_name": "Алексей", "last_name": "Козлов", "position": "Менеджер"},
            {"first_name": "Елена", "last_name": "Волкова", "position": "Директор"}
        ]
        
        # Известная компания для GPT поиска
        company_name = "Сбербанк"
        
        print(f"🏢 Компания: {company_name}")
        print(f"👥 Контактов: {len(contacts)}")
        print(f"📁 Директория закупок: отсутствует")
        
        domain, pattern, example = await find_email_pattern_for_contacts(
            client, company_name, contacts, None, None
        )
        
        print(f"\n📊 Результат:")
        print(f"  Домен: {domain}")
        print(f"  Паттерн: {pattern}")
        print(f"  Пример: {example}")
        
        # Проверяем результат
        has_domain = bool(domain)
        has_example = bool(example)
        no_pattern = not bool(pattern)  # Паттерн должен быть пустым для GPT генерации
        
        print(f"\n✅ Домен найден: {has_domain}")
        print(f"✅ Пример найден: {has_example}")
        print(f"✅ Паттерн пустой (GPT генерация): {no_pattern}")
        
        return has_domain and has_example and no_pattern
        
    except Exception as e:
        print(f"❌ Ошибка теста: {e}")
        return False

async def test_scenario_3_hunter_examples():
    """Тест сценария 3: Примеры от Hunter.io"""
    print("\n🧪 СЦЕНАРИЙ 3: ПРИМЕРЫ ОТ HUNTER.IO")
    print("=" * 60)
    
    try:
        from leadgen_enhanced import find_email_pattern_for_contacts
        import openai
        
        client = openai.AsyncClient(api_key=openai_api_key)
        
        # Тестовые контакты
        contacts = [
            {"first_name": "Дмитрий", "last_name": "Новиков", "position": "Менеджер"},
            {"first_name": "Анна", "last_name": "Морозова", "position": "Директор"}
        ]
        
        # Малоизвестная компания (GPT может не найти)
        company_name = "Неизвестная Тестовая Компания ООО"
        
        print(f"🏢 Компания: {company_name}")
        print(f"👥 Контактов: {len(contacts)}")
        print(f"📁 Директория закупок: отсутствует")
        print(f"🔑 Hunter.io API: отсутствует (имитация)")
        
        domain, pattern, example = await find_email_pattern_for_contacts(
            client, company_name, contacts, None, None
        )
        
        print(f"\n📊 Результат:")
        print(f"  Домен: {domain}")
        print(f"  Паттерн: {pattern}")
        print(f"  Пример: {example}")
        
        # В этом сценарии ожидаем отсутствие результатов
        no_domain = not bool(domain)
        no_pattern = not bool(pattern)
        no_example = not bool(example)
        
        print(f"\n✅ Домен отсутствует (ожидаемо): {no_domain}")
        print(f"✅ Паттерн отсутствует (ожидаемо): {no_pattern}")
        print(f"✅ Пример отсутствует (ожидаемо): {no_example}")
        
        return no_domain and no_pattern and no_example
        
    except Exception as e:
        print(f"❌ Ошибка теста: {e}")
        return False

async def test_full_workflow_with_gpt_generation():
    """Тест полного workflow с GPT генерацией"""
    print("\n🧪 ПОЛНЫЙ WORKFLOW С GPT ГЕНЕРАЦИЕЙ")
    print("=" * 60)
    
    try:
        from leadgen_enhanced import process_contacts_with_emails_new_logic
        import openai
        
        client = openai.AsyncClient(api_key=openai_api_key)
        
        # Создаем тестовую директорию с закупками
        tenders_dir = tempfile.mkdtemp(prefix="test_workflow_")
        
        # Создаем Excel файл с персональными email
        data = []
        header_row = [''] * 30
        header_row[26] = 'Заказчик'  # AA
        data.append(header_row)
        data.append([''] * 30)  # Пустая строка
        
        test_row = [''] * 30
        test_row[21] = 'Менеджер: Иванов И.И., email: <EMAIL>'  # V
        test_row[26] = 'Тестовая Компания'  # AA
        data.append(test_row)
        
        df = pd.DataFrame(data)
        excel_path = f"{tenders_dir}/test.xlsx"
        df.to_excel(excel_path, index=False, header=False)
        
        # Тестовые контакты
        contacts = [
            {
                "first_name": "Петр",
                "last_name": "Сидоров",
                "position": "Директор",
                "source": "web_search"
            },
            {
                "first_name": "Анна",
                "last_name": "Кузнецова",
                "position": "Менеджер",
                "source": "setka_api"
            }
        ]
        
        company_name = "Тестовая Компания"
        
        print(f"🏢 Компания: {company_name}")
        print(f"👥 Контактов: {len(contacts)}")
        print(f"📁 Директория: {tenders_dir}")
        
        result = await process_contacts_with_emails_new_logic(
            client, company_name, contacts, None, None, tenders_dir
        )
        
        print(f"\n📊 Результат workflow:")
        print(result[:500] + "..." if len(result) > 500 else result)
        
        # Проверяем результат
        has_contacts = len(result) > 50  # Достаточно длинный результат
        has_emails = '@' in result
        has_correct_domain = 'testcompany.ru' in result
        has_gpt_generated = 'p.sidorov@' in result or 'a.kuznetsova@' in result  # GPT генерация по примеру
        
        print(f"\n✅ Есть контакты: {has_contacts}")
        print(f"✅ Есть email: {has_emails}")
        print(f"✅ Правильный домен: {has_correct_domain}")
        print(f"✅ GPT генерация работает: {has_gpt_generated}")
        
        # Удаляем тестовую директорию
        shutil.rmtree(tenders_dir)
        print(f"🗑️ Удалена тестовая директория")
        
        return has_contacts and has_emails and has_correct_domain
        
    except Exception as e:
        print(f"❌ Ошибка теста: {e}")
        return False

def test_imports():
    """Тест импортов обновленных функций"""
    print("\n🧪 ТЕСТ ИМПОРТОВ ОБНОВЛЕННЫХ ФУНКЦИЙ")
    print("=" * 60)
    
    try:
        from leadgen_enhanced import (
            find_email_pattern_for_contacts,
            process_contacts_with_emails_new_logic,
            filter_personal_emails
        )
        
        print("✅ Все обновленные функции импортированы успешно:")
        print("  • find_email_pattern_for_contacts (унифицированная логика)")
        print("  • process_contacts_with_emails_new_logic (только GPT генерация)")
        print("  • filter_personal_emails (фильтрация служебных email)")
        
        return True
        
    except Exception as e:
        print(f"❌ Ошибка импорта: {e}")
        return False

async def main():
    """Главная функция тестирования"""
    print("🧪 ТЕСТИРОВАНИЕ УНИФИЦИРОВАННОЙ GPT ГЕНЕРАЦИИ EMAIL")
    print("=" * 80)
    
    # Сначала тестируем импорты
    imports_ok = test_imports()
    
    if not imports_ok:
        print("❌ Проблемы с импортами, остальные тесты пропущены")
        return
    
    # Асинхронные тесты
    async_tests = [
        ("Сценарий 1: Примеры из закупок", test_scenario_1_procurement_examples()),
        ("Сценарий 2: Примеры от GPT", test_scenario_2_gpt_examples()),
        ("Сценарий 3: Отсутствие примеров", test_scenario_3_hunter_examples()),
        ("Полный workflow с GPT генерацией", test_full_workflow_with_gpt_generation())
    ]
    
    print("\n📊 РЕЗУЛЬТАТЫ ТЕСТИРОВАНИЯ:")
    print("=" * 80)
    
    passed = 1 if imports_ok else 0  # Импорты уже протестированы
    print(f"  Импорты обновленных функций: {'✅ ПРОЙДЕН' if imports_ok else '❌ НЕ ПРОЙДЕН'}")
    
    # Асинхронные тесты
    for test_name, test_coro in async_tests:
        try:
            result = await test_coro
            status = "✅ ПРОЙДЕН" if result else "❌ НЕ ПРОЙДЕН"
            print(f"  {test_name}: {status}")
            if result:
                passed += 1
        except Exception as e:
            print(f"  {test_name}: ❌ НЕ ПРОЙДЕН ({e})")
    
    total_tests = len(async_tests) + 1
    print(f"\n🎯 ИТОГО: {passed}/{total_tests} тестов пройдено")
    
    if passed == total_tests:
        print("🎉 ВСЕ ТЕСТЫ ПРОЙДЕНЫ!")
        print("\n✅ УНИФИЦИРОВАННАЯ GPT ГЕНЕРАЦИЯ:")
        print("  • Все сценарии используют GPT генерацию по примеру")
        print("  • Стандартные паттерны полностью убраны")
        print("  • Без примеров = без email (не стреляем палкой в небо)")
        print("  • Умная фильтрация служебных email")
        print("  • Максимальная точность генерации для сложных случаев")
    elif passed > 2:
        print("⚠️  Частично работает - проверьте API ключи и логику")
    else:
        print("❌ Есть серьезные проблемы с унифицированной логикой")

if __name__ == "__main__":
    asyncio.run(main())
