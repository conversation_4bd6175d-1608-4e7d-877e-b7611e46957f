"""debug_full_process.py
Отладка полного процесса для выявления ошибки "4"
"""

import os
import sys
import asyncio

# Устанавливаем API ключ
api_key = os.getenv("OPENAI_API_KEY")
if not api_key:
    print("❌ Установите OPENAI_API_KEY")
    sys.exit(1)

# Предотвращаем запуск CLI
sys.argv = ['test']

async def debug_full_process():
    """Отладка полного процесса"""
    print("🔍 ОТЛАДКА ПОЛНОГО ПРОЦЕССА")
    print("=" * 50)
    
    try:
        from leadgen_enhanced import process_companies_batch
        
        # Параметры как в реальном запуске
        excel_path = "/Users/<USER>/Downloads/Копия test_test_test_crm.xlsx"
        template_path = "/Users/<USER>/Downloads/Шаблон письма Nova AI (теплый).pdf"
        nova_ai_path = "/Users/<USER>/Downloads/Позиционирование Nova AI.pdf"
        output_dir = "/tmp/debug_leadgen_output"
        tenders_dir = "/Users/<USER>/Downloads/kontur.zakupki_AI"
        
        # Проверяем файлы
        files_to_check = [
            (excel_path, "Excel"),
            (template_path, "Template"),
            (nova_ai_path, "Nova AI")
        ]
        
        for file_path, name in files_to_check:
            if os.path.exists(file_path):
                print(f"✅ {name}: найден")
            else:
                print(f"❌ {name}: НЕ найден")
                return False
        
        # Создаем выходную директорию
        os.makedirs(output_dir, exist_ok=True)
        
        print("\n🚀 Запуск process_companies_batch...")
        
        await process_companies_batch(
            excel_path=excel_path,
            template_pdf_path=template_path,
            nova_ai_pdf_path=nova_ai_path,
            output_dir=output_dir,
            tenders_dir=tenders_dir,
            company_column="jur_name",
            inn_column="ИНН",
            am_column="Клиентский менеджер"
        )
        
        print("✅ Процесс завершен успешно!")
        return True
        
    except Exception as e:
        print(f"❌ Ошибка: {type(e).__name__}: {e}")
        
        # Детальная диагностика
        error_str = str(e)
        if "4" in error_str:
            print("🔍 Ошибка содержит '4'")
        if "KeyError" in str(type(e).__name__):
            print("🔍 KeyError - проблема с доступом к ключу")
        if "list index out of range" in error_str:
            print("🔍 Проблема с индексом списка")
        if "tuple index out of range" in error_str:
            print("🔍 Проблема с индексом кортежа")
        
        # Полный traceback
        import traceback
        print("\n📋 Полный traceback:")
        traceback.print_exc()
        
        return False

def main():
    """Главная функция"""
    print("🧪 ОТЛАДКА ПОЛНОГО ПРОЦЕССА LEADGEN")
    print("=" * 70)
    
    success = asyncio.run(debug_full_process())
    
    print(f"\n📊 РЕЗУЛЬТАТ: {'✅ УСПЕХ' if success else '❌ ОШИБКА'}")

if __name__ == "__main__":
    main()
