# 🔄 Новая логика генерации email через GPT

## ❌ Что было неправильно в старой логике:

1. **Программная генерация email** - шаблоны создавались программно
2. **Нет контекста компании** - не учитывались реальные примеры корпоративных почт
3. **Один поиск для всех** - домен искался один раз, без учета специфики людей
4. **Нет проброса найденных имен** - GPT не знал о конкретных людях

## ✅ Новая правильная логика:

### **Шаг 1: Поиск информации о корпоративной почте (один раз для компании)**
```python
async def search_company_email_info(client, company_name):
    # GPT ищет:
    # 1. Почтовый домен компании (@company.ru)
    # 2. КОНКРЕТНЫЕ ПРИМЕРЫ email сотрудников
    # 3. Реальные шаблоны из публичных источников
```

**Пример результата:**
```
ДОМЕН: sberbank.ru
ПРИМЕРЫ:
- <EMAIL>
- <EMAIL>
- <EMAIL>
```

### **Шаг 2: Генерация персонального email (для каждого человека отдельно)**
```python
async def generate_personal_email(client, first_name, last_name, company_email_info):
    # GPT получает:
    # 1. Имя и фамилию конкретного человека
    # 2. Информацию о корпоративной почте компании
    # 3. Задачу составить email для этого человека
```

**Пример промпта:**
```
СОТРУДНИК:
- Имя: Александр (английский: aleksandr)
- Фамилия: Иванов (английский: ivanov)

ИНФОРМАЦИЯ О КОРПОРАТИВНОЙ ПОЧТЕ КОМПАНИИ:
ДОМЕН: sberbank.ru
ПРИМЕРЫ:
- <EMAIL>
- <EMAIL>

ЗАДАЧА: Составь email для Александра Иванова
```

**Результат:** `<EMAIL>`

## 🔧 Реализованные функции:

### 1. **`search_company_email_info()`**
- Ищет домен и примеры корпоративных почт
- Использует реальные источники (сайт, LinkedIn, пресс-релизы)
- Возвращает структурированную информацию

### 2. **`generate_personal_email()`**
- Генерирует email для конкретного человека
- Использует контекст информации о компании
- Применяет транслитерацию русских имен
- Извлекает email из ответа GPT

### 3. **`process_single_contact_with_email()`**
- Обрабатывает один контакт
- Расшифровывает частичные имена (если нужно)
- Генерирует email через GPT
- Валидирует полученный email

### 4. **`process_contacts_with_emails()`**
- Получает информацию о почте компании (один раз)
- Обрабатывает каждый контакт с этой информацией
- Возвращает итоговый список с email

## 🔄 Полный алгоритм работы:

```
1. Веб-поиск контактов → [имя, фамилия, должность]
2. Setka API поиск → [имя, фамилия, должность]
3. Объединение контактов
4. GPT: поиск домена и примеров почт компании (ОДИН РАЗ)
5. Для каждого контакта:
   a. Расшифровка частичных имен (если нужно)
   b. GPT: генерация персонального email
   c. Валидация email
   d. Формирование итоговой строки
```

## 📊 Примеры работы:

### **Поиск информации о компании:**
**Вход:** `"Сбербанк"`
**GPT находит:**
```
ДОМЕН: sberbank.ru
ПРИМЕРЫ:
- <EMAIL>
- <EMAIL>
- <EMAIL>
```

### **Генерация персонального email:**
**Вход:** 
- Имя: `Александр`
- Фамилия: `Иванов`
- Информация о компании: (см. выше)

**GPT генерирует:** `<EMAIL>`

### **Итоговый результат:**
```
• Иванов Александр — CTO (<EMAIL>)
• Петрова Мария — CIO (<EMAIL>)
```

## ✅ Преимущества новой логики:

1. **🎯 Контекстность** - GPT знает о реальных примерах почт компании
2. **🔍 Персонализация** - каждый email генерируется индивидуально
3. **📚 Реальные данные** - используются найденные примеры, а не шаблоны
4. **🌐 Правильная транслитерация** - русские имена корректно переводятся
5. **✅ Валидация** - каждый email проверяется на живучесть

## 🧪 Тестирование:

```bash
python test_gpt_email_generation.py
```

**Ожидаемые результаты:**
- ✅ Импорты новых функций
- ❌ API тесты (требуют правильный OPENAI_API_KEY)

## 🚀 Использование:

```bash
python leadgen_enhanced.py \
  --excel "companies.xlsx" \
  --template "template.pdf" \
  --nova-ai "nova_ai.pdf" \
  --output "./output" \
  --refresh-token "setka_token"
```

## 📋 Что изменилось в коде:

1. **Убрана** `search_email_domain_pattern()` (старая)
2. **Убрана** `generate_email_variants()` (программная генерация)
3. **Добавлена** `search_company_email_info()` (новая)
4. **Добавлена** `generate_personal_email()` (новая)
5. **Обновлена** `process_single_contact_with_email()` (использует GPT)
6. **Обновлена** `process_contacts_with_emails()` (новый workflow)

## ✅ Итог:

**Новая логика полностью реализована!** 

🎯 **Теперь GPT:**
- Ищет реальные примеры корпоративных почт
- Генерирует персональные email для каждого человека
- Использует контекст найденной информации о компании
- Применяет правильную транслитерацию

**Система готова к работе с правильным OPENAI_API_KEY!**
