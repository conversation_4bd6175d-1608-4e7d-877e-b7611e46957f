# 🔧 Исправленная логика поиска контактов

## ❌ Что было неправильно:

1. **JSON формат не поддерживался** с web_search моделями
2. **Русские имена в email** - символы должны быть на английском
3. **Полное название компании** для Setka API - нужно только "сердце"
4. **Refresh токен обрезался** - не показывался полностью

## ✅ Что исправлено:

### 1. **Убран JSON формат**
- Заменен на структурированный текстовый формат
- Добавлен парсер `parse_contacts_from_text()`
- Формат: `CONTACT_START ... CONTACT_END`

### 2. **Добавлена транслитерация**
- Функция `transliterate_to_english()` - русские имена → английские
- Email генерируются только на английском языке
- Пример: `Александр Иванов` → `<EMAIL>`

### 3. **Извлечение "сердца" компании**
- Используется `_extract_company_name_from_quotes()` для Setka API
- Убирает хвосты, оставляет содержимое кавычек
- Пример: `Федеральное ГБУ «Российский Фонд»` → `Российский Фонд`

### 4. **Полное сохранение refresh токена**
- Токен сохраняется в файл `/tmp/setka_refresh_token.txt`
- Полный токен выводится в консоль
- Можно использовать для следующих запусков

## 🔄 Правильный алгоритм работы:

### **Шаг 1: Поиск контактов**
```
Веб-поиск (GPT) → [имя, фамилия, должность] (русские)
     +
Setka API → [имя, фамилия, должность] (русские)
     ↓
Объединение и дедупликация
```

### **Шаг 2: Обработка каждого контакта**
```
Русские имена → Транслитерация → Английские имена
     ↓
Поиск домена компании (один раз для всех)
     ↓
Генерация email вариантов (на английском)
     ↓
Валидация каждого варианта
     ↓
Выбор первого валидного email
```

### **Шаг 3: Итоговый результат**
```
• Иванов Александр — CTO (<EMAIL>)
• Петрова Мария — CIO (<EMAIL>)
```

## 📊 Примеры работы:

### **Транслитерация:**
- `Александр` → `aleksandr`
- `Иванов` → `ivanov`
- `Сергей` → `sergey`
- `Щербаков` → `scherbakov`

### **Email генерация:**
- Имя: `Александр Иванов`
- Домен: `company.ru`
- Шаблоны: `["name.surname@domain", "n.surname@domain"]`
- Результат: `["<EMAIL>", "<EMAIL>"]`

### **Извлечение названия:**
- Полное: `Федеральное ГБУ «Российский Фонд Информации»`
- Сердце: `Российский Фонд Информации`
- Для Setka: используется сердце

## 🧪 Тестирование:

Запустите тест:
```bash
python test_corrected_logic.py
```

**Результат:** ✅ 4/4 тестов пройдено

## 🚀 Использование:

```bash
python leadgen_enhanced.py \
  --excel "companies.xlsx" \
  --template "template.pdf" \
  --nova-ai "nova_ai.pdf" \
  --output "./output" \
  --refresh-token "your_setka_token"
```

## 📁 Файлы refresh токена:

- **Сохраняется в:** `/tmp/setka_refresh_token.txt`
- **Выводится в консоль:** полный токен для копирования
- **Автоматически обновляется** при каждом запросе к Setka API

## ✅ Итог:

**Все проблемы исправлены!** Логика поиска контактов теперь работает правильно:

1. ✅ **Структурированный текст** вместо JSON
2. ✅ **Транслитерация** русских имен в английский для email
3. ✅ **"Сердце" компании** для Setka API поиска
4. ✅ **Полное сохранение** refresh токена
5. ✅ **Одинаковая обработка** для веб-поиска и Setka API
6. ✅ **Email на английском языке** с правильной валидацией

**Система готова к работе с правильным OPENAI_API_KEY!**
