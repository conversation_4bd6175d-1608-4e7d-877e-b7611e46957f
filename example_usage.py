"""example_usage.py
-----------------------------------------------------------------------
Пример использования функции поиска сотрудников компании
-----------------------------------------------------------------------
"""

import asyncio
import os
from company_search import search_company_employees_sync, search_company_employees

def example_sync():
    """Пример синхронного использования"""
    print("=" * 60)
    print("СИНХРОННЫЙ ПРИМЕР ИСПОЛЬЗОВАНИЯ")
    print("=" * 60)
    
    # Настройки
    company_name = "Автоваз"
    access_token = os.getenv("SETKA_ACCESS_TOKEN", "your_access_token_here")
    output_dir = "./example_reports"
    
    print(f"Компания: {company_name}")
    print(f"Токен: {access_token[:20]}..." if len(access_token) > 20 else access_token)
    print(f"Папка отчетов: {output_dir}")
    print()
    
    try:
        # Выполняем поиск
        employees = search_company_employees_sync(
            company_name=company_name,
            access_token=access_token,
            output_dir=output_dir
        )
        
        # Выводим результаты
        print(f"\n✅ Поиск завершен успешно!")
        print(f"📊 Найдено сотрудников: {len(employees)}")
        
        if employees:
            print("\n👥 Список найденных сотрудников:")
            for i, (last_name, first_name, position) in enumerate(employees, 1):
                print(f"  {i:2d}. {last_name} {first_name}")
                print(f"      Должность: {position}")
                print()
        
        print(f"📄 Отчет сохранен в папке: {output_dir}")
        
    except Exception as e:
        print(f"❌ Ошибка: {e}")
        print("\n💡 Возможные причины:")
        print("  - Неверный или истекший токен доступа")
        print("  - Компания не найдена в базе Setka.ru")
        print("  - Отсутствует интернет-соединение")
        print("  - У компании нет Networks")

async def example_async():
    """Пример асинхронного использования"""
    print("=" * 60)
    print("АСИНХРОННЫЙ ПРИМЕР ИСПОЛЬЗОВАНИЯ")
    print("=" * 60)
    
    # Настройки
    company_name = "Сбербанк"
    access_token = os.getenv("SETKA_ACCESS_TOKEN", "your_access_token_here")
    output_dir = "./async_reports"
    
    # Кастомные ключевые слова для фильтрации
    custom_keywords = [
        "директор", "ИТ", "IT", "CIO", "CTO", "CDO",
        "информационных технологий", "цифровых технологий",
        "цифровизации", "автоматизации", "разработки",
        "архитектор", "технический директор"
    ]
    
    print(f"Компания: {company_name}")
    print(f"Токен: {access_token[:20]}..." if len(access_token) > 20 else access_token)
    print(f"Папка отчетов: {output_dir}")
    print(f"Ключевые слова: {', '.join(custom_keywords[:5])}...")
    print()
    
    try:
        # Выполняем асинхронный поиск
        employees = await search_company_employees(
            company_name=company_name,
            access_token=access_token,
            output_dir=output_dir,
            keywords=custom_keywords
        )
        
        # Анализируем результаты
        print(f"\n✅ Асинхронный поиск завершен!")
        print(f"📊 Найдено сотрудников: {len(employees)}")
        
        if employees:
            # Группируем по типам должностей
            directors = [emp for emp in employees if "директор" in emp[2].lower()]
            it_specialists = [emp for emp in employees if any(keyword in emp[2].lower() 
                            for keyword in ["ит", "it", "информационных технологий"])]
            c_level = [emp for emp in employees if any(keyword in emp[2].lower() 
                      for keyword in ["cio", "cto", "cdo"])]
            
            print(f"\n📈 Анализ по категориям:")
            print(f"  👔 Директора: {len(directors)}")
            print(f"  💻 ИТ-специалисты: {len(it_specialists)}")
            print(f"  🎯 C-level: {len(c_level)}")
            
            print(f"\n👥 Топ-5 найденных сотрудников:")
            for i, (last_name, first_name, position) in enumerate(employees[:5], 1):
                print(f"  {i}. {last_name} {first_name} - {position}")
        
        print(f"\n📄 Отчет сохранен в папке: {output_dir}")
        
    except Exception as e:
        print(f"❌ Ошибка: {e}")

def example_multiple_companies():
    """Пример поиска по нескольким компаниям"""
    print("=" * 60)
    print("ПРИМЕР ПОИСКА ПО НЕСКОЛЬКИМ КОМПАНИЯМ")
    print("=" * 60)
    
    companies = [
        "Автоваз",
        "Сбербанк", 
        "Яндекс",
        "Mail.ru Group",
        "Тинькофф"
    ]
    
    access_token = os.getenv("SETKA_ACCESS_TOKEN", "your_access_token_here")
    output_dir = "./multi_company_reports"
    
    print(f"Компании для поиска: {', '.join(companies)}")
    print(f"Токен: {access_token[:20]}..." if len(access_token) > 20 else access_token)
    print(f"Папка отчетов: {output_dir}")
    print()
    
    results = {}
    
    for i, company in enumerate(companies, 1):
        print(f"[{i}/{len(companies)}] Поиск в компании: {company}")
        
        try:
            employees = search_company_employees_sync(
                company_name=company,
                access_token=access_token,
                output_dir=output_dir
            )
            
            results[company] = employees
            print(f"  ✅ Найдено: {len(employees)} сотрудников")
            
        except Exception as e:
            print(f"  ❌ Ошибка: {e}")
            results[company] = []
        
        print()
    
    # Сводка результатов
    print("=" * 60)
    print("СВОДКА РЕЗУЛЬТАТОВ")
    print("=" * 60)
    
    total_employees = 0
    for company, employees in results.items():
        count = len(employees)
        total_employees += count
        status = "✅" if count > 0 else "❌"
        print(f"{status} {company:<20} {count:>3} сотрудников")
    
    print(f"\n📊 Всего найдено: {total_employees} сотрудников")
    print(f"📁 Отчеты сохранены в: {output_dir}")

def setup_environment():
    """Настройка окружения и проверка зависимостей"""
    print("🔧 Проверка окружения...")
    
    # Проверяем зависимости
    try:
        import aiohttp
        print("✅ aiohttp установлен")
    except ImportError:
        print("❌ aiohttp не установлен. Выполните: pip install aiohttp")
        return False
    
    try:
        from docx import Document
        print("✅ python-docx установлен")
    except ImportError:
        print("❌ python-docx не установлен. Выполните: pip install python-docx")
        return False
    
    # Проверяем токен
    token = os.getenv("SETKA_ACCESS_TOKEN")
    if token and token != "your_access_token_here":
        print(f"✅ Токен найден: {token[:20]}...")
    else:
        print("⚠️  Токен не найден в переменной окружения SETKA_ACCESS_TOKEN")
        print("   Установите токен: export SETKA_ACCESS_TOKEN='your_token'")
        print("   Или замените 'your_access_token_here' в коде на реальный токен")
    
    return True

def main():
    """Главная функция с меню выбора примеров"""
    print("🚀 ПРИМЕРЫ ИСПОЛЬЗОВАНИЯ ФУНКЦИИ ПОИСКА СОТРУДНИКОВ")
    print("=" * 60)
    
    if not setup_environment():
        print("\n❌ Не все зависимости установлены. Установите их и попробуйте снова.")
        return
    
    print("\nВыберите пример для запуска:")
    print("1. Синхронный поиск (одна компания)")
    print("2. Асинхронный поиск (одна компания)")
    print("3. Поиск по нескольким компаниям")
    print("4. Выход")
    
    while True:
        try:
            choice = input("\nВведите номер (1-4): ").strip()
            
            if choice == "1":
                example_sync()
                break
            elif choice == "2":
                asyncio.run(example_async())
                break
            elif choice == "3":
                example_multiple_companies()
                break
            elif choice == "4":
                print("👋 До свидания!")
                break
            else:
                print("❌ Неверный выбор. Введите число от 1 до 4.")
                
        except KeyboardInterrupt:
            print("\n\n👋 Выход по Ctrl+C")
            break
        except Exception as e:
            print(f"❌ Ошибка: {e}")

if __name__ == "__main__":
    main()
