#!/usr/bin/env python3
"""
Тест для проверки поиска по закупкам без ИНН в названии компании
"""

import pandas as pd
import tempfile
import os
from leadgen_enhanced import _scan_tenders

def test_tenders_search():
    """Тест поиска по закупкам с чистым названием компании"""
    
    # Создаем тестовые данные закупок
    test_data = []
    
    # Создаем 30 столбцов (A-AD), столбец AB (27) содержит названия компаний
    for i in range(5):  # 5 строк данных
        row = [''] * 30  # 30 пустых столбцов
        if i == 0:
            # Заголовки
            row[27] = "Заказчик"  # Столбец AB
        elif i == 1:
            row[27] = "ООО Тестовая Компания"  # Точное совпадение
        elif i == 2:
            row[27] = "АО Другая Компания"
        elif i == 3:
            row[27] = "ООО ТЕСТОВАЯ КОМПАНИЯ"  # Совпадение с разным регистром
        elif i == 4:
            row[27] = "ИП Иванов"
        
        # Добавляем данные в другие столбцы
        if i > 0:  # Не для заголовка
            row[2] = f"Закупка {i}"  # Столбец C - название закупки
            row[3] = f"{1000000 * i}"  # Столбец D - цена
            row[11] = f"2024-01-{i:02d}"  # Столбец L - дата
        
        test_data.append(row)
    
    # Создаем временный Excel файл
    with tempfile.NamedTemporaryFile(suffix='.xlsx', delete=False) as tmp_file:
        df = pd.DataFrame(test_data)
        df.to_excel(tmp_file.name, index=False, header=False, engine='openpyxl')
        tmp_path = tmp_file.name
    
    # Создаем временную директорию для тендеров
    with tempfile.TemporaryDirectory() as tmp_dir:
        # Перемещаем файл в директорию
        final_path = os.path.join(tmp_dir, "tenders.xlsx")
        os.rename(tmp_path, final_path)
        
        try:
            print("🧪 Тестирование поиска по закупкам...")
            
            # Тест 1: Поиск с точным названием (без ИНН)
            print("\n📋 Тест 1: Поиск 'ООО Тестовая Компания'")
            result1 = _scan_tenders(tmp_dir, "ООО Тестовая Компания")
            print(f"Результат: {result1}")
            
            # Тест 2: Поиск с названием + ИНН (должен НЕ найти)
            print("\n📋 Тест 2: Поиск 'ООО Тестовая Компания (ИНН: 1234567890)'")
            result2 = _scan_tenders(tmp_dir, "ООО Тестовая Компания (ИНН: 1234567890)")
            print(f"Результат: {result2}")
            
            # Тест 3: Поиск несуществующей компании
            print("\n📋 Тест 3: Поиск 'Несуществующая Компания'")
            result3 = _scan_tenders(tmp_dir, "Несуществующая Компания")
            print(f"Результат: {result3}")
            
            # Проверяем результаты
            success = True
            
            if "Не найдено" in result1:
                print("❌ Ошибка: Не найдена компания с точным названием")
                success = False
            else:
                print("✅ Найдена компания с точным названием")
            
            if "Не найдено" not in result2:
                print("❌ Ошибка: Найдена компания с ИНН в названии (не должна)")
                success = False
            else:
                print("✅ Правильно: Не найдена компания с ИНН в названии")
            
            if "Не найдено" not in result3:
                print("❌ Ошибка: Найдена несуществующая компания")
                success = False
            else:
                print("✅ Правильно: Не найдена несуществующая компания")
            
            return success
            
        except Exception as e:
            print(f"💥 Ошибка при выполнении теста: {e}")
            return False

if __name__ == "__main__":
    print("🚀 Запуск теста поиска по закупкам...")
    
    try:
        if test_tenders_search():
            print("\n🎉 Тест поиска по закупкам пройден успешно!")
        else:
            print("\n❌ Тест поиска по закупкам не пройден!")
            
    except Exception as e:
        print(f"\n💥 Ошибка при выполнении теста: {e}")
        import traceback
        traceback.print_exc()
