"""test_tenders_directory.py
Тест анализа закупок из директории --tenders
"""

import os
import sys
import asyncio
import pandas as pd
import tempfile
import shutil

# Устанавливаем API ключи для тестирования
openai_api_key = os.getenv("OPENAI_API_KEY")

if not openai_api_key:
    print("❌ Установите OPENAI_API_KEY для тестирования")
    sys.exit(1)

# Предотвращаем запуск CLI
sys.argv = ['test']

def create_test_tenders_directory():
    """Создаем тестовую директорию с Excel файлами закупок"""
    print("📁 Создание тестовой директории с закупками...")
    
    # Создаем временную директорию
    tenders_dir = tempfile.mkdtemp(prefix="test_tenders_")
    
    # Тестовые данные для разных файлов
    test_files = [
        {
            "filename": "zakupki_2023_q1.xlsx",
            "data": {
                'A': ['Закупка IT услуг', 'Закупка оборудования', 'Закупка консультаций'],
                'B': ['ООО "Техно"', 'Российский Фонд Информации по Природным Ресурсам', 'ИП Иванов'],
                'C': ['Поставщик 1', 'Поставщик 2', 'Поставщик 3'],
                'V': [
                    'Контакт: Менеджер, тел: +7(495)123-45-67',
                    'Ответственный: Стройков Валерий, email: <EMAIL>, тел: +7(495)987-65-43',
                    'Связь: <EMAIL>'
                ]
            }
        },
        {
            "filename": "zakupki_2023_q2.xlsx", 
            "data": {
                'A': ['Закупка ПО', 'Закупка услуг', 'Закупка материалов', 'Закупка работ'],
                'B': ['ООО "Софт"', 'РФИ МНР', 'ООО "Материалы"', 'Российский Фонд Информации'],
                'C': ['Поставщик A', 'Поставщик B', 'Поставщик C', 'Поставщик D'],
                'V': [
                    'Менеджер: Петров, email: <EMAIL>',
                    'Контакт: Иванова Наталия, email: <EMAIL>, доп: <EMAIL>',
                    'Связь: <EMAIL>',
                    'Ответственный: Российский Евгений, email: <EMAIL>'
                ]
            }
        },
        {
            "filename": "zakupki_2023_q3.xlsx",  # Используем .xlsx формат
            "data": {
                'A': ['Закупка услуг связи', 'Закупка транспорта'],
                'B': ['ООО "Связь"', 'Другая компания'],
                'C': ['Поставщик X', 'Поставщик Y'],
                'V': [
                    'Контакт: <EMAIL>',
                    'Менеджер: Сидоров, тел: +7(495)555-55-55'
                ]
            }
        }
    ]
    
    # Создаем файлы
    for file_info in test_files:
        file_path = os.path.join(tenders_dir, file_info["filename"])
        df = pd.DataFrame(file_info["data"])
        df.to_excel(file_path, index=False)
        print(f"  ✅ Создан файл: {file_info['filename']} ({len(df)} строк)")
    
    print(f"✅ Создана тестовая директория: {tenders_dir}")
    print(f"📊 Файлов в директории: {len(test_files)}")
    
    return tenders_dir

async def test_tenders_directory_analysis():
    """Тест анализа закупок из директории"""
    print("\n🧪 ТЕСТ АНАЛИЗА ЗАКУПОК ИЗ ДИРЕКТОРИИ")
    print("=" * 60)
    
    try:
        from leadgen_enhanced import analyze_procurement_data
        import openai
        
        client = openai.AsyncClient(api_key=openai_api_key)
        
        # Создаем тестовую директорию
        tenders_dir = create_test_tenders_directory()
        
        company_name = "Российский Фонд Информации по Природным Ресурсам"
        
        print(f"🏢 Компания: {company_name}")
        print(f"📁 Директория: {tenders_dir}")
        
        result = await analyze_procurement_data(client, company_name, tenders_dir)
        
        print(f"\n📊 Результат анализа директории:")
        print(f"  Домен: {result.get('domain', '')}")
        print(f"  Паттерн: {result.get('pattern', '')}")
        print(f"  Пример: {result.get('example', '')}")
        
        # Проверяем результат
        has_domain = bool(result.get('domain'))
        has_example = bool(result.get('example'))
        correct_domain = 'rfimnr.ru' in result.get('domain', '')
        
        print(f"\n✅ Домен найден: {has_domain}")
        print(f"✅ Пример найден: {has_example}")
        print(f"✅ Правильный домен: {correct_domain}")
        
        # Удаляем тестовую директорию
        shutil.rmtree(tenders_dir)
        print(f"🗑️ Удалена тестовая директория: {tenders_dir}")
        
        return has_domain and has_example and correct_domain
        
    except Exception as e:
        print(f"❌ Ошибка теста: {e}")
        return False

async def test_multiple_files_processing():
    """Тест обработки множественных файлов"""
    print("\n🧪 ТЕСТ ОБРАБОТКИ МНОЖЕСТВЕННЫХ ФАЙЛОВ")
    print("=" * 60)
    
    try:
        from leadgen_enhanced import analyze_procurement_data
        import openai
        
        client = openai.AsyncClient(api_key=openai_api_key)
        
        # Создаем тестовую директорию
        tenders_dir = create_test_tenders_directory()
        
        # Тестируем с разными вариантами названия компании
        test_companies = [
            "Российский Фонд Информации по Природным Ресурсам",  # Полное название
            "РФИ МНР",  # Сокращение
            "Российский Фонд Информации"  # Частичное название
        ]
        
        all_results = []
        
        for company_name in test_companies:
            print(f"\n🏢 Тестируем компанию: {company_name}")
            
            result = await analyze_procurement_data(client, company_name, tenders_dir)
            
            has_domain = bool(result.get('domain'))
            has_example = bool(result.get('example'))
            
            print(f"  Домен найден: {'✅' if has_domain else '❌'}")
            print(f"  Пример найден: {'✅' if has_example else '❌'}")
            
            all_results.append(has_domain and has_example)
        
        # Удаляем тестовую директорию
        shutil.rmtree(tenders_dir)
        print(f"\n🗑️ Удалена тестовая директория")
        
        # Проверяем, что хотя бы один вариант сработал
        success = any(all_results)
        print(f"\n📊 Результат: {sum(all_results)}/{len(all_results)} вариантов успешно")
        
        return success
        
    except Exception as e:
        print(f"❌ Ошибка теста: {e}")
        return False

async def test_empty_directory():
    """Тест обработки пустой директории"""
    print("\n🧪 ТЕСТ ОБРАБОТКИ ПУСТОЙ ДИРЕКТОРИИ")
    print("=" * 60)
    
    try:
        from leadgen_enhanced import analyze_procurement_data
        import openai
        
        client = openai.AsyncClient(api_key=openai_api_key)
        
        # Создаем пустую директорию
        empty_dir = tempfile.mkdtemp(prefix="test_empty_")
        
        company_name = "Тестовая Компания"
        
        print(f"🏢 Компания: {company_name}")
        print(f"📁 Пустая директория: {empty_dir}")
        
        result = await analyze_procurement_data(client, company_name, empty_dir)
        
        print(f"\n📊 Результат анализа пустой директории:")
        print(f"  Домен: {result.get('domain', '')}")
        print(f"  Паттерн: {result.get('pattern', '')}")
        print(f"  Пример: {result.get('example', '')}")
        
        # Проверяем, что результат пустой
        is_empty = not any([result.get('domain'), result.get('pattern'), result.get('example')])
        
        print(f"\n✅ Корректно обработана пустая директория: {is_empty}")
        
        # Удаляем тестовую директорию
        shutil.rmtree(empty_dir)
        print(f"🗑️ Удалена пустая директория")
        
        return is_empty
        
    except Exception as e:
        print(f"❌ Ошибка теста: {e}")
        return False

async def test_nonexistent_directory():
    """Тест обработки несуществующей директории"""
    print("\n🧪 ТЕСТ ОБРАБОТКИ НЕСУЩЕСТВУЮЩЕЙ ДИРЕКТОРИИ")
    print("=" * 60)
    
    try:
        from leadgen_enhanced import analyze_procurement_data
        import openai
        
        client = openai.AsyncClient(api_key=openai_api_key)
        
        nonexistent_dir = "/path/that/does/not/exist"
        company_name = "Тестовая Компания"
        
        print(f"🏢 Компания: {company_name}")
        print(f"📁 Несуществующая директория: {nonexistent_dir}")
        
        result = await analyze_procurement_data(client, company_name, nonexistent_dir)
        
        print(f"\n📊 Результат анализа несуществующей директории:")
        print(f"  Домен: {result.get('domain', '')}")
        print(f"  Паттерн: {result.get('pattern', '')}")
        print(f"  Пример: {result.get('example', '')}")
        
        # Проверяем, что результат пустой
        is_empty = not any([result.get('domain'), result.get('pattern'), result.get('example')])
        
        print(f"\n✅ Корректно обработана несуществующая директория: {is_empty}")
        
        return is_empty
        
    except Exception as e:
        print(f"❌ Ошибка теста: {e}")
        return False

def test_imports():
    """Тест импортов обновленных функций"""
    print("\n🧪 ТЕСТ ИМПОРТОВ ОБНОВЛЕННЫХ ФУНКЦИЙ")
    print("=" * 60)
    
    try:
        from leadgen_enhanced import (
            analyze_procurement_data,
            find_email_pattern_for_contacts,
            process_contacts_with_emails_new_logic
        )
        
        print("✅ Все обновленные функции импортированы успешно:")
        print("  • analyze_procurement_data (работает с директорией)")
        print("  • find_email_pattern_for_contacts (принимает tenders_dir)")
        print("  • process_contacts_with_emails_new_logic (принимает tenders_dir)")
        
        return True
        
    except Exception as e:
        print(f"❌ Ошибка импорта: {e}")
        return False

async def main():
    """Главная функция тестирования"""
    print("🧪 ТЕСТИРОВАНИЕ АНАЛИЗА ЗАКУПОК ИЗ ДИРЕКТОРИИ --tenders")
    print("=" * 80)
    
    # Сначала тестируем импорты
    imports_ok = test_imports()
    
    if not imports_ok:
        print("❌ Проблемы с импортами, остальные тесты пропущены")
        return
    
    # Асинхронные тесты
    async_tests = [
        ("Анализ закупок из директории", test_tenders_directory_analysis()),
        ("Обработка множественных файлов", test_multiple_files_processing()),
        ("Обработка пустой директории", test_empty_directory()),
        ("Обработка несуществующей директории", test_nonexistent_directory())
    ]
    
    print("\n📊 РЕЗУЛЬТАТЫ ТЕСТИРОВАНИЯ:")
    print("=" * 80)
    
    passed = 1 if imports_ok else 0  # Импорты уже протестированы
    print(f"  Импорты обновленных функций: {'✅ ПРОЙДЕН' if imports_ok else '❌ НЕ ПРОЙДЕН'}")
    
    # Асинхронные тесты
    for test_name, test_coro in async_tests:
        try:
            result = await test_coro
            status = "✅ ПРОЙДЕН" if result else "❌ НЕ ПРОЙДЕН"
            print(f"  {test_name}: {status}")
            if result:
                passed += 1
        except Exception as e:
            print(f"  {test_name}: ❌ НЕ ПРОЙДЕН ({e})")
    
    total_tests = len(async_tests) + 1
    print(f"\n🎯 ИТОГО: {passed}/{total_tests} тестов пройдено")
    
    if passed == total_tests:
        print("🎉 ВСЕ ТЕСТЫ ПРОЙДЕНЫ!")
        print("\n✅ РАБОТА С ДИРЕКТОРИЕЙ --tenders:")
        print("  • Автоматический поиск Excel файлов в директории")
        print("  • Обработка множественных файлов (.xlsx)")
        print("  • Поиск компании во всех файлах")
        print("  • Извлечение email из всех найденных записей")
        print("  • Корректная обработка пустых/несуществующих директорий")
        print("  • Интеграция с существующей системой через --tenders")
    elif passed > 2:
        print("⚠️  Частично работает - проверьте API ключи")
    else:
        print("❌ Есть серьезные проблемы с обработкой директории")

if __name__ == "__main__":
    asyncio.run(main())
