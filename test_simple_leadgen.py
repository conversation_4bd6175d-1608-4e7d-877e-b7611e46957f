"""test_simple_leadgen.py
Простой тест для проверки исправления ошибки "4"
"""

import os
import sys
import asyncio

# Добавляем текущую директорию в путь
sys.path.insert(0, '.')

def test_import():
    """Тест импорта"""
    print("🔍 ТЕСТ ИМПОРТА")
    print("=" * 50)
    
    try:
        from leadgen_enhanced import SECTIONS, SECTION_QUERIES
        print("✅ Импорт успешен")
        
        print(f"📋 SECTIONS: {SECTIONS}")
        print(f"📝 SECTION_QUERIES keys: {list(SECTION_QUERIES.keys())}")
        
        # Проверяем проблемную секцию
        if 3 in SECTIONS and 3 not in SECTION_QUERIES:
            print("⚠️  Секция 3 есть в SECTIONS, но нет в SECTION_QUERIES")
        
        return True
    except Exception as e:
        print(f"❌ Ошибка импорта: {e}")
        return False

def test_collect_function():
    """Тест функции _collect"""
    print("\n🧪 ТЕСТ ФУНКЦИИ _collect")
    print("=" * 50)
    
    try:
        from leadgen_enhanced import _collect
        print("✅ Импорт _collect успешен")
        
        # Проверяем, что функция не вызывает ошибку при отсутствии API ключа
        # (должна упасть на API ключе, а не на KeyError)
        
        async def test_collect_async():
            try:
                result = await _collect("Тест", "", None)
                print("✅ _collect выполнена без ошибок")
                return result
            except ImportError as e:
                if "openai" in str(e):
                    print("✅ Ошибка openai (ожидаемо)")
                    return None
                else:
                    raise
            except EnvironmentError as e:
                if "OPENAI_API_KEY" in str(e):
                    print("✅ Ошибка API ключа (ожидаемо)")
                    return None
                else:
                    raise
            except KeyError as e:
                print(f"❌ KeyError: {e}")
                if "4" in str(e) or "3" in str(e):
                    print("❌ Проблема с секцией 3 или 4!")
                raise
            except Exception as e:
                print(f"❌ Неожиданная ошибка: {type(e).__name__}: {e}")
                raise
        
        # Запускаем тест
        result = asyncio.run(test_collect_async())
        return True
        
    except Exception as e:
        print(f"❌ Ошибка в тесте _collect: {type(e).__name__}: {e}")
        return False

def test_gather_section():
    """Тест функции _gather_section"""
    print("\n🔧 ТЕСТ ФУНКЦИИ _gather_section")
    print("=" * 50)
    
    try:
        from leadgen_enhanced import _gather_section
        print("✅ Импорт _gather_section успешен")
        
        # Тестируем с неправильным индексом
        async def test_gather_async():
            try:
                # Создаем фиктивный клиент
                class MockClient:
                    pass
                
                client = MockClient()
                
                # Пытаемся вызвать с индексом 3 (должно упасть с нашей ошибкой)
                result = await _gather_section(client, "Тест", "", 3)
                print("❌ Функция не упала на индексе 3!")
                return False
                
            except KeyError as e:
                if "Секция 3 не найдена в SECTION_QUERIES" in str(e):
                    print("✅ Правильная ошибка для секции 3")
                    return True
                else:
                    print(f"❌ Неправильная KeyError: {e}")
                    return False
            except Exception as e:
                print(f"❌ Неожиданная ошибка: {type(e).__name__}: {e}")
                return False
        
        result = asyncio.run(test_gather_async())
        return result
        
    except Exception as e:
        print(f"❌ Ошибка в тесте _gather_section: {type(e).__name__}: {e}")
        return False

def main():
    """Главная функция тестирования"""
    print("🧪 ПРОСТОЙ ТЕСТ LEADGEN ENHANCED")
    print("=" * 70)
    
    # Тест импорта
    import_ok = test_import()
    
    # Тест функции _collect
    collect_ok = test_collect_function()
    
    # Тест функции _gather_section
    gather_ok = test_gather_section()
    
    print("\n📊 РЕЗУЛЬТАТЫ ТЕСТИРОВАНИЯ:")
    print("=" * 50)
    print(f"  Импорт: {'✅' if import_ok else '❌'}")
    print(f"  _collect: {'✅' if collect_ok else '❌'}")
    print(f"  _gather_section: {'✅' if gather_ok else '❌'}")
    
    if all([import_ok, collect_ok, gather_ok]):
        print("\n🎉 ВСЕ ТЕСТЫ ПРОЙДЕНЫ!")
        print("Проблема с ошибкой '4' должна быть исправлена.")
    else:
        print("\n❌ ЕСТЬ ПРОБЛЕМЫ!")
        print("Нужно дополнительное исследование.")

if __name__ == "__main__":
    main()
