"""test_real_leadgen.py
Тест с реальными данными для проверки исправления
"""

import os
import asyncio
from leadgen_enhanced import process_companies_batch

async def test_real_processing():
    """Тест реальной обработки"""
    print("🧪 ТЕСТ РЕАЛЬНОЙ ОБРАБОТКИ")
    print("=" * 50)
    
    # Проверяем наличие файлов
    excel_path = "/Users/<USER>/Downloads/Копия test_test_test_crm.xlsx"
    template_path = "/Users/<USER>/Downloads/Шаблон письма Nova AI (теплый).pdf"
    nova_ai_path = "/Users/<USER>/Downloads/Позиционирование Nova AI.pdf"
    output_dir = "/tmp/test_leadgen_output"
    tenders_dir = "/Users/<USER>/Downloads/kontur.zakupki_AI"
    
    # Проверяем файлы
    files_to_check = [
        (excel_path, "Excel файл"),
        (template_path, "Шаблон письма"),
        (nova_ai_path, "Nova AI позиционирование")
    ]
    
    for file_path, name in files_to_check:
        if os.path.exists(file_path):
            print(f"✅ {name}: найден")
        else:
            print(f"❌ {name}: НЕ найден - {file_path}")
            return False
    
    # Проверяем API ключ
    api_key = os.getenv("OPENAI_API_KEY")
    if not api_key:
        print("❌ OPENAI_API_KEY не установлен")
        return False
    
    print(f"✅ API ключ: {api_key[:20]}...")
    
    # Создаем выходную директорию
    os.makedirs(output_dir, exist_ok=True)
    
    try:
        print("\n🚀 Запуск обработки...")
        await process_companies_batch(
            excel_path=excel_path,
            template_pdf_path=template_path,
            nova_ai_pdf_path=nova_ai_path,
            output_dir=output_dir,
            tenders_dir=tenders_dir,
            company_column="jur_name",
            inn_column="ИНН",
            am_column="Клиентский менеджер"
        )
        
        print("✅ Обработка завершена успешно!")
        return True
        
    except Exception as e:
        print(f"❌ Ошибка обработки: {type(e).__name__}: {e}")
        
        # Детальная диагностика
        error_str = str(e)
        if "4" in error_str:
            print("🔍 Ошибка содержит '4' - возможно проблема с секцией")
        if "KeyError" in str(type(e).__name__):
            print("🔍 KeyError - проблема с доступом к ключу")
        if "SECTION_QUERIES" in error_str:
            print("🔍 Ошибка связана с SECTION_QUERIES")
        
        return False

def main():
    """Главная функция"""
    print("🧪 ТЕСТ РЕАЛЬНОГО LEADGEN")
    print("=" * 70)
    
    # Устанавливаем API ключ для теста
    if not os.getenv("OPENAI_API_KEY"):
        print("⚠️  Установите OPENAI_API_KEY для полного тестирования")
        print("export OPENAI_API_KEY='your_key'")
        return
    
    # Запускаем тест
    success = asyncio.run(test_real_processing())
    
    print("\n📊 РЕЗУЛЬТАТ:")
    print("=" * 50)
    if success:
        print("🎉 ТЕСТ ПРОЙДЕН!")
        print("Проблема с ошибкой '4' исправлена.")
    else:
        print("❌ ТЕСТ НЕ ПРОЙДЕН!")
        print("Нужно дополнительное исследование.")

if __name__ == "__main__":
    main()
