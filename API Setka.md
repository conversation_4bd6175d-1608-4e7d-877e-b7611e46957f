# Token refresh
POST https://api.setka.ru/v1/oauth/refresh
## Request
### Body
```json
{
"refresh_token": "eyJhbGciOiJSUzI1NiIsImtpZCI6ImVlOWVkMWVhLTU3ODctNDUyZi05YmI1LTA0MzkwMGMyY2EzMCIsInR5cCI6IkpXVCJ9.********************************************************************************************************************************************************************************************************************************************.JSDYgBXsQhO5hG0fw5rh4MWJV9Chp911HmF9e2TEwbxMZiy-Y-F7-D4YBa3plsjHeCVSsMBEyJlpR_Uclv89BqVCBB39oKvIdF2Qq_3eKq26jv4jo5sA2hEChOt44Cla8GWQudzVH8JeqP37DsMcFFv3SKjV-fSPDlpcm_rMCTecpuE-hAWm3Jurqc5x_08KoWf4L9_c2dsucCH6ZzNdvv4twpIVQxttiwPOeYL8rRtrtGJDhItxAyjna9rH2vsJPi424E9ci5mKjz9E-tEh-xkHkXtXykPYuIlry3FR_rV7MwGlqDQo-nizVfrFs8waw__YMQyzWivcWjcNDC2meQ"
}
```
## Response
### Body
```json
{
"access_token": "eyJhbGciOiJSUzI1NiIsImtpZCI6IjgxZDM1ODA3LWM5MjctNDlhNi1hYTczLTExNDYyOGI5ZjdkMSIsInR5cCI6IkpXVCJ9.********************************************************************************************************************************************************************************************************************************************.SHq6iSpN-aA10bFKuyecKONQM38RwRkp0M2pu3j1cgaMcaGjm9GL5oGlRRKUO1S0vMLEpm7EXpNkOYHwhyqXKfwJXVrzeIU33ZA2BiRX3aHJNjTOZz0eRe4LKqPWMTyqhOCVULlX7r6yQiFggVPib5z2p9rjY9kwg3kyCmiU_oCYzLPXT0WRXXEwvwQ-uA_zLrUg8mmJX8ZNKGkXu2BWnQlYrlIZ5CW5o7nrICIw2ywHKHx7jKDNfQIhQpdCGulW7xJV1Jux5JyOZtO2j61h6I4STITChhEUE5IuMMGfwtv8VIBfJ93KCZtw8cj6GVui2W3MhQUF_8tuxCUVUnpcDw",
"refresh_token": "eyJhbGciOiJSUzI1NiIsImtpZCI6ImVlOWVkMWVhLTU3ODctNDUyZi05YmI1LTA0MzkwMGMyY2EzMCIsInR5cCI6IkpXVCJ9.********************************************************************************************************************************************************************************************************************************************.FOm3KFu_-1AAzkv6qe_qRH8ouMNqa1npHy1JUVYvpBdJFU5dKYkX6wkJDiE4P41vAXvbSF4Q1Yc9RFYacm9KL4Z3v_1GLZLRE3GCRZgVw_dN0lnKsuj5H2pyT-yhzuzDprL95QZDykhq2m9LHAR-qcvTY1-hXQwvbEMy4idk5shEXdZ4FRVjCVNkVLXOOYRhWOOtCoeekPbzU610Z5WhfBs8UoIccDukRmylSSFaK2694X4bKhEIVK09rX8CIHhGuodWJp_9i-ov44Dyo_oxE39wX8KWykQHFC8UQpk0hYJqnIhHkf4kusEAbCWvWi6tZT0M9Poz9sArICA0Y4U0Ag",
"access_token_expired_at": "2025-07-18T21:33:41.917542Z",
"refresh_token_expired_at": "2026-01-14T21:18:41.969644Z",
"token_type": "Bearer",
"cached": false
}
```
# Search
GET https://api.setka.ru/v1/search/all?text=Автоваз
## Request
### Headers
- `Authorization`: `Bearer ...`
## Response
### Body
```json
{
	"items": [
		{
			"type": "ACCOUNTS",
			"items": [
				{
					"uuid": "ae487bf2-8bc3-4856-bfd9-de9a78c19228",
					"id": "421417",
					"first_name": "Иван",
					"last_name": "Мангилёв",
					"description": null,
					"caption": null,
					"avatar": {
						"avatar_url": "https://api.setka.ru/uploads/01977e70-5828-4922-908c-7a4d87423a79",
						"autogenerated": false
					},
					"avatar_url": "https://api.setka.ru/uploads/01977e70-5828-4922-908c-7a4d87423a79",
					"is_current": false,
					"is_support_account": false,
					"is_finished": true,
					"name_required": false,
					"company": {
						"type": "COMPANY_SUGGEST_RESPONSE",
						"id": 22594,
						"uuid": "29e7d074-30da-4750-9195-0dfeb1b5180e",
						"name": "Автоваз",
						"logo_url": "https://api.setka.ru/uploads/0192c2fa-a2d0-4785-b573-544dca478663"
					},
					"position": {
						"type": "POSITION_RESPONSE",
						"id": null,
						"uuid": null,
						"name": "Тестировщик"
					},
					"position_title": "Инженер-тестировщик",
					"city": null,
					"place": null,
					"status_id": null,
					"status_description": null,
					"p2p_channel_id": null,
					"tags": [],
					"relations": null,
					"key_skills": null,
					"roles": null,
					"work_experiences": [
						{
							"id": "01977e73-9572-7049-aceb-9db3cb08488c",
							"company_position": {
								"company": {
									"type": "COMPANY_SUGGEST_RESPONSE",
									"id": 22594,
									"uuid": "29e7d074-30da-4750-9195-0dfeb1b5180e",
									"name": "Автоваз",
									"logo_url": "https://api.setka.ru/uploads/0192c2fa-a2d0-4785-b573-544dca478663"
								},
								"position": {
									"type": "POSITION_RESPONSE",
									"id": 149,
									"uuid": "fe35f240-d028-4f8b-a553-da9755808fc3",
									"name": "Тестировщик"
								},
								"position_title": "Инженер-тестировщик"
							},
							"start_year": 2024,
							"end_year": 2025,
							"start_month": 4,
							"end_month": 7,
							"work_currently": true,
							"index": null,
							"description": null,
							"industries": [],
							"is_private": false,
							"networks_count": 17,
							"state": "NOT_VERIFIED"
						},
						// ...
					],
					"total_experience_years": 11,
					"total_experience": {
						"years": 11,
						"months": 7
					},
					"has_complaint": false,
					"allow_target": null,
					"deleted": null,
					"share_url": "https://setka.ru/accounts/421417",
					"reverse_relations": null,
					"networks_count": 16,
					"relationship_strength": "WEAK",
					"relationship": {
						"shared_count": 0,
						"others_shared_count": 0,
						"all_count": 0,
						"relationship_strength": "WEAK",
						"members": []
					},
					"allow_mentions": true,
					"has_new_profile_views": true,
					"profile_view_setting": "ALL",
					"recruitment_process_status": null
				},
				// ..
			]
		},
		{
			"type": "COMMUNITIES",
			"items": [
				{
					"id": "018f1cd6-e4d8-405e-8129-f527a3c8ee1f",
					"name": "АвтоВАЗ",
					"current_participant": null,
					"tags": [],
					"last_messages": null,
					"first_messages": null,
					"total_messages_count": null,
					"unread_messages_count": 0,
					"untouched_posts_count": 0,
					"post_preview": null,
					"icon_url": "https://api.setka.ru/uploads/0192094d-7b7f-437d-b597-455dcddce626",
					"share_url": "https://setka.ru/communities/018f1cd6-e4d8-405e-8129-f527a3c8ee1f",
					"description": "Этот нейминг занят! Для связи по условиям освобождения пишите в телеграм @dlazyan",
					"favorited": false,
					"has_complaint": false,
					"parent_channel_id": null,
					"p2p_account": null,
					"subscriptions": [],
					"roles": [
						{
							"type": "CAN_WRITE_ANON"
						}
					],
					"settings": null,
					"is_question": false,
					"views": null,
					"accounts_who_commented": [],
					"accounts_who_liked": [],
					"displayed_author": "USER",
					"is_channel": true,
					"has_join_request": false,
					"verified": false,
					"verified_company_name": null,
					"verified_hh_employer_id": null,
					"verified_start_date": null,
					"verified_end_date": null,
					"allow_target": false,
					"deleted": false,
					"visibility_type": "PUBLIC",
					"post_type": "REGULAR",
					"snippets": [],
					"join_requests_count": 0,
					"repost_enabled": true,
					"subscribers_count": 1
				}
				// ...
			]
		},
		{
			"type": "NETWORKS",
			"items": [
				{
					"id": "e9791ecb-f7bb-470a-9b80-f36236f5cdfd",
					"name": "Автоваз",
					"type": "company",
					"icon": {
						"type": "URL",
						"icon": "https://api.setka.ru/uploads/0192c2fa-a2d0-4785-b573-544dca478663"
					},
					"members_count": 9551,
					"member_avatar_urls": [
						"https://api.setka.ru/v1/uploads/hh?t=**********&h=RpozIOFmfMJNoUDJrWYJzg&url_path=%2Fphoto%2F722015353.jpg",
						"https://api.setka.ru/v1/uploads/hh?t=**********&h=S2xFe-ilkcUGy4Z4QEBDxw&url_path=%2Fphoto%2F696569642.jpg",
						"https://api.setka.ru/v1/uploads/hh?t=**********&h=vpAFYZDSDZt8YQ3h6XupOQ&url_path=%2Fphoto%2F648555327.jpg",
						"https://api.setka.ru/v1/uploads/hh?t=**********&h=4Uk6gw7ojRZHzFLfG0lQow&url_path=%2Fphoto%2F634995369.jpeg"
					],
					"settings": null
				},
				// ...
			]
		},
		{
			"type": "POSTS",
			"items": [
				{
					"id": "0191e0c5-a5f7-4672-b841-b478e340425f",
					"name": null,
					"current_participant": null,
					"tags": [],
					"last_messages": null,
					"first_messages": [
						{
							"id": "0191e0c5-a601-4549-a234-908e2bbfce67",
							"text": "«Люблю» такие громкие заголовки:\n*АвтоВАЗ беспрецедентно обновил дизайн легендарной «Нивы». Как внедорожник выглядит теперь:*\n«АвтоВАЗ» впервые за долгое время обновил дизайн классической трехдверной «Нивы». У внедорожника LADA Niva Legend впервые появились светодиодные дневные ходовые огни.",
							"title": null,
							"img": [
								"https://api.setka.ru/uploads/0191e0c5-a501-4e0a-8855-50443aaac873"
							],
							"img_info": {
								"0191e0c5-a501-4e0a-8855-50443aaac873": {
									"size": {
										"width": 1077,
										"height": 816
									}
								}
							},
							"account_mentions": null,
							"dt": "2024-09-11T11:08:55.169000Z",
							"participant_id": "0191e0c5-a5fe-401f-a493-c6e7c8e799bc",
							"reactions": {
								"dislike": {
									"count": 0
								},
								"party": {
									"count": 0
								},
								"angry": {
									"count": 0
								},
								"shock": {
									"count": 0
								},
								"cry": {
									"count": 0
								},
								"lol": {
									"count": 0
								},
								"like": {
									"count": 0
								}
							},
							"current_account_reaction_id": null,
							"reply_to_message": null,
							"thread_channel_id": null,
							"has_complaint": false,
							"deleted": false,
							"channel_id": "0191e0c5-a5f7-4672-b841-b478e340425f",
							"type": "POST",
							"files": null,
							"last_edit_time": null,
							"displayed_author": "RESOURCE_OWNER",
							"displayed_author_id": null,
							"visibility_type": null,
							"display": null,
							"comments_allowed": true
						}
					],
					"total_messages_count": 1,
					"unread_messages_count": 1,
					"untouched_posts_count": null,
					"post_preview": null,
					"icon_url": null,
					"share_url": "https://setka.ru/channels/0191e0c5-a5f7-4672-b841-b478e340425f",
					"description": null,
					"favorited": false,
					"has_complaint": false,
					"parent_channel_id": "********-6b8c-4c91-b8e5-af9426f6b048",
					"p2p_account": null,
					"subscriptions": [],
					"roles": [
						{
							"type": "CAN_WRITE_MESSAGE"
						},
						{
							"type": "CAN_WRITE_ANON"
						}
					],
					"settings": null,
					"is_question": false,
					"views": 20,
					"accounts_who_commented": [],
					"accounts_who_liked": [],
					"displayed_author": "RESOURCE_OWNER",
					"is_channel": null,
					"has_join_request": null,
					"verified": null,
					"verified_company_name": null,
					"verified_hh_employer_id": null,
					"verified_start_date": null,
					"verified_end_date": null,
					"allow_target": null,
					"deleted": false,
					"visibility_type": "PUBLIC",
					"post_type": "REGULAR",
					"snippets": [],
					"join_requests_count": 0,
					"repost_enabled": true,
					"subscribers_count": null,
					"repost_of": null,
					"reposts_count": null
				},
				// ...
			]
		},
		{
			"type": "VACANCIES",
			"items": []
		},
		{
			"type": "RESUMES",
			"items": []
		}
	]
}
```
# Get Network participants
GET https://api.setka.ru/v2/networks/e9791ecb-f7bb-470a-9b80-f36236f5cdfd/members/search?limit=1000&type=working_now&query=&cursor=
## Request
### Headers
- `Authorization`: `Bearer ...`
## Response
### Body
```json
{
	"members": [
		{
			"status_id": null,
			"status_description": null,
			"id": "64949",
			"uuid": "73bf5e36-09de-4227-b1a7-df4fb782d8f1",
			"resume_storage_id": "4:a7ce3f55-5f54-44f7-bf76-bc80af6e7392:10298405",
			"avatar_url": "https://api.setka.ru/uploads/01907c4e-dcf3-4803-9cd7-908b4b34f5c2",
			"company": {
				"id": "29e7d074-30da-4750-9195-0dfeb1b5180e",
				"name": "Автоваз",
				"logo_url": "https://api.setka.ru/uploads/0192c2fa-a2d0-4785-b573-544dca478663"
			},
			"position": {
				"id": null,
				"name": "Руководитель проекта ИТ"
			},
			"first_name": "Родион",
			"last_name": "Антонов",
			"relative_networks_count": 0,
			"network_state": "REGISTERED",
			"relations": [],
			"relationship_strength": null,
			"relationship": null
		},
		// ...
	],
}
```