"""test_short_email_filter.py
Тест фильтрации коротких email адресов (2 символа и меньше)
"""

import os
import sys

# Предотвращаем запуск CLI
sys.argv = ['test']

def test_short_email_filtering():
    """Тест фильтрации коротких email адресов"""
    print("\n🧪 ТЕСТ ФИЛЬТРАЦИИ КОРОТКИХ EMAIL АДРЕСОВ")
    print("=" * 60)
    
    try:
        from leadgen_enhanced import filter_personal_emails
        
        # Тестовые email с короткими локальными частями
        test_emails = [
            # Короткие (должны быть отфильтрованы)
            "<EMAIL>",           # 2 символа
            "<EMAIL>",        # 2 символа  
            "<EMAIL>",        # 2 символа
            "<EMAIL>",        # 2 символа
            "<EMAIL>",         # 1 символ
            "<EMAIL>",         # 1 символ
            
            # Нормальные (должны пройти)
            "<EMAIL>",      # 4 символа
            "<EMAIL>",    # 6 символов
            "<EMAIL>",  # 8 символов (с точкой)
            "<EMAIL>",       # 3 символа (граничный случай)
            
            # Служебные (должны быть отфильтрованы по ключевым словам)
            "<EMAIL>",   # служебное слово
            "<EMAIL>",      # служебное слово
        ]
        
        print(f"📧 Тестовые email: {len(test_emails)}")
        for i, email in enumerate(test_emails, 1):
            local_part = email.split('@')[0]
            print(f"  {i:2d}. {email} (локальная часть: '{local_part}' - {len(local_part)} символов)")
        
        print(f"\n🔍 Фильтрация...")
        personal_emails, domain = filter_personal_emails(test_emails)
        
        print(f"\n📊 Результаты фильтрации:")
        print(f"  Входных email: {len(test_emails)}")
        print(f"  Персональных email: {len(personal_emails)}")
        print(f"  Найденный домен: {domain}")
        
        print(f"\n✅ Прошедшие фильтрацию:")
        for email in personal_emails:
            local_part = email.split('@')[0]
            print(f"  • {email} ('{local_part}' - {len(local_part)} символов)")
        
        # Проверяем результаты
        expected_personal = [
            "<EMAIL>",
            "<EMAIL>", 
            "<EMAIL>",
            "<EMAIL>"  # 3 символа - должен пройти
        ]
        
        # Проверяем, что короткие email отфильтрованы
        short_emails_filtered = all(
            email not in personal_emails 
            for email in ["<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>"]
        )
        
        # Проверяем, что нормальные email прошли
        normal_emails_passed = all(
            email in personal_emails 
            for email in ["<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>"]
        )
        
        # Проверяем, что служебные email отфильтрованы
        service_emails_filtered = all(
            email not in personal_emails
            for email in ["<EMAIL>", "<EMAIL>"]
        )
        
        print(f"\n📋 Проверки:")
        print(f"  ✅ Короткие email отфильтрованы (≤2 символов): {short_emails_filtered}")
        print(f"  ✅ Нормальные email прошли (>2 символов): {normal_emails_passed}")
        print(f"  ✅ Служебные email отфильтрованы: {service_emails_filtered}")
        print(f"  ✅ Домен извлечен: {bool(domain)}")
        
        return short_emails_filtered and normal_emails_passed and service_emails_filtered and bool(domain)
        
    except Exception as e:
        print(f"❌ Ошибка теста: {e}")
        return False

def test_edge_cases():
    """Тест граничных случаев"""
    print("\n🧪 ТЕСТ ГРАНИЧНЫХ СЛУЧАЕВ")
    print("=" * 60)
    
    try:
        from leadgen_enhanced import filter_personal_emails
        
        # Граничные случаи
        edge_cases = [
            # Ровно 2 символа (должны быть отфильтрованы)
            ("<EMAIL>", False, "2 символа - отфильтровать"),
            ("<EMAIL>", False, "2 символа - отфильтровать"),
            ("<EMAIL>", False, "2 символа - отфильтровать"),
            
            # Ровно 3 символа (должны пройти)
            ("<EMAIL>", True, "3 символа - пропустить"),
            ("<EMAIL>", True, "3 символа - пропустить"),
            ("<EMAIL>", True, "3 символа - пропустить"),
            
            # 1 символ (должны быть отфильтрованы)
            ("<EMAIL>", False, "1 символ - отфильтровать"),
            ("<EMAIL>", False, "1 символ - отфильтровать"),
            
            # Пустая локальная часть (некорректный email)
            ("@test.ru", False, "пустая локальная часть"),
            
            # Специальные символы в коротких email
            ("<EMAIL>", False, "2 символа с точкой - отфильтровать"),
            ("<EMAIL>", False, "2 символа с дефисом - отфильтровать"),
        ]
        
        print(f"🔍 Тестируем {len(edge_cases)} граничных случаев:")
        
        all_correct = True
        for email, should_pass, description in edge_cases:
            print(f"\n  Тест: {email}")
            print(f"    Описание: {description}")
            print(f"    Ожидаемый результат: {'пройти' if should_pass else 'отфильтровать'}")
            
            personal_emails, _ = filter_personal_emails([email])
            actually_passed = email in personal_emails
            
            print(f"    Фактический результат: {'прошел' if actually_passed else 'отфильтрован'}")
            
            correct = actually_passed == should_pass
            print(f"    ✅ Правильно: {correct}")
            
            if not correct:
                all_correct = False
        
        return all_correct
        
    except Exception as e:
        print(f"❌ Ошибка теста: {e}")
        return False

def test_real_world_examples():
    """Тест на реальных примерах из логов"""
    print("\n🧪 ТЕСТ НА РЕАЛЬНЫХ ПРИМЕРАХ ИЗ ЛОГОВ")
    print("=" * 60)
    
    try:
        from leadgen_enhanced import filter_personal_emails
        
        # Реальные примеры из логов
        real_examples = [
            # Проблемный случай из лога
            {
                "emails": ["<EMAIL>", "<EMAIL>"],
                "expected_personal": [],  # Оба должны быть отфильтрованы
                "description": "Реальный случай из лога - is@ слишком короткий, mail@ служебный"
            },
            
            # Смешанный случай
            {
                "emails": ["<EMAIL>", "<EMAIL>", "<EMAIL>"],
                "expected_personal": ["<EMAIL>"],  # Только нормальный email
                "description": "Смешанный случай - один нормальный, один короткий, один служебный"
            },
            
            # Только короткие email
            {
                "emails": ["<EMAIL>", "<EMAIL>", "<EMAIL>"],
                "expected_personal": [],  # Все должны быть отфильтрованы
                "description": "Только короткие служебные email"
            },
            
            # Только нормальные email
            {
                "emails": ["<EMAIL>", "<EMAIL>"],
                "expected_personal": ["<EMAIL>", "<EMAIL>"],
                "description": "Только нормальные персональные email"
            }
        ]
        
        print(f"🔍 Тестируем {len(real_examples)} реальных примеров:")
        
        all_correct = True
        for i, example in enumerate(real_examples, 1):
            print(f"\n  Пример {i}: {example['description']}")
            print(f"    Входные email: {example['emails']}")
            print(f"    Ожидаемые персональные: {example['expected_personal']}")
            
            personal_emails, domain = filter_personal_emails(example['emails'])
            
            print(f"    Фактические персональные: {personal_emails}")
            print(f"    Найденный домен: {domain}")
            
            # Проверяем соответствие
            correct = set(personal_emails) == set(example['expected_personal'])
            print(f"    ✅ Правильно: {correct}")
            
            if not correct:
                all_correct = False
                print(f"    ❌ Ожидали: {example['expected_personal']}")
                print(f"    ❌ Получили: {personal_emails}")
        
        return all_correct
        
    except Exception as e:
        print(f"❌ Ошибка теста: {e}")
        return False

def test_imports():
    """Тест импортов обновленных функций"""
    print("\n🧪 ТЕСТ ИМПОРТОВ ОБНОВЛЕННЫХ ФУНКЦИЙ")
    print("=" * 60)
    
    try:
        from leadgen_enhanced import filter_personal_emails
        
        print("✅ Обновленная функция импортирована успешно:")
        print("  • filter_personal_emails (с фильтрацией коротких email)")
        
        return True
        
    except Exception as e:
        print(f"❌ Ошибка импорта: {e}")
        return False

def main():
    """Главная функция тестирования"""
    print("🧪 ТЕСТИРОВАНИЕ ФИЛЬТРАЦИИ КОРОТКИХ EMAIL АДРЕСОВ")
    print("=" * 80)
    
    # Тесты
    tests = [
        ("Импорты обновленных функций", test_imports()),
        ("Фильтрация коротких email", test_short_email_filtering()),
        ("Граничные случаи", test_edge_cases()),
        ("Реальные примеры из логов", test_real_world_examples())
    ]
    
    print("\n📊 РЕЗУЛЬТАТЫ ТЕСТИРОВАНИЯ:")
    print("=" * 80)
    
    passed = 0
    for test_name, result in tests:
        status = "✅ ПРОЙДЕН" if result else "❌ НЕ ПРОЙДЕН"
        print(f"  {test_name}: {status}")
        if result:
            passed += 1
    
    total_tests = len(tests)
    print(f"\n🎯 ИТОГО: {passed}/{total_tests} тестов пройдено")
    
    if passed == total_tests:
        print("🎉 ВСЕ ТЕСТЫ ПРОЙДЕНЫ!")
        print("\n✅ ФИЛЬТРАЦИЯ КОРОТКИХ EMAIL:")
        print("  • Email с локальной частью ≤2 символов отфильтровываются")
        print("  • Защита от служебных сокращений (is@, it@, hr@, pr@)")
        print("  • Сохранение нормальных персональных email (≥3 символов)")
        print("  • Правильная обработка граничных случаев")
        print("  • Исправлена проблема с <EMAIL> из логов")
    elif passed > 2:
        print("⚠️  Частично работает - проверьте логику фильтрации")
    else:
        print("❌ Есть серьезные проблемы с фильтрацией коротких email")

if __name__ == "__main__":
    main()
