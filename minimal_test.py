"""minimal_test.py
Минимальный тест для воспроизведения ошибки "4"
"""

import os
import sys
import asyncio

# Устанавливаем фиктивный API ключ
os.environ['OPENAI_API_KEY'] = 'test_key'

# Предотвращаем запуск CLI
sys.argv = ['test']

def test_collect_company_profile():
    """Тест функции collect_company_profile_async"""
    print("🧪 МИНИМАЛЬНЫЙ ТЕСТ")
    print("=" * 50)
    
    try:
        from leadgen_enhanced import collect_company_profile_async, SECTIONS
        
        print(f"SECTIONS: {SECTIONS}")
        print(f"sorted(SECTIONS): {sorted(SECTIONS)}")
        
        async def run_test():
            try:
                result = await collect_company_profile_async(
                    company="Федеральное ГБУ «Российский Фонд Информации по Природным Ресурсам и Охране Окружающей Среды Минприроды России»",
                    inn="7734258158",
                    tenders_dir=None
                )
                print("✅ Функция выполнена успешно")
                print(f"Длина результата: {len(result)}")
                return True
                
            except Exception as e:
                print(f"❌ Ошибка: {type(e).__name__}: {e}")
                
                # Детальная диагностика
                error_str = str(e)
                if "4" in error_str:
                    print("🔍 Ошибка содержит '4'")
                if "KeyError" in str(type(e).__name__):
                    print("🔍 KeyError - проблема с доступом к ключу")
                if "list index out of range" in error_str:
                    print("🔍 Проблема с индексом списка")
                
                # Попробуем понять, где именно ошибка
                import traceback
                print("\n📋 Полный traceback:")
                traceback.print_exc()
                
                return False
        
        return asyncio.run(run_test())
        
    except Exception as e:
        print(f"❌ Ошибка импорта: {e}")
        return False

if __name__ == "__main__":
    success = test_collect_company_profile()
    
    print(f"\n📊 РЕЗУЛЬТАТ: {'✅ УСПЕХ' if success else '❌ ОШИБКА'}")
