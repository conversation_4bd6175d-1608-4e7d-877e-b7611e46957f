"""test_cleaned_code.py
Тест очищенного кода - проверка удаления лишних элементов
"""

import os
import sys

# Предотвращаем запуск CLI
sys.argv = ['test']

def test_removed_functions():
    """Тест что лишние функции удалены"""
    print("🧪 ТЕСТ УДАЛЕНИЯ ЛИШНИХ ФУНКЦИЙ")
    print("=" * 50)
    
    try:
        import leadgen_enhanced
        
        # Проверяем, что generate_email_variants удалена
        has_generate_email_variants = hasattr(leadgen_enhanced, 'generate_email_variants')
        
        print(f"❌ generate_email_variants существует: {has_generate_email_variants}")
        
        if has_generate_email_variants:
            print("⚠️  Функция generate_email_variants не была удалена!")
            return False
        else:
            print("✅ Функция generate_email_variants успешно удалена")
            return True
        
    except Exception as e:
        print(f"❌ Ошибка теста: {e}")
        return False

def test_existing_functions():
    """Тест что нужные функции остались"""
    print("\n🧪 ТЕСТ СУЩЕСТВОВАНИЯ НУЖНЫХ ФУНКЦИЙ")
    print("=" * 50)
    
    try:
        from leadgen_enhanced import (
            search_company_email_info,
            generate_personal_email,
            process_single_contact_with_email,
            process_contacts_with_emails,
            transliterate_to_english
        )
        
        functions = [
            ("search_company_email_info", search_company_email_info),
            ("generate_personal_email", generate_personal_email),
            ("process_single_contact_with_email", process_single_contact_with_email),
            ("process_contacts_with_emails", process_contacts_with_emails),
            ("transliterate_to_english", transliterate_to_english)
        ]
        
        all_exist = True
        for name, func in functions:
            if callable(func):
                print(f"✅ {name}: существует")
            else:
                print(f"❌ {name}: не найдена")
                all_exist = False
        
        return all_exist
        
    except Exception as e:
        print(f"❌ Ошибка импорта: {e}")
        return False

def test_code_structure():
    """Тест структуры кода"""
    print("\n🧪 ТЕСТ СТРУКТУРЫ КОДА")
    print("=" * 50)
    
    try:
        # Читаем файл и проверяем, что лишние элементы удалены
        with open('leadgen_enhanced.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Проверяем, что паттерны удалены
        has_patterns = 'name.surname@domain' in content
        has_pattern_loop = 'for pattern in patterns:' in content
        has_pattern_replace = 'pattern.replace(' in content
        
        print(f"❌ Содержит паттерны шаблонов: {has_patterns}")
        print(f"❌ Содержит цикл по паттернам: {has_pattern_loop}")
        print(f"❌ Содержит замену паттернов: {has_pattern_replace}")
        
        # Проверяем, что новые функции есть
        has_search_company = 'search_company_email_info' in content
        has_generate_personal = 'generate_personal_email' in content
        
        print(f"✅ Содержит search_company_email_info: {has_search_company}")
        print(f"✅ Содержит generate_personal_email: {has_generate_personal}")
        
        # Проверяем промпты
        has_osint_prompt = 'OSINT‑исследователь' in content
        has_prompt_specialist = 'prompt specialist' in content
        
        print(f"✅ Содержит OSINT промпт: {has_osint_prompt}")
        print(f"✅ Содержит prompt specialist промпт: {has_prompt_specialist}")
        
        # Итоговая оценка
        removed_old = not (has_patterns or has_pattern_loop or has_pattern_replace)
        added_new = has_search_company and has_generate_personal
        has_prompts = has_osint_prompt and has_prompt_specialist
        
        print(f"\n📊 Результат:")
        print(f"  Удалены старые элементы: {'✅' if removed_old else '❌'}")
        print(f"  Добавлены новые функции: {'✅' if added_new else '❌'}")
        print(f"  Промпты на месте: {'✅' if has_prompts else '❌'}")
        
        return removed_old and added_new and has_prompts
        
    except Exception as e:
        print(f"❌ Ошибка чтения файла: {e}")
        return False

def test_process_flow():
    """Тест логики процесса"""
    print("\n🧪 ТЕСТ ЛОГИКИ ПРОЦЕССА")
    print("=" * 50)
    
    try:
        from leadgen_enhanced import transliterate_to_english
        
        # Тест транслитерации
        test_cases = [
            ("Александр", "aleksandr"),
            ("Иванов", "ivanov"),
            ("Мария", "mariya"),
            ("Петрова", "petrova")
        ]
        
        print("🔤 Тест транслитерации:")
        all_correct = True
        for russian, expected in test_cases:
            result = transliterate_to_english(russian)
            correct = result == expected
            print(f"  {russian} → {result} {'✅' if correct else '❌'}")
            if not correct:
                all_correct = False
        
        return all_correct
        
    except Exception as e:
        print(f"❌ Ошибка теста: {e}")
        return False

def main():
    """Главная функция тестирования"""
    print("🧪 ТЕСТИРОВАНИЕ ОЧИЩЕННОГО КОДА")
    print("=" * 80)
    
    tests = [
        ("Удаление лишних функций", test_removed_functions()),
        ("Существование нужных функций", test_existing_functions()),
        ("Структура кода", test_code_structure()),
        ("Логика процесса", test_process_flow())
    ]
    
    print("\n📊 РЕЗУЛЬТАТЫ ТЕСТИРОВАНИЯ:")
    print("=" * 80)
    
    passed = 0
    for test_name, result in tests:
        status = "✅ ПРОЙДЕН" if result else "❌ НЕ ПРОЙДЕН"
        print(f"  {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n🎯 ИТОГО: {passed}/{len(tests)} тестов пройдено")
    
    if passed == len(tests):
        print("🎉 ВСЕ ТЕСТЫ ПРОЙДЕНЫ!")
        print("\n✅ КОД УСПЕШНО ОЧИЩЕН:")
        print("  • Удалены лишние функции генерации паттернов")
        print("  • Удалены программные шаблоны email")
        print("  • Оставлены только GPT-функции")
        print("  • Промпты работают корректно")
        print("  • Процесс: реальные примеры → персональный email")
    else:
        print("⚠️  Есть проблемы с очисткой кода")

if __name__ == "__main__":
    main()
