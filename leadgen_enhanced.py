"""leadgen_enhanced.py
-----------------------------------------------------------------------
Enhanced Lead Generation with Personalized Email Generation (v1.0.0)
-----------------------------------------------------------------------
New Features:
* Excel input processing with company data from column D "Заказчик (Из Контур.Закупки)"
* Personalized email generation using Nova AI templates
* PDF generation for each company
* AM folder organization system
* Integration with existing company profile collection
"""
from __future__ import annotations

import asyncio
import glob
import os
import re
import sys
import unittest
from pathlib import Path
from typing import Any, Dict, List, Optional, Tuple
import argparse

try:
    from dotenv import load_dotenv  # type: ignore
    load_dotenv()
except ModuleNotFoundError:
    pass

try:
    import openai  # type: ignore
    from openai.types.chat import ChatCompletion, ChatCompletionMessageParam  # type: ignore
except ModuleNotFoundError:
    openai = None  # type: ignore
    ChatCompletion = Any  # type: ignore
    ChatCompletionMessageParam = Dict[str, Any]  # type: ignore

try:
    import pandas as pd  # type: ignore
except ModuleNotFoundError:
    pd = None  # type: ignore

try:
    import requests  # type: ignore
    import json  # type: ignore
    REQUESTS_AVAILABLE = True
except ModuleNotFoundError:
    requests = None  # type: ignore
    json = None  # type: ignore
    REQUESTS_AVAILABLE = False

try:
    from docx import Document  # type: ignore
    from docx.shared import Inches  # type: ignore
    from docx.enum.text import WD_ALIGN_PARAGRAPH  # type: ignore
    DOCX_AVAILABLE = True
except ModuleNotFoundError:
    print("Warning: python-docx not installed. Word document generation will not work.")
    Document = None
    DOCX_AVAILABLE = False

# Keep reportlab as fallback option
try:
    from reportlab.lib.pagesizes import A4  # type: ignore
    from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer  # type: ignore
    from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle  # type: ignore
    from reportlab.lib.units import inch  # type: ignore
    from reportlab.pdfbase import pdfmetrics  # type: ignore
    from reportlab.pdfbase.ttfonts import TTFont  # type: ignore
    REPORTLAB_AVAILABLE = True
except ModuleNotFoundError:
    print("Warning: reportlab not installed. PDF generation will not work.")
    SimpleDocTemplate = None
    REPORTLAB_AVAILABLE = False

OPENAI_AVAILABLE = openai is not None

# --------------------------------------------------------------------
# Config
# --------------------------------------------------------------------
# Model configuration
RESEARCH_MODEL = "gpt-4o-search-preview-2025-03-11"  # For company research with search capabilities
EMAIL_MODEL = "o4-mini-2025-04-16"  # For personalized email generation (o4-mini model)
TIMEOUT_SECS = 300

# o4-mini model specific settings (reasoning_effort may not be supported)
O4_REASONING_EFFORT = "medium"  # Options: "low", "medium", "high" (fallback if not supported)

# Hunter.io cost control settings
HUNTER_MAX_EMAILS_PER_DOMAIN = 3  # Максимум email для поиска по домену (экономия средств)
HUNTER_ENABLE_VERIFICATION = True  # Включить верификацию email (стоит денег)
HUNTER_MAX_VERIFICATIONS = 3  # Максимум верификаций при тестировании паттернов

# Procurement analysis settings (ПРИОРИТЕТНЫЙ МЕТОД)
PROCUREMENT_ENABLE = True  # Включить анализ закупок (самый приоритетный метод)
PROCUREMENT_COLUMN = "V"  # Столбец с контактной информацией
PUBLIC_DOMAINS = [  # Публичные домены для исключения
    "mail.ru", "yandex.ru", "gmail.com", "yahoo.com", "outlook.com",
    "hotmail.com", "rambler.ru", "list.ru", "bk.ru", "inbox.ru"
]

SECTIONS: Dict[int, str] = {
    1: "Основные ML/AI‑проекты",
    2: "Контакты LPR + телефоны/e‑mail",
    3: "Релевантные закупки с kontur.zakupki",
}

# System prompts for different search types
ML_AI_SYSTEM_PROMPT = (
    "You are an expert web-researcher specializing in ML/AI projects and hiring analysis. "
    "Your task is to find specific information about a company's machine learning and artificial intelligence activities.\n\n"
    "For ML/AI projects: Find concrete projects, implementations, use cases, technologies used, results achieved. "
    "Look for press releases, case studies, conference presentations, technical articles.\n\n"
    "For hiring analysis: Find current and recent job postings for DS/ML/DevOps positions. "
    "Provide direct links to vacancies with publication dates. Focus on roles like: "
    "Data Scientist, ML Engineer, DevOps Engineer, MLOps, AI Engineer, Head of ML/DS.\n\n"
    "Output format:\n"
    "**ML/AI Проекты:**\n"
    "• <описание проекта> — <URL>, <дата>\n\n"
    "**Найм DS/ML/DevOps:**\n"
    "• <название вакансии> — <URL вакансии>, <дата публикации>\n\n"
    "Use Russian language. Base only on found information, no speculation.\n"
    "Use advanced search operators and site-specific filters.\n"
)

CONTACTS_SYSTEM_PROMPT = (
    "You are an expert web-researcher specializing in finding executive contacts. "
    "Your task is to find specific contact information for C-level executives and IT leadership.\n\n"
    "Target roles: CTO, CIO, CEO, Head of Infrastructure, IT Director, Head of Digital, CDO, "
    "Технический директор, Директор по информационным технологиям, Директор по цифровизации.\n\n"
    "Search in: LinkedIn, company websites, press releases, conference speaker lists, "
    "professional directories, news articles, interview publications.\n\n"
    "IMPORTANT: Return results in STRICT format - each contact on separate line:\n"
    "CONTACT_START\n"
    "FIRST_NAME: Имя (полное или частичное, например 'А****' если засекречено)\n"
    "LAST_NAME: Фамилия (полная или частичная, например 'Е****' если засекречена)\n"
    "POSITION: Должность\n"
    "CONFIDENCE: high|medium|low\n"
    "CONTACT_END\n\n"
    "Example:\n"
    "CONTACT_START\n"
    "FIRST_NAME: Александр\n"
    "LAST_NAME: Иванов\n"
    "POSITION: CTO\n"
    "CONFIDENCE: high\n"
    "CONTACT_END\n\n"
    "Focus only on verified current positions. Use Russian language for names and positions.\n"
)

SECTION_QUERIES: Dict[int, List[str]] = {
    1: [
        # Первый запрос: ML/AI проекты + найм DS/ML/DevOps
        """Найди информацию о {company} по двум направлениям:

1. ML/AI ПРОЕКТЫ: Поиск проектов и задач, связанных с машинным обучением и искусственным интеллектом именно в {company}.
   Ищи в: TAdviser, CNews, ComNews, пресс-релизы, официальный сайт, конференции (AI Journey, MLConf, Highload),
   Habr, Medium, GitHub, корпоративные блоги, кейсы, технические статьи.
   Ключевые слова: "машинное обучение", "искусственный интеллект", "ML", "AI", "нейронные сети", "алгоритмы",
   "предиктивная аналитика", "компьютерное зрение", "NLP", "рекомендательные системы".

2. НАЙМ DS/ML/DevOps: Ищет ли {company} специалистов по Data Science, Machine Learning, DevOps.
   Ищи актуальные вакансии на: hh.ru, LinkedIn, Superjob, Хабр Карьера, сайт компании.
   Целевые позиции: "Data Scientist", "ML Engineer", "DevOps Engineer", "MLOps", "AI Engineer",
   "Head of ML", "Head of DS", "Аналитик данных", "Инженер машинного обучения".

   Для каждой найденной вакансии указывай: название позиции, прямую ссылку на вакансию, дату публикации.
   Выводи ТОЛЬКО 2-3 самые релевантные вакансии именно этой компании."""
    ],
    2: [
        # Второй запрос: контактные лица
        """Найди контактные лица {company} в открытых источниках - строго имена, фамилии, должности.

Целевые роли: CTO, CIO, CEO, Начальник инфраструктуры, Директор ИТ, Head of Digital, CDO,
Технический директор, Директор по информационным технологиям, Директор по цифровизации.

Источники поиска:
- LinkedIn (site:linkedin.com/in + "{company}")
- Официальный сайт {company} (раздел "Руководство", "Команда", "О компании")
- Пресс-релизы и новости с упоминанием руководителей
- Конференции и мероприятия (спикеры от {company})
- Интервью в СМИ (VC.ru, CNews, TAdviser, РБК)
- Профессиональные рейтинги и награды

ТРЕБОВАНИЯ К ВЫХОДУ:
- Используй СТРОГИЙ формат - каждый контакт на отдельной строке:
CONTACT_START
FIRST_NAME: Имя (полное или частичное, например 'А****' если засекречено)
LAST_NAME: Фамилия (полная или частичная, например 'Е****' если засекречена)
POSITION: Должность
CONFIDENCE: high|medium|low
CONTACT_END

Пример:
CONTACT_START
FIRST_NAME: Александр
LAST_NAME: Иванов
POSITION: CTO
CONFIDENCE: high
CONTACT_END

- Никаких вымышленных контактов — только реально найденные.
- Фокус только на проверенных текущих позициях."""
    ],
}

# Email generation prompt
EMAIL_GENERATION_PROMPT = """You are a Russian-speaking B2B copywriter who turns raw briefs into personalised outbound emails.
Ваша задача: на основе приложенных файлов сформировать готовое ценностное предложение (e-mail) для конкретной компании-получателя.
Требования к результату:
1. Письмо должно быть написано на русском, вежливым деловым тоном, обращение на «Вы». Письмо должно быть лаконичным и связным.
2. Используйте шаблон письма как каркас; меняйте/дополняйте только те блоки, где указана кастомизация.
3. Персонализация: Проанализируйте профиль компании из файла "Профиль компании [название компании]" на основе чего примените релевантную информацию о компании к письму.  
4. Обязательно укажите выгоды Nova AI, которые напрямую закрывают проблемы, выявленные в этом профиле. При выборе выгод опирайтесь на файл «Позиционирование Nova AI».
5. Не переписывайте весь шаблон: неизменяемые части (приветствие, перечисление общих преимуществ, завершающий абзац-призыв) оставьте как есть.
6. Структура итогового ответа: только готовое письмо, без пояснений и метаданных.
Если в профиле не нашлось данных для определённого пункта плана, пропустите соответствующую кастомизацию, не оставляя пустых плейсхолдеров.
Файлы вложены:
1. «Профиль компании.pdf» — детальный контекст о компании-получателе (организация, проекты, стек, закупки).
2. «Позиционирование Nova AI.pdf» — описание нашего продукта и его преимуществ.
3. «Шаблон письма Nova AI.pdf» — базовый текст письма без персонализации.
Инструкция:  
• Используй план кастомизации, чтобы связать данные из профиля (источник) с шаблонными фразами.  
• Сгенерируй персонализированное письмо для этой компании, вписав конкретные факты вместо плейсхолдеров «[ … ]».  
• В итоговом письме не должно остаться квадратных скобок или плейсхолдеров.
Выведи, пожалуйста, только финальный, полностью готовый e-mail."""

def _extract_company_name_from_quotes(company: str) -> str:
    """Extract company name from innermost quotes, handling unclosed quotes"""
    import re

    # Найти все пары кавычек (включая разные типы)
    quote_patterns = [
        r'"([^"]*)"',  # Обычные кавычки
        r'«([^»]*)»',  # Французские кавычки
        r'"([^"]*)"',  # Типографские кавычки открывающие/закрывающие
        r"'([^']*)'",  # Одинарные кавычки
    ]

    extracted_texts = []

    for pattern in quote_patterns:
        matches = re.findall(pattern, company)
        extracted_texts.extend(matches)

    if extracted_texts:
        # Возвращаем самый длинный текст из кавычек (обычно самый информативный)
        return max(extracted_texts, key=len).strip()

    # Если кавычек нет, возвращаем исходный текст
    return company.strip()

def _build_user_prompt(company: str, inn: str, instruction: str) -> str:
    """Build user prompt with clean company name (no INN, extract from quotes)"""
    # Извлекаем чистое название компании из кавычек
    clean_company = _extract_company_name_from_quotes(company)
    return instruction.format(company=clean_company)

# --------------------------------------------------------------------
# GPT helpers (from original)
# --------------------------------------------------------------------
async def _run_subquery(client: "openai.AsyncClient", company: str, inn: str, instr: str, section_idx: int, model: str = RESEARCH_MODEL) -> str:
    # Извлекаем чистое название компании из кавычек для поиска
    clean_company = _extract_company_name_from_quotes(company)

    # Выбираем системный промпт в зависимости от секции
    if section_idx == 1:  # ML/AI проекты и найм
        sys_msg = ML_AI_SYSTEM_PROMPT
    elif section_idx == 2:  # Контакты
        sys_msg = CONTACTS_SYSTEM_PROMPT
    else:
        # Для остальных секций используем базовый промпт
        sys_msg = ML_AI_SYSTEM_PROMPT

    msgs: List[ChatCompletionMessageParam] = [
        {"role": "system", "content": sys_msg},
        {"role": "user", "content": _build_user_prompt(company, inn, instr)},
    ]

    # Убираем JSON формат - не поддерживается с web_search моделями
    resp: ChatCompletion = await client.chat.completions.create(
        model=model,
        messages=msgs,
        timeout=TIMEOUT_SECS,
    )

    return resp.choices[0].message.content or ""

def _filter_by_company(lines: List[str], company: str) -> List[str]:
    # Извлекаем чистое название компании для фильтрации
    clean_company = _extract_company_name_from_quotes(company)
    c_lower = clean_company.lower()
    return [ln for ln in lines if c_lower in ln.lower()]

async def _gather_section(client: "openai.AsyncClient", company: str, inn: str, idx: int, model: str = RESEARCH_MODEL, refresh_token: Optional[str] = None, tenders_dir: Optional[str] = None) -> str:
    # Проверяем, что индекс существует в SECTION_QUERIES
    if idx not in SECTION_QUERIES:
        raise KeyError(f"Секция {idx} не найдена в SECTION_QUERIES. Доступные секции: {list(SECTION_QUERIES.keys())}")

    # Специальная обработка для секции контактов (idx == 2)
    if idx == 2:
        print("  🔍 Поиск контактов через веб + Setka API...")

        # 1. Получаем контакты через веб-поиск (GPT) - запускаем дважды для стабильности
        web_contacts = []
        print(f"    🌐 Запуск веб-поиска контактов...")
        for i, q in enumerate(SECTION_QUERIES[idx], 1):
            query_contacts = []

            # Запускаем каждый запрос дважды для повышения стабильности
            for attempt in range(1, 3):  # 2 попытки
                try:
                    print(f"      Запрос {i}/{len(SECTION_QUERIES[idx])}, попытка {attempt}/2: {q[:100]}...")
                    chunk = await _run_subquery(client, company, inn, q, idx, model)
                    print(f"      Получен ответ длиной: {len(chunk)} символов")

                    # Показываем первые 300 символов ответа для отладки
                    print(f"      Начало ответа: {chunk[:300]}...")

                    # Парсим структурированный текст
                    contacts = parse_contacts_from_text(chunk)
                    print(f"      Распарсено контактов: {len(contacts)}")

                    for contact in contacts:
                        print(f"        - {contact.get('last_name', '')} {contact.get('first_name', '')} ({contact.get('position', '')})")

                    query_contacts.extend(contacts)
                except Exception as exc:
                    print(f"    ⚠️ Ошибка веб-поиска контактов (попытка {attempt}): {exc}")

            # Добавляем все контакты из обеих попыток
            web_contacts.extend(query_contacts)
            print(f"      📊 Итого контактов из запроса {i}: {len(query_contacts)}")

        print(f"    🌐 Итого из веб-поиска (все попытки): {len(web_contacts)} контактов")

        # 2. Получаем контакты через Setka API (если есть refresh_token)
        setka_contacts = []
        new_refresh_token = refresh_token
        if refresh_token:
            try:
                # Сначала получаем сырые данные из Setka API
                raw_setka_contacts, new_refresh_token = await get_contacts_from_setka(company, refresh_token)
                print(f"    📊 Получено {len(raw_setka_contacts)} сырых контактов из Setka API")

                # Проверяем, есть ли зашифрованные контакты для расшифровки
                encrypted_contacts = []
                normal_contacts = []

                for contact in raw_setka_contacts:
                    first_name = contact.get("first_name", "")
                    last_name = contact.get("last_name", "")
                    position = contact.get("position", "")

                    is_partial_first = "*" in first_name or len(first_name) <= 2
                    is_partial_last = "*" in last_name or len(last_name) <= 2

                    if is_partial_first or is_partial_last:
                        encrypted_contacts.append((last_name, first_name, position))
                    else:
                        normal_contacts.append(contact)

                # Расшифровываем зашифрованные контакты
                if encrypted_contacts:
                    print(f"    🔍 Найдено {len(encrypted_contacts)} зашифрованных контактов, попытка расшифровки...")
                    decrypted_contacts = await decrypt_setka_contacts(client, company, encrypted_contacts)
                    setka_contacts = normal_contacts + decrypted_contacts
                else:
                    setka_contacts = normal_contacts

                print(f"    ✅ Итого обработано {len(setka_contacts)} контактов из Setka API")

                # Сохраняем новый refresh токен в файл
                if new_refresh_token and new_refresh_token != refresh_token:
                    token_file = "/tmp/setka_refresh_token.txt"
                    with open(token_file, 'w') as f:
                        f.write(new_refresh_token)
                    print(f"    🔑 Новый refresh токен сохранен в: {token_file}")
                    print(f"    🔑 Новый токен: {new_refresh_token}")

            except Exception as exc:
                print(f"    ⚠️ Ошибка Setka API: {exc}")

        # 3. Объединяем контакты
        all_contacts = web_contacts + setka_contacts

        # 4. Дедуплицируем контакты с подробным логированием
        print(f"    🔄 Дедупликация контактов...")
        unique_contacts = deduplicate_contacts(all_contacts)

        print(f"    📊 Итого уникальных контактов: {len(unique_contacts)}")

        # 5. Обрабатываем контакты с поиском email (НОВАЯ ЛОГИКА)
        if unique_contacts:
            # Получаем Hunter.io API ключ из переменных окружения
            hunter_api_key = os.getenv("HUNTER_API_KEY")
            if hunter_api_key:
                print(f"    🔑 Используем Hunter.io API ключ")
            else:
                print(f"    ⚠️ Hunter.io API ключ не найден (установите HUNTER_API_KEY)")

            return await process_contacts_with_emails_new_logic(client, company, unique_contacts, hunter_api_key, refresh_token, tenders_dir)
        else:
            return "Контакты не найдены"

    # Обычная обработка для других секций
    raw: List[str] = []
    for q in SECTION_QUERIES[idx]:
        try:
            chunk = await _run_subquery(client, company, inn, q, idx, model)
        except Exception as exc:
            chunk = f"Ошибка: {exc}"
        raw.extend(chunk.strip().split("\n"))

    raw = [ln for ln in raw if ln]
    uniq, seen = [], set()
    for ln in raw:
        if ln not in seen:
            uniq.append(ln)
            seen.add(ln)

    # Убираем агрессивную фильтрацию - GPT уже получил контекст о компании
    # и возвращает релевантную информацию. Фильтрация по названию компании
    # удаляет слишком много полезной информации (контакты, адреса, финансы и т.д.)

    if uniq:
        # Минимальная фильтрация - убираем только явно нерелевантные строки
        filtered_lines = []
        irrelevant_keywords = [
            "реклама", "advertisement", "sponsored", "промо", "скидка",
            "купить", "заказать", "акция", "предложение услуг"
        ]

        for line in uniq:
            line_lower = line.lower()
            # Пропускаем только явно рекламные/нерелевантные строки
            if not any(keyword in line_lower for keyword in irrelevant_keywords):
                filtered_lines.append(line)

        if filtered_lines:
            return "\n".join(filtered_lines)

    urls = {m.group(0) for ln in uniq for m in re.finditer(r"https?://[^ ,]+", ln)}
    return f"Информация отсутствует. Проверены источники: {', '.join(sorted(urls)) or '—'}"

def _scan_tenders(dir_path: Optional[str], company: str) -> str:
    if dir_path is None:
        return "Не задан каталог с таблицами закупок (--tenders)"
    if pd is None:
        return "pandas не установлен, сканирование закупок невозможно"

    bullets: List[str] = []
    # Используем полное название юридического лица без преобразований для поиска в закупках
    comp_low = company.lower()
    for file in glob.glob(os.path.join(dir_path, "*.xls*")):
        try:
            df = pd.read_excel(file, header=None, engine="openpyxl")  # type: ignore[arg-type]
        except Exception:
            continue
        aa_col = 26  # AA (столбец AA вместо AB)
        for i in range(2, len(df)):
            cell = str(df.iat[i, aa_col]).lower() if not pd.isna(df.iat[i, aa_col]) else ""
            # Точное совпадение названий компаний
            if cell.strip() == comp_low.strip():
                # Извлекаем данные в новом формате
                name = str(df.iat[i, 1]).strip() if not pd.isna(df.iat[i, 1]) else ""  # Столбец B
                price = str(df.iat[i, 2]).strip() if not pd.isna(df.iat[i, 2]) else ""  # Столбец C
                date = str(df.iat[i, 10]).strip() if not pd.isna(df.iat[i, 10]) else ""  # Столбец K
                contact = str(df.iat[i, 21]).strip() if not pd.isna(df.iat[i, 21]) else ""  # Столбец V

                # Формируем строку в новом формате
                tender_info = f"Название закупки: {name}.\nДата: {date}.\nНМЦ: {price} руб.\nКонтактное лицо: {contact}."
                bullets.append(tender_info)

    # Удаляем дубликаты, сохраняя порядок
    unique_bullets = []
    seen = set()
    for bullet in bullets:
        if bullet not in seen:
            unique_bullets.append(bullet)
            seen.add(bullet)

    return "Не найдено" if not unique_bullets else "\n\n".join(unique_bullets)

def _extract_contacts_from_tenders(dir_path: Optional[str], company: str) -> List[str]:
    """Extract contact information from tenders for email analysis"""
    if dir_path is None or pd is None:
        return []

    contacts: List[str] = []
    comp_low = company.lower()

    for file in glob.glob(os.path.join(dir_path, "*.xls*")):
        try:
            df = pd.read_excel(file, header=None, engine="openpyxl")  # type: ignore[arg-type]
        except Exception:
            continue
        aa_col = 26  # AA (столбец AA вместо AB)
        for i in range(2, len(df)):
            cell = str(df.iat[i, aa_col]).lower() if not pd.isna(df.iat[i, aa_col]) else ""
            # Точное совпадение названий компаний
            if cell.strip() == comp_low.strip():
                # Извлекаем только контактную информацию (столбец V)
                contact = str(df.iat[i, 21]).strip() if not pd.isna(df.iat[i, 21]) else ""  # Столбец V
                if contact and contact != "nan":
                    contacts.append(contact)

    return contacts

# --------------------------------------------------------------------
# Procurement analysis functions (ПРИОРИТЕТНЫЙ МЕТОД)
# --------------------------------------------------------------------

def extract_emails_from_text(text: str) -> List[str]:
    """Extract all email addresses from text using regex"""
    if not text or not isinstance(text, str):
        return []

    import re

    # Регулярное выражение для поиска email адресов
    email_pattern = r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b'
    emails = re.findall(email_pattern, text)

    # Очищаем и нормализуем найденные email
    cleaned_emails = []
    for email in emails:
        email = email.lower().strip()
        if len(email) > 5 and '@' in email and '.' in email:
            cleaned_emails.append(email)

    return list(set(cleaned_emails))  # Убираем дубликаты

def filter_corporate_emails(emails: List[str]) -> List[str]:
    """Filter out public domain emails, keep only corporate ones"""
    corporate_emails = []

    for email in emails:
        if '@' not in email:
            continue

        domain = email.split('@')[1]

        # Проверяем, что это не публичный домен
        is_public = any(public_domain in domain for public_domain in PUBLIC_DOMAINS)

        if not is_public:
            corporate_emails.append(email)
            print(f"      ✅ Корпоративный email: {email}")
        else:
            print(f"      ⏭️ Пропускаем публичный: {email}")

    return corporate_emails

def filter_personal_emails(emails: List[str]) -> tuple[List[str], str]:
    """Filter out service emails (like zakupki@), keep only personal ones. Return (personal_emails, domain)"""

    # Служебные ключевые слова в локальной части email
    SERVICE_KEYWORDS = {
        'zakupki', 'tender', 'tenders', 'procurement', 'закупки', 'торги',
        'info', 'admin', 'support', 'contact', 'office', 'general',
        'mail', 'email', 'noreply', 'no-reply', 'donotreply',
        'comm', 'communication', 'communications', 'press',
        'sales', 'marketing', 'hr', 'finance', 'accounting',
        'reception', 'secretary', 'assistant', 'help', 'service'
    }

    personal_emails = []
    domain_from_emails = ""

    # Сначала извлекаем домен из любого корпоративного email
    for email in emails:
        if '@' in email:
            domain_from_emails = email.split('@')[1]
            break

    for email in emails:
        if '@' not in email:
            continue

        local_part = email.split('@')[0].lower()

        # Проверяем, содержит ли локальная часть служебные слова
        is_service = any(keyword in local_part for keyword in SERVICE_KEYWORDS)

        # Проверяем на короткие локальные части (2 символа и меньше)
        # Такие как: is@, it@, hr@, pr@ - почти всегда служебные
        is_too_short = len(local_part) <= 2

        if is_too_short:
            print(f"      ⚠️ Слишком короткий email пропущен: {email} (локальная часть: '{local_part}' - {len(local_part)} символов)")
        elif not is_service:
            personal_emails.append(email)
            print(f"      ✅ Персональный email: {email}")
        else:
            print(f"      ⚠️ Служебный email пропущен: {email}")

    return personal_emails, domain_from_emails

def deduplicate_contacts(contacts: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
    """Remove duplicate contacts based on first_name + last_name combination"""
    seen = set()
    unique_contacts = []

    for contact in contacts:
        first_name = contact.get("first_name", "").strip().lower()
        last_name = contact.get("last_name", "").strip().lower()

        # Создаем ключ для дедупликации
        key = f"{first_name}_{last_name}"

        if key and key not in seen and first_name and last_name:
            seen.add(key)
            unique_contacts.append(contact)
            print(f"      ✅ Добавлен: {contact.get('last_name', '')} {contact.get('first_name', '')} - {contact.get('position', '')}")
        elif key in seen:
            print(f"      🔄 Дубликат пропущен: {contact.get('last_name', '')} {contact.get('first_name', '')} - {contact.get('position', '')}")
        else:
            print(f"      ⚠️ Неполные данные пропущены: {contact}")

    return unique_contacts

async def decrypt_setka_contacts(client: "openai.AsyncClient", company_name: str, encrypted_contacts: List[Tuple[str, str, str]]) -> List[Dict[str, Any]]:
    """Decrypt encrypted contacts from Setka API using GPT"""
    decrypted_contacts = []

    for last_name, first_name, position in encrypted_contacts:
        print(f"    👤 Попытка расшифровки: {last_name} {first_name} - {position}")

        # Проверяем, есть ли зашифрованные имена (например, А**** Е****)
        is_partial_first = "*" in first_name or len(first_name) <= 2
        is_partial_last = "*" in last_name or len(last_name) <= 2

        if is_partial_first or is_partial_last:
            print(f"      🔍 Обнаружены зашифрованные данные, попытка расшифровки...")
            first_initial = first_name[0] if first_name else ""
            last_initial = last_name[0] if last_name else ""

            resolved = await resolve_partial_name(client, company_name, position, first_initial, last_initial)

            if resolved.get("confidence") in ["high", "medium"]:
                final_first_name = resolved.get("first_name", first_name)
                final_last_name = resolved.get("last_name", last_name)
                print(f"      ✅ Расшифровано: {final_last_name} {final_first_name}")

                # Добавляем только успешно расшифрованные контакты
                decrypted_contacts.append({
                    "first_name": final_first_name,
                    "last_name": final_last_name,
                    "position": position,
                    "confidence": resolved.get("confidence", "medium"),
                    "source": "setka_api_decrypted"
                })
            else:
                print(f"      ❌ Не удалось расшифровать {first_name} {last_name}, контакт пропущен")
                # НЕ добавляем нерасшифрованные контакты в список
        else:
            # Обычный контакт без шифрования
            print(f"      ✅ Обычный контакт добавлен")
            decrypted_contacts.append({
                "first_name": first_name,
                "last_name": last_name,
                "position": position,
                "confidence": "high",
                "source": "setka_api"
            })

    print(f"    📊 Итого расшифровано контактов: {len(decrypted_contacts)} из {len(encrypted_contacts)}")
    return decrypted_contacts

async def analyze_procurement_emails(client: "openai.AsyncClient", corporate_emails: List[str]) -> Dict[str, str]:
    """Analyze corporate emails to determine domain and pattern using GPT"""
    if not corporate_emails:
        return {"domain": "", "pattern": "", "example": ""}

    print(f"    🧠 Анализ {len(corporate_emails)} корпоративных email через GPT...")

    # Группируем email по доменам
    domain_groups = {}
    for email in corporate_emails:
        domain = email.split('@')[1]
        if domain not in domain_groups:
            domain_groups[domain] = []
        domain_groups[domain].append(email)

    # Выбираем домен с наибольшим количеством примеров
    best_domain = max(domain_groups.keys(), key=lambda d: len(domain_groups[d]))
    best_emails = domain_groups[best_domain]

    print(f"    🎯 Выбранный домен: {best_domain} ({len(best_emails)} примеров)")

    # Анализируем паттерн через GPT
    emails_text = "\n".join([f"- {email}" for email in best_emails])

    prompt = f"""Проанализируй корпоративные email адреса и определи шаблон написания:

НАЙДЕННЫЕ EMAIL АДРЕСА:
{emails_text}

ЗАДАЧА:
1. Определи домен компании (часть после @)
2. Проанализируй локальную часть (до @) каждого email
3. Определи шаблон написания имени и фамилии
4. Выбери ОДИН наиболее типичный пример для генерации новых email

ВОЗМОЖНЫЕ ШАБЛОНЫ:
- ivan.petrov (полное имя.полная фамилия)
- i.petrov (первая буква имени.полная фамилия)
- petrov.ivan (полная фамилия.полное имя)
- petrov.i (полная фамилия.первая буква имени)
- ivan-petrov (имя-фамилия через дефис)
- ivanpetrov (имя и фамилия слитно)
- ipetrov (первая буква имени + фамилия)
- petrov (только фамилия)
- другие нестандартные варианты

ВАЖНО ДЛЯ НЕПОЛНЫХ ДАННЫХ:
- Если email содержит только одно слово (например: "lebedev", "zhilin"), это может быть:
  1. Только фамилия (lebedev = фамилия Лебедев)
  2. Сокращение (zhilin = возможно Жилин)
- В таких случаях указывай шаблон как "только фамилия" и пример как есть
- НЕ ДОДУМЫВАЙ сложные шаблоны для простых примеров

ФОРМАТ ОТВЕТА:
ДОМЕН: [домен компании]
ШАБЛОН: [описание шаблона]
ПРИМЕР: [один типичный email для копирования стиля]"""

    msgs: List[ChatCompletionMessageParam] = [
        {"role": "system", "content": "You are an expert at analyzing email patterns and corporate naming conventions."},
        {"role": "user", "content": prompt},
    ]

    resp: ChatCompletion = await client.chat.completions.create(
        model="gpt-4o-mini",  # Используем базовую модель без поиска
        messages=msgs,
        timeout=TIMEOUT_SECS,
    )

    content = resp.choices[0].message.content or ""

    # Парсим ответ
    domain = ""
    pattern = ""
    example = ""

    lines = content.split('\n')
    for line in lines:
        line = line.strip()
        if line.startswith("ДОМЕН:"):
            domain = line.replace("ДОМЕН:", "").strip()
        elif line.startswith("ШАБЛОН:"):
            pattern = line.replace("ШАБЛОН:", "").strip()
        elif line.startswith("ПРИМЕР:"):
            example = line.replace("ПРИМЕР:", "").strip()

    # Fallback: если парсинг не удался, используем первый email как пример
    if not domain and best_emails:
        domain = best_domain
    if not example and best_emails:
        example = best_emails[0]

    print(f"    📊 Результат анализа:")
    print(f"      Домен: {domain}")
    print(f"      Шаблон: {pattern}")
    print(f"      Пример: {example}")

    return {"domain": domain, "pattern": pattern, "example": example}

async def generate_email_by_example(client: "openai.AsyncClient", first_name: str, last_name: str, email_example: str, domain: str) -> str:
    """Generate email for person based on example using GPT"""
    if not first_name or not last_name or not email_example or not domain:
        return ""

    # Транслитерируем имена
    first_en = transliterate_to_english(first_name)
    last_en = transliterate_to_english(last_name)

    # Определяем, полный ли это email или только локальная часть
    if '@' in email_example:
        # Полный email - извлекаем локальную часть
        local_part = email_example.split('@')[0]
        example_description = f"полный email: {email_example} (локальная часть: {local_part})"
    else:
        # Только локальная часть
        local_part = email_example
        example_description = f"локальная часть: {local_part}"

    prompt = f"""Создай email адрес для сотрудника по примеру корпоративного стиля:

СОТРУДНИК:
- Имя: {first_name} (английский: {first_en})
- Фамилия: {last_name} (английский: {last_en})

ПРИМЕР КОРПОРАТИВНОГО СТИЛЯ:
{example_description}

ДОМЕН КОМПАНИИ:
{domain}

ЗАДАЧА:
Проанализируй пример локальной части "{local_part}" и создай аналогичный адрес для данного сотрудника.
Сохрани точно такой же стиль написания локальной части.

АНАЛИЗ ПРИМЕРА "{local_part}":
- Если это "lebedev" или "zhilin" (одно слово) → стиль: только фамилия → используй "{last_en}"
- Если это "m.zinovev" → стиль: первая буква имени + точка + полная фамилия → используй "{first_en[0]}.{last_en}"
- Если это "ivan.petrov" → стиль: полное имя + точка + полная фамилия → используй "{first_en}.{last_en}"
- Если это "i.petrov" → стиль: первая буква имени + точка + полная фамилия → используй "{first_en[0]}.{last_en}"
- Если это "ivanpetrov" → стиль: полное имя + полная фамилия слитно → используй "{first_en}{last_en}"
- Если это "ipetrov" → стиль: первая буква имени + полная фамилия → используй "{first_en[0]}{last_en}"

ОСОБОЕ ВНИМАНИЕ К ПРОСТЫМ ПРИМЕРАМ:
Если пример "{local_part}" содержит только одно слово без точек и дефисов:
- Это скорее всего ТОЛЬКО ФАМИЛИЯ
- НЕ добавляй имя, если его нет в примере
- Используй только фамилию: {last_en}

ВАЖНО:
- Используй английские версии имени и фамилии
- Точно скопируй стиль из примера "{local_part}"
- Используй указанный домен компании: {domain}
- Верни ТОЛЬКО email адрес без дополнительного текста

Ответ (только email):"""

    msgs: List[ChatCompletionMessageParam] = [
        {"role": "system", "content": "You are an expert at creating corporate email addresses following specific company patterns."},
        {"role": "user", "content": prompt},
    ]

    resp: ChatCompletion = await client.chat.completions.create(
        model="gpt-4o-mini",  # Используем базовую модель без поиска
        messages=msgs,
        timeout=TIMEOUT_SECS,
    )

    content = resp.choices[0].message.content or ""

    # Извлекаем email из ответа
    import re
    email_pattern = r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b'
    emails = re.findall(email_pattern, content)

    if emails:
        generated_email = emails[0]
        print(f"      🎯 Сгенерированный email: {generated_email}")
        return generated_email
    else:
        print(f"      ❌ Не удалось извлечь email из ответа: {content[:100]}...")
        return ""

async def analyze_procurement_data(client: "openai.AsyncClient", company_name: str, tenders_dir: str = None) -> Dict[str, str]:
    """Analyze procurement data to find corporate emails (ПРИОРИТЕТНЫЙ МЕТОД)"""
    if not PROCUREMENT_ENABLE:
        print(f"    ⚠️ Анализ закупок отключен в настройках")
        return {"domain": "", "pattern": "", "example": ""}

    if not tenders_dir:
        print(f"    ⚠️ Директория с закупками не указана (--tenders)")
        return {"domain": "", "pattern": "", "example": ""}

    try:
        print(f"    📊 Анализ закупок для компании: {company_name}")
        print(f"    📁 Директория: {tenders_dir}")

        # Используем существующую функцию для извлечения контактов
        print(f"    🔍 Извлекаем контактную информацию из закупок...")
        contact_texts = _extract_contacts_from_tenders(tenders_dir, company_name)

        if not contact_texts:
            print(f"    ❌ Контактная информация не найдена в закупках")
            return {"domain": "", "pattern": "", "example": ""}

        print(f"    📊 Найдено {len(contact_texts)} записей контактной информации")

        # Извлекаем все email из контактной информации
        all_emails = []
        for i, contact_text in enumerate(contact_texts):
            emails = extract_emails_from_text(contact_text)
            if emails:
                print(f"      Запись {i+1}: найдено {len(emails)} email")
                all_emails.extend(emails)

        if not all_emails:
            print(f"    ❌ Email адреса не найдены в контактной информации")
            return {"domain": "", "pattern": "", "example": ""}

        print(f"    📊 Всего найдено {len(all_emails)} email адресов")

        # Фильтруем корпоративные email
        corporate_emails = filter_corporate_emails(all_emails)

        if not corporate_emails:
            print(f"    ❌ Корпоративные email не найдены (только публичные домены)")
            return {"domain": "", "pattern": "", "example": ""}

        print(f"    ✅ Найдено {len(corporate_emails)} корпоративных email")

        # Фильтруем персональные email (исключаем служебные типа zakupki@)
        personal_emails, domain_from_emails = filter_personal_emails(corporate_emails)

        if not personal_emails:
            print(f"    ⚠️ Персональные email не найдены (только служебные типа zakupki@)")
            print(f"    📧 Сохраняем домен из служебных email: {domain_from_emails}")

            if domain_from_emails:
                # Возвращаем только домен, без паттерна и примера
                # Это заставит систему использовать другие методы поиска паттерна
                return {"domain": domain_from_emails, "pattern": "", "example": ""}
            else:
                return {"domain": "", "pattern": "", "example": ""}

        print(f"    ✅ Найдено {len(personal_emails)} персональных email")

        # Анализируем паттерн через GPT только для персональных email
        result = await analyze_procurement_emails(client, personal_emails)

        if result["domain"] and result["example"]:
            print(f"    🎉 УСПЕХ! Найден проверенный корпоративный домен и стиль")
            print(f"      Домен: {result['domain']}")
            print(f"      Пример: {result['example']}")
            return result
        else:
            print(f"    ❌ Не удалось определить домен и паттерн")
            # Если анализ персональных email не удался, возвращаем хотя бы домен
            if domain_from_emails:
                return {"domain": domain_from_emails, "pattern": "", "example": ""}
            else:
                return {"domain": "", "pattern": "", "example": ""}

    except Exception as e:
        print(f"    ❌ Ошибка анализа закупок: {e}")
        return {"domain": "", "pattern": "", "example": ""}

# Email search and validation functions
# --------------------------------------------------------------------

async def search_company_domain_and_emails(client: "openai.AsyncClient", company_name: str) -> List[str]:
    """Search for company domain and employee emails using GPT"""
    prompt = f"""You are an expert OSINT‑исследователь. Ваша задача — найти почтовый домен компании и реальные корпоративные e‑mail-адреса сотрудников.

ВХОД:
- {company_name}: название компании

ЗАДАЧА:
1. Определите официальный веб‑сайт компании (например, через поиск).
2. На сайте найдите примеры корпоративных адресов (например, <EMAIL>) или подсказки к домену.
3. Используя найденный домен, найдите и соберите реальные e‑mail-адреса сотрудников компании обязательно по найденному почтовому домену.
4. Каждый адрес должен быть подтверждён ссылкой на источник, где он найден.

ТРЕБОВАНИЯ К ВЫХОДУ:
- Вернуть JSON-массив строк
- ПЕРВЫЙ элемент — почтовый домен компании (например: "company.ru")
- ОСТАЛЬНЫЕ элементы — реальные email адреса сотрудников (если найдены)
- Никаких вымышленных или шаблонных адресов — только реально найденные
- Пример формата ответа:
```json
[
  "company.ru",
  "<EMAIL>",
  "<EMAIL>"
]
```

Если email сотрудников не найдены, верните только домен:
```json
[
  "company.ru"
]
```"""

    msgs: List[ChatCompletionMessageParam] = [
        {"role": "system", "content": "You are an expert OSINT‑исследователь. Ваша задача — найти почтовый домен компании и реальные корпоративные e‑mail-адреса сотрудников."},
        {"role": "user", "content": prompt},
    ]

    resp: ChatCompletion = await client.chat.completions.create(
        model=RESEARCH_MODEL,
        messages=msgs,
        timeout=TIMEOUT_SECS,
    )

    content = resp.choices[0].message.content or ""

    # Парсим JSON из ответа
    import json
    import re

    try:
        # Ищем JSON массив в тексте
        json_pattern = r'\[\s*"[^"]+"\s*(?:,\s*"[^"]+"\s*)*\]'
        json_matches = re.findall(json_pattern, content, re.DOTALL)

        if json_matches:
            json_text = json_matches[0]
            data = json.loads(json_text)

            if isinstance(data, list) and len(data) > 0:
                return [str(item).strip() for item in data]

        # Fallback: попытка найти домен в тексте
        domain_pattern = r'[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}'
        domains = re.findall(domain_pattern, content)

        if domains:
            return [domains[0]]  # Возвращаем первый найденный домен

        return []

    except Exception as e:
        print(f"    ⚠️ Ошибка парсинга ответа GPT: {e}")
        return []

async def generate_personal_email(client: "openai.AsyncClient", first_name: str, last_name: str, company_email_info: str) -> str:
    """Generate personal email for specific person using GPT"""
    # Транслитерируем имена
    first_en = transliterate_to_english(first_name)
    last_en = transliterate_to_english(last_name)

    prompt = f"""You are a prompt specialist. Задача — на основе входных данных точно и однозначно сгенерировать корпоративный e‑mail сотрудника. Строго следуйте инструкциям.

ВХОДНЫЕ ДАННЫЕ:
– {first_name}: имя сотрудника на русском  
– {first_en}: имя сотрудника на английском  
– {last_name}: фамилия сотрудника на русском  
– {last_en}: фамилия сотрудника на английском  
– {company_email_info}: примеры реальных адресов и доменов из корпоративной почты данной компании  

ЦЕЛЬ:
Составить единственный наиболее вероятный корпоративный e‑mail для сотрудника.

ИНСТРУКЦИИ:
1. Проанализируйте {company_email_info}:  
   a. Вычлените используемый домен, это все, что идет после "@".  
   b. Определите логику построения найденных почт. 
2. Используя только английские {first_en} и {last_en}, подставьте их в выявленный шаблон.  
3. Верните ровно один адрес в формате user@домен.  

ОГРАНИЧЕНИЯ:
- Никакого лишнего текста: только адрес, без кавычек, без пояснений.  
- Если company_email_info пуст или шаблон неясен, верните пустое значение.
"""

    msgs: List[ChatCompletionMessageParam] = [
        {"role": "system", "content": "You are an expert at constructing corporate email addresses based on company patterns."},
        {"role": "user", "content": prompt},
    ]

    resp: ChatCompletion = await client.chat.completions.create(
        model=RESEARCH_MODEL,
        messages=msgs,
        timeout=TIMEOUT_SECS,
    )

    content = resp.choices[0].message.content or ""

    # Извлекаем email из ответа
    import re
    email_pattern = r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b'
    emails = re.findall(email_pattern, content)

    return emails[0] if emails else ""

# Hunter.io API functions
# --------------------------------------------------------------------

async def search_emails_hunter_io(domain: str, hunter_api_key: str = None) -> List[str]:
    """Search for emails using Hunter.io API"""
    if not REQUESTS_AVAILABLE:
        print(f"  ⚠️ requests не установлен, пропускаем Hunter.io поиск")
        return []

    if not hunter_api_key:
        print(f"  ⚠️ Hunter.io API ключ не предоставлен")
        return []

    try:
        import requests

        url = "https://api.hunter.io/v2/domain-search"
        params = {
            "domain": domain,
            "api_key": hunter_api_key,
            "limit": 3
        }

        print(f"    🔍 Hunter.io поиск по домену: {domain}")

        response = requests.get(url, params=params, timeout=10)

        if response.status_code == 200:
            data = response.json()
            emails = []

            if "data" in data and "emails" in data["data"]:
                for email_data in data["data"]["emails"]:
                    if "value" in email_data:
                        emails.append(email_data["value"])
                        print(f"      ✅ Найден email: {email_data['value']}")

            print(f"    📊 Hunter.io нашел {len(emails)} email адресов")
            return emails
        else:
            print(f"    ❌ Hunter.io API ошибка: {response.status_code}")
            return []

    except Exception as e:
        print(f"    ⚠️ Ошибка Hunter.io поиска: {e}")
        return []

async def verify_email_hunter_io(email: str, hunter_api_key: str = None) -> bool:
    """Verify email using Hunter.io API"""
    if not REQUESTS_AVAILABLE:
        print(f"  ⚠️ requests не установлен, пропускаем Hunter.io верификацию")
        return False

    if not hunter_api_key:
        print(f"  ⚠️ Hunter.io API ключ не предоставлен")
        return False

    try:
        import requests

        url = "https://api.hunter.io/v2/email-verifier"
        params = {
            "email": email,
            "api_key": hunter_api_key
        }

        print(f"    🔍 Hunter.io верификация: {email}")

        response = requests.get(url, params=params, timeout=10)

        if response.status_code == 200:
            data = response.json()

            if "data" in data and "status" in data["data"]:
                status = data["data"]["status"]
                is_valid = status == "valid"
                print(f"      {'✅' if is_valid else '❌'} Статус: {status}")
                return is_valid
        else:
            print(f"    ❌ Hunter.io верификация ошибка: {response.status_code}")
            return False

    except Exception as e:
        print(f"    ⚠️ Ошибка Hunter.io верификации: {e}")
        return False

async def find_email_pattern_for_contacts(client: "openai.AsyncClient", company_name: str, contacts: List[Dict[str, Any]], hunter_api_key: str = None, tenders_dir: str = None) -> tuple[str, str, str]:
    """Find email pattern for contacts using new logic - returns (domain, pattern, example)"""
    print(f"  🔍 Поиск email паттерна для {len(contacts)} контактов...")

    # ПРИОРИТЕТНЫЙ МЕТОД: Анализ закупок
    print(f"  🥇 ШАГ 1: Анализ закупок (ПРИОРИТЕТНЫЙ МЕТОД)")
    procurement_result = await analyze_procurement_data(client, company_name, tenders_dir)

    if procurement_result["domain"] and procurement_result["example"]:
        print(f"    🎉 УСПЕХ! Найден проверенный домен и стиль из закупок")
        # Возвращаем пустой паттерн для GPT генерации по примеру
        example_local_part = procurement_result["example"].split('@')[0] if '@' in procurement_result["example"] else procurement_result["example"]
        return procurement_result["domain"], "", example_local_part

    print(f"    ⚠️ Анализ закупок не дал результата, переходим к следующему методу")

    # Проверяем, есть ли домен из закупок (даже без примера)
    if procurement_result["domain"]:
        print(f"    📧 Домен из закупок найден: {procurement_result['domain']}")
        domain_from_procurement = procurement_result["domain"]
    else:
        domain_from_procurement = ""

    # Шаг 2: Получаем домен и примеры от GPT
    print(f"  🥈 ШАГ 2: GPT поиск примеров")
    domain_and_emails = await search_company_domain_and_emails(client, company_name)

    # Определяем приоритетный домен
    if domain_from_procurement:
        # ПРИОРИТЕТ: Домен из закупок важнее домена от GPT
        domain = domain_from_procurement
        print(f"    🎯 Используем приоритетный домен из закупок: {domain}")
    elif domain_and_emails:
        # Используем домен от GPT только если нет домена из закупок
        domain = domain_and_emails[0]
        print(f"    🌐 Используем домен от GPT: {domain}")
    else:
        print(f"    ❌ Домен не найден ни в закупках, ни через GPT")
        return "", "", ""

    # Проверяем примеры от GPT
    if domain_and_emails:
        gpt_examples = domain_and_emails[1:] if len(domain_and_emails) > 1 else []
        print(f"    📧 GPT примеры: {len(gpt_examples)} email адресов")

        # Фильтруем персональные email из GPT примеров
        if gpt_examples:
            personal_gpt_examples, _ = filter_personal_emails(gpt_examples)

            if personal_gpt_examples:
                print(f"    ✅ Используем персональные примеры от GPT:")
                for email in personal_gpt_examples:
                    print(f"      - {email}")

                # Используем первый персональный пример для GPT генерации
                example = personal_gpt_examples[0].split('@')[0]  # Только локальная часть
                print(f"    🎯 Пример для GPT генерации: {example}")
                return domain, "", example  # Паттерн пустой - используем GPT генерацию
    else:
        print(f"    ⚠️ GPT не нашел примеры email")

    # Шаг 3: Если примеров нет - пробуем Hunter.io
    print(f"  🥉 ШАГ 3: Hunter.io поиск")
    print(f"    🔍 GPT примеров нет, пробуем Hunter.io...")

    # Ограничиваем количество запрашиваемых email для экономии
    hunter_emails = await search_emails_hunter_io(domain, hunter_api_key)

    if hunter_emails:
        print(f"    ✅ Hunter.io нашел {len(hunter_emails)} email адресов:")
        for email in hunter_emails:
            print(f"      - {email}")

        # Фильтруем персональные email из Hunter.io
        personal_hunter_emails, _ = filter_personal_emails(hunter_emails)

        if personal_hunter_emails:
            print(f"    ✅ Используем персональные примеры от Hunter.io:")
            for email in personal_hunter_emails:
                print(f"      - {email}")

            # Используем первый персональный пример для GPT генерации
            example = personal_hunter_emails[0].split('@')[0]  # Только локальная часть
            print(f"    🎯 Пример для GPT генерации: {example}")
            return domain, "", example  # Паттерн пустой - используем GPT генерацию

    # Шаг 4: Если ни один метод не дал примеров - возвращаем домен без примера
    print(f"  ❌ ШАГ 4: Примеры email не найдены")
    print(f"    🔍 Ни один метод не нашел примеры корпоративных email")
    print(f"    🚫 Без примеров GPT генерация невозможна")
    print(f"    📧 Контакты будут возвращены без email адресов")

    # Возвращаем найденный домен (если есть) для информации, но без примера
    if domain:
        print(f"    📧 Сохраняем найденный домен: {domain}")
        return domain, "", ""  # Домен есть, но нет примера для генерации
    else:
        return "", "", ""  # Нет ни домена, ни примера

def analyze_email_pattern_smart(emails: List[str], contacts: List[Dict[str, Any]]) -> str:
    """Smart analysis of email pattern using both examples and contact names"""
    if not emails:
        return "first.last"

    print(f"    🧠 Умный анализ паттернов из {len(emails)} примеров...")

    # Фильтруем email адреса, которые могут содержать имена людей
    name_emails = []
    for email in emails:
        if "@" not in email:
            continue

        local_part = email.split("@")[0].lower()

        # Пропускаем служебные адреса
        service_keywords = ['info', 'admin', 'support', 'contact', 'office', 'mail', 'noreply', 'no-reply']
        if any(keyword in local_part for keyword in service_keywords):
            print(f"      ⏭️ Пропускаем служебный: {email}")
            continue

        # Пропускаем адреса с цифрами (вероятно не имена)
        if any(char.isdigit() for char in local_part):
            print(f"      ⏭️ Пропускаем с цифрами: {email}")
            continue

        # Пропускаем слишком короткие или длинные
        if len(local_part) < 3 or len(local_part) > 30:
            print(f"      ⏭️ Пропускаем по длине: {email}")
            continue

        name_emails.append(email)
        print(f"      ✅ Анализируем: {email}")

    if not name_emails:
        print(f"    ⚠️ Не найдено подходящих email для анализа, используем стандартный паттерн")
        return "first.last"

    # Анализируем структуру найденных email
    patterns_found = {}

    for email in name_emails:
        local_part = email.split("@")[0]

        if "." in local_part:
            parts = local_part.split(".")
            if len(parts) == 2:
                first_part, second_part = parts

                # Определяем тип паттерна по длине частей
                if len(first_part) == 1 and len(second_part) > 1:
                    pattern = "first_initial.last"
                elif len(first_part) > 1 and len(second_part) == 1:
                    pattern = "last.first_initial"
                elif len(first_part) > 1 and len(second_part) > 1:
                    pattern = "first.last"
                else:
                    continue

                patterns_found[pattern] = patterns_found.get(pattern, 0) + 1
                print(f"      📊 {email} → {pattern}")
        else:
            # Если нет точки, пытаемся понять по длине
            if len(local_part) > 8:  # Вероятно firstname+lastname
                patterns_found["first.last"] = patterns_found.get("first.last", 0) + 1
                print(f"      📊 {email} → first.last (без точки, длинный)")

    if not patterns_found:
        print(f"    ⚠️ Не удалось определить паттерн, используем стандартный")
        return "first.last"

    # Выбираем наиболее частый паттерн
    best_pattern = max(patterns_found.items(), key=lambda x: x[1])
    print(f"    🎯 Найденные паттерны: {patterns_found}")
    print(f"    ✅ Выбранный паттерн: {best_pattern[0]} (встречается {best_pattern[1]} раз)")

    return best_pattern[0]

def analyze_email_pattern(emails: List[str]) -> str:
    """Legacy function - kept for compatibility"""
    return analyze_email_pattern_smart(emails, [])

def generate_email_by_pattern(first_name: str, last_name: str, domain: str, pattern: str) -> str:
    """Generate email by pattern"""
    if not first_name or not last_name or not domain or not pattern:
        return ""

    # Транслитерируем имена
    first_en = transliterate_to_english(first_name)
    last_en = transliterate_to_english(last_name)

    if not first_en or not last_en:
        return ""

    # Генерируем email по паттерну
    if pattern == "first_initial.last":
        return f"{first_en[0]}.{last_en}@{domain}"
    elif pattern == "first.last":
        return f"{first_en}.{last_en}@{domain}"
    elif pattern == "last.first_initial":
        return f"{last_en}.{first_en[0]}@{domain}"
    else:
        # Fallback на стандартный паттерн
        return f"{first_en}.{last_en}@{domain}"

async def resolve_partial_name(client: "openai.AsyncClient", company_name: str, position: str, first_initial: str, last_initial: str) -> Dict[str, Any]:
    """Resolve partial name using GPT search"""
    prompt = f"""Найди полное имя и фамилию сотрудника компании "{company_name}":

Известные параметры:
- Компания: {company_name}
- Должность: {position}
- Имя начинается на: {first_initial}
- Фамилия начинается на: {last_initial}

Ищи в открытых источниках:
- LinkedIn профили
- Официальный сайт компании
- Пресс-релизы и новости
- Конференции и мероприятия
- Интервью в СМИ

Верни результат в JSON формате:
{{
  "first_name": "полное имя",
  "last_name": "полная фамилия",
  "confidence": "high|medium|low",
  "source": "источник информации"
}}"""

    msgs: List[ChatCompletionMessageParam] = [
        {"role": "system", "content": "You are an expert at finding people information from public sources. Return only valid JSON."},
        {"role": "user", "content": prompt},
    ]

    resp: ChatCompletion = await client.chat.completions.create(
        model=RESEARCH_MODEL,
        messages=msgs,
        timeout=TIMEOUT_SECS,
    )

    # Парсим ответ без JSON формата
    content = resp.choices[0].message.content or ""

    # Простой парсинг имени и фамилии из текста
    first_name = ""
    last_name = ""
    source = ""

    lines = content.split('\n')
    for line in lines:
        line = line.strip()
        if 'имя:' in line.lower():
            first_name = line.split(':', 1)[1].strip()
        elif 'фамилия:' in line.lower():
            last_name = line.split(':', 1)[1].strip()
        elif 'источник:' in line.lower():
            source = line.split(':', 1)[1].strip()

    # Если не найдено в структурированном виде, ищем в тексте
    if not first_name or not last_name:
        import re
        # Ищем паттерны типа "Александр Иванов", "А. Иванов" и т.д.
        name_patterns = re.findall(r'([А-ЯЁ][а-яё]+)\s+([А-ЯЁ][а-яё]+)', content)
        if name_patterns:
            first_name, last_name = name_patterns[0]

    confidence = "medium" if first_name and last_name else "low"

    return {"first_name": first_name, "last_name": last_name, "confidence": confidence, "source": source}

def transliterate_to_english(text: str) -> str:
    """Transliterate Russian text to English"""
    translit_dict = {
        'а': 'a', 'б': 'b', 'в': 'v', 'г': 'g', 'д': 'd', 'е': 'e', 'ё': 'e',
        'ж': 'zh', 'з': 'z', 'и': 'i', 'й': 'y', 'к': 'k', 'л': 'l', 'м': 'm',
        'н': 'n', 'о': 'o', 'п': 'p', 'р': 'r', 'с': 's', 'т': 't', 'у': 'u',
        'ф': 'f', 'х': 'h', 'ц': 'ts', 'ч': 'ch', 'ш': 'sh', 'щ': 'sch',
        'ъ': '', 'ы': 'y', 'ь': '', 'э': 'e', 'ю': 'yu', 'я': 'ya'
    }

    result = ""
    for char in text.lower():
        if char in translit_dict:
            result += translit_dict[char]
        elif char.isalpha():
            result += char  # Оставляем английские буквы как есть
        # Пропускаем все остальные символы

    return result

# Функция generate_email_variants удалена - теперь email генерируется через GPT

async def validate_email(email: str) -> bool:
    """Validate email using free email validation service"""
    if not REQUESTS_AVAILABLE:
        print(f"  ⚠️ requests не установлен, пропускаем валидацию {email}")
        return False

    try:
        # Используем бесплатный сервис для проверки email
        # Можно заменить на другой сервис по необходимости
        url = f"https://api.hunter.io/v2/email-verifier?email={email}&api_key=free"

        response = requests.get(url, timeout=10)
        if response.status_code == 200:
            data = response.json()
            # Проверяем результат валидации
            result = data.get("data", {}).get("result", "undeliverable")
            return result in ["deliverable", "risky"]
        else:
            # Fallback: простая проверка формата
            import re
            pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
            return bool(re.match(pattern, email))

    except Exception as e:
        print(f"  ⚠️ Ошибка валидации {email}: {e}")
        # Fallback: простая проверка формата
        import re
        pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        return bool(re.match(pattern, email))

def parse_contacts_from_text(text: str) -> List[Dict[str, Any]]:
    """Parse contacts from JSON array format: [["Имя", "Фамилия", "Должность"], ...]"""
    contacts = []

    try:
        # Сначала пытаемся найти JSON в тексте
        import json
        import re

        # Ищем JSON массив в тексте
        json_pattern = r'\[\s*\[.*?\]\s*\]'
        json_matches = re.findall(json_pattern, text, re.DOTALL)

        if json_matches:
            # Берем первый найденный JSON массив
            json_text = json_matches[0]
            print(f"    🔍 Найден JSON: {json_text[:200]}...")

            try:
                data = json.loads(json_text)

                if isinstance(data, list):
                    for item in data:
                        if isinstance(item, list) and len(item) >= 3:
                            # Формат: ["Имя", "Фамилия", "Должность"]
                            first_name = str(item[0]).strip()
                            last_name = str(item[1]).strip()
                            position = str(item[2]).strip()

                            if first_name and last_name and position:
                                contacts.append({
                                    'first_name': first_name,
                                    'last_name': last_name,
                                    'position': position,
                                    'confidence': 'high',
                                    'source': 'web_search'
                                })
                                print(f"      ✅ Добавлен: {last_name} {first_name} - {position}")

                print(f"    📊 Распарсено из JSON: {len(contacts)} контактов")
                return contacts

            except json.JSONDecodeError as e:
                print(f"    ⚠️ Ошибка парсинга JSON: {e}")

        # Fallback: старый формат CONTACT_START/END (если JSON не найден)
        print(f"    🔄 JSON не найден, пробуем старый формат...")
        lines = text.split('\n')
        current_contact = {}

        for line in lines:
            line = line.strip()

            if line == "CONTACT_START":
                current_contact = {}
            elif line == "CONTACT_END":
                if current_contact and all(key in current_contact for key in ['first_name', 'last_name', 'position']):
                    current_contact['source'] = 'web_search'
                    contacts.append(current_contact)
                current_contact = {}
            elif line.startswith("FIRST_NAME:"):
                current_contact['first_name'] = line.replace("FIRST_NAME:", "").strip()
            elif line.startswith("LAST_NAME:"):
                current_contact['last_name'] = line.replace("LAST_NAME:", "").strip()
            elif line.startswith("POSITION:"):
                current_contact['position'] = line.replace("POSITION:", "").strip()
            elif line.startswith("CONFIDENCE:"):
                current_contact['confidence'] = line.replace("CONFIDENCE:", "").strip()

        print(f"    📊 Распарсено из старого формата: {len(contacts)} контактов")
        return contacts

    except Exception as e:
        print(f"    ❌ Ошибка парсинга контактов: {e}")
        return []

async def get_contacts_from_setka(company_name: str, refresh_token: str) -> Tuple[List[Dict[str, Any]], str]:
    """Get contacts from Setka API using company_search.py"""
    try:
        # Импортируем функцию из company_search.py
        from company_search import search_company_employees

        # Извлекаем "сердце" названия компании (убираем хвосты, оставляем содержимое кавычек)
        clean_company_name = _extract_company_name_from_quotes(company_name)
        print(f"    🏢 Поиск в Setka для: '{clean_company_name}' (из '{company_name}')")

        # Получаем сотрудников через Setka API
        employees, new_refresh_token = await search_company_employees(
            company_name=clean_company_name,  # Используем очищенное название
            refresh_token=refresh_token,
            output_dir="/tmp",  # Временная директория
            keywords=["директор", "ИТ", "CIO", "CTO", "IT"]
        )

        # Преобразуем в формат JSON (возвращаем все контакты для дальнейшей обработки)
        contacts = []
        for last_name, first_name, position in employees:
            contacts.append({
                "first_name": first_name,
                "last_name": last_name,
                "position": position,
                "confidence": "high",
                "source": "setka_api"
            })

        return contacts, new_refresh_token

    except Exception as e:
        print(f"  ⚠️ Ошибка получения контактов из Setka: {e}")
        return [], refresh_token  # Возвращаем старый токен если ошибка

async def process_single_contact_with_email(client: "openai.AsyncClient", company_name: str, contact: Dict[str, Any], company_email_info: str) -> str:
    """Process single contact and find email address using GPT"""
    first_name = contact.get("first_name", "")
    last_name = contact.get("last_name", "")
    position = contact.get("position", "")
    source = contact.get("source", "web")

    print(f"  👤 Обработка: {last_name} {first_name} - {position} (источник: {source})")

    # Проверяем, есть ли частичные имена (например, А**** Е****)
    is_partial_first = "*" in first_name or len(first_name) <= 2
    is_partial_last = "*" in last_name or len(last_name) <= 2

    final_first_name = first_name
    final_last_name = last_name

    # Если имена частичные, пытаемся их расшифровать
    if is_partial_first or is_partial_last:
        print(f"    🔍 Расшифровка частичного имени...")
        first_initial = first_name[0] if first_name else ""
        last_initial = last_name[0] if last_name else ""

        resolved = await resolve_partial_name(client, company_name, position, first_initial, last_initial)

        if resolved.get("confidence") in ["high", "medium"]:
            final_first_name = resolved.get("first_name", first_name)
            final_last_name = resolved.get("last_name", last_name)
            print(f"    ✅ Расшифровано: {final_last_name} {final_first_name}")
        else:
            print(f"    ❌ Не удалось расшифровать")

    # Генерируем email через GPT на основе информации о компании
    valid_email = None
    if final_first_name and final_last_name and not ("*" in final_first_name or "*" in final_last_name):
        print(f"    📧 Генерация email через GPT...")

        try:
            generated_email = await generate_personal_email(client, final_first_name, final_last_name, company_email_info)

            if generated_email:
                print(f"    � Сгенерированный email: {generated_email}")

                # Валидируем сгенерированный email
                if await validate_email(generated_email):
                    valid_email = generated_email
                    print(f"    ✅ Email валиден: {generated_email}")
                else:
                    print(f"    ❌ Email не прошел валидацию: {generated_email}")
            else:
                print(f"    ❌ GPT не смог сгенерировать email")

        except Exception as e:
            print(f"    ❌ Ошибка генерации email: {e}")

    # Формируем строку результата
    contact_line = f"• {final_last_name} {final_first_name} — {position}"
    if valid_email:
        contact_line += f" ({valid_email})"

    return contact_line

async def process_contacts_with_emails_new_logic(client: "openai.AsyncClient", company_name: str, contacts: List[Dict[str, Any]], hunter_api_key: str = None, refresh_token: Optional[str] = None, tenders_dir: str = None) -> str:
    """Process contacts and find their email addresses using new logic with procurement analysis"""
    if not contacts:
        return "Контакты не найдены"

    # Разделяем контакты по источникам
    web_contacts = [c for c in contacts if c.get('source') == 'web_search']
    setka_contacts = [c for c in contacts if c.get('source') == 'setka_api']

    print(f"    📊 Разделение по источникам:")
    print(f"      🌐 Веб-поиск: {len(web_contacts)} контактов")
    print(f"      🔗 Setka API: {len(setka_contacts)} контактов")

    # НОВАЯ ЛОГИКА: Находим email домен и пример для всех контактов (с анализом закупок)
    print("  🔍 Поиск email паттерна для компании...")
    domain, _, email_example = await find_email_pattern_for_contacts(
        client, company_name, contacts, hunter_api_key, tenders_dir
    )

    if not domain:
        print("    ❌ Не удалось определить домен компании")
        # Возвращаем контакты без email
        result_sections = []

        if web_contacts:
            web_lines = [f"• {c.get('last_name', '')} {c.get('first_name', '')} — {c.get('position', '')}" for c in web_contacts]
            result_sections.append("**Контакты из открытых источников (веб-поиск):**\n" + "\n".join(web_lines))

        if setka_contacts:
            setka_lines = [f"• {c.get('last_name', '')} {c.get('first_name', '')} — {c.get('position', '')}" for c in setka_contacts]
            result_sections.append("**Контакты из Setka API:**\n" + "\n".join(setka_lines))

        return "\n\n".join(result_sections) if result_sections else "Контакты не найдены"

    if not email_example:
        print("    ❌ Не удалось найти примеры email для GPT генерации")
        # Возвращаем контакты без email
        result_sections = []

        if web_contacts:
            web_lines = [f"• {c.get('last_name', '')} {c.get('first_name', '')} — {c.get('position', '')}" for c in web_contacts]
            result_sections.append("**Контакты из открытых источников (веб-поиск):**\n" + "\n".join(web_lines))

        if setka_contacts:
            setka_lines = [f"• {c.get('last_name', '')} {c.get('first_name', '')} — {c.get('position', '')}" for c in setka_contacts]
            result_sections.append("**Контакты из Setka API:**\n" + "\n".join(setka_lines))

        return "\n\n".join(result_sections) if result_sections else "Контакты не найдены"

    print(f"    ✅ Найден домен: {domain}")
    print(f"    ✅ Найден пример для GPT генерации: {email_example}")
    print(f"    🧠 Используем GPT генерацию по примеру для всех контактов")

    result_sections = []

    # Обрабатываем веб-контакты с новой логикой
    if web_contacts:
        print(f"\n  🌐 Обработка контактов из веб-поиска ({len(web_contacts)})...")
        web_lines = []
        for contact in web_contacts:
            try:
                # Используем GPT генерацию по примеру (пример гарантированно есть)
                contact_line = await process_single_contact_with_example(client, contact, domain, email_example)
                web_lines.append(contact_line)
                print(f"    ✅ {contact_line}")
            except Exception as e:
                print(f"    ❌ Ошибка обработки веб-контакта: {e}")
                # Добавляем контакт без email
                first_name = contact.get("first_name", "")
                last_name = contact.get("last_name", "")
                position = contact.get("position", "")
                web_lines.append(f"• {last_name} {first_name} — {position}")

        if web_lines:
            result_sections.append("**Контакты из открытых источников (веб-поиск):**\n" + "\n".join(web_lines))

    # Обрабатываем Setka контакты с новой логикой
    if setka_contacts:
        print(f"\n  🔗 Обработка контактов из Setka API ({len(setka_contacts)})...")
        setka_lines = []
        for contact in setka_contacts:
            try:
                # Используем GPT генерацию по примеру (пример гарантированно есть)
                contact_line = await process_single_contact_with_example(client, contact, domain, email_example)
                setka_lines.append(contact_line)
                print(f"    ✅ {contact_line}")
            except Exception as e:
                print(f"    ❌ Ошибка обработки Setka-контакта: {e}")
                # Добавляем контакт без email
                first_name = contact.get("first_name", "")
                last_name = contact.get("last_name", "")
                position = contact.get("position", "")
                setka_lines.append(f"• {last_name} {first_name} — {position}")

        if setka_lines:
            result_sections.append("**Контакты из Setka API:**\n" + "\n".join(setka_lines))

    return "\n\n".join(result_sections) if result_sections else "Контакты не найдены"

def process_single_contact_new_logic(contact: Dict[str, Any], domain: str, email_pattern: str, email_example: str = "") -> str:
    """Process single contact with new logic (supports procurement examples)"""
    first_name = contact.get("first_name", "")
    last_name = contact.get("last_name", "")
    position = contact.get("position", "")

    # Генерируем email по найденному паттерну или примеру
    if email_example:
        # Если есть пример из закупок - используем стандартную генерацию по паттерну
        # (пример будет использован в асинхронной функции)
        print(f"      🎯 Используем пример из закупок: {email_example}")
        email = generate_email_by_pattern(first_name, last_name, domain, email_pattern)
    else:
        # Используем стандартную генерацию по паттерну
        email = generate_email_by_pattern(first_name, last_name, domain, email_pattern)

    # Формируем строку результата
    contact_line = f"• {last_name} {first_name} — {position}"
    if email:
        contact_line += f" ({email})"

    return contact_line

async def process_single_contact_with_example(client: "openai.AsyncClient", contact: Dict[str, Any], domain: str, email_example: str) -> str:
    """Process single contact using procurement example (for complex patterns)"""
    first_name = contact.get("first_name", "")
    last_name = contact.get("last_name", "")
    position = contact.get("position", "")

    # Генерируем email по примеру из закупок через GPT
    email = await generate_email_by_example(client, first_name, last_name, email_example, domain)

    # Формируем строку результата
    contact_line = f"• {last_name} {first_name} — {position}"
    if email:
        contact_line += f" ({email})"

    return contact_line

# --------------------------------------------------------------------
# Company profile collection (from original)
# --------------------------------------------------------------------
async def _collect(company: str, inn: str, tenders_dir: Optional[str], research_model: str = RESEARCH_MODEL, refresh_token: Optional[str] = None) -> Dict[int, str]:
    if not OPENAI_AVAILABLE:
        raise ImportError("openai package required for sections 1‑2")

    client = openai.AsyncClient(api_key=os.getenv("OPENAI_API_KEY"))  # type: ignore[arg-type]
    res: Dict[int, str] = {}

    # Выполняем только 2 основных поисковых запроса
    # Для секции 2 (контакты) передаем refresh_token и tenders_dir
    tasks = {}
    for i in range(1, 3):
        if i == 2:  # Секция контактов
            tasks[i] = asyncio.create_task(_gather_section(client, company, inn, i, research_model, refresh_token, tenders_dir))
        else:
            tasks[i] = asyncio.create_task(_gather_section(client, company, inn, i, research_model))

    for i, t in tasks.items():
        res[i] = await t

    # Добавляем секцию с закупками
    res[3] = _scan_tenders(tenders_dir, company)

    return res

async def collect_company_profile_async(company: str, inn: str = "", tenders_dir: Optional[str] = None, research_model: str = RESEARCH_MODEL, refresh_token: Optional[str] = None) -> str:
    """Collect company profile and return as markdown string (async version)"""
    if not OPENAI_AVAILABLE:
        raise ImportError("Install the 'openai' package: pip install openai")
    if not os.getenv("OPENAI_API_KEY"):
        raise EnvironmentError("OPENAI_API_KEY not set (.env or export)")

    sections = await _collect(company, inn, tenders_dir, research_model, refresh_token)

    company_title = f"{company}"
    if inn:
        company_title = f"{company} (ИНН: {inn})"

    md: List[str] = [f"**Профиль компании «{company_title}»**\n"]
    for idx in sorted(SECTIONS):
        md.append(f"### {idx}. {SECTIONS[idx]}\n")
        md.append((sections[idx] or "Не найдено") + "\n")
    return "\n".join(md)

def collect_company_profile(company: str, inn: str = "", tenders_dir: Optional[str] = None, research_model: str = RESEARCH_MODEL, refresh_token: Optional[str] = None) -> str:
    """Collect company profile and return as markdown string (sync wrapper)"""
    if not OPENAI_AVAILABLE:
        raise ImportError("Install the 'openai' package: pip install openai")
    if not os.getenv("OPENAI_API_KEY"):
        raise EnvironmentError("OPENAI_API_KEY not set (.env or export)")

    try:
        # Try to get existing event loop
        loop = asyncio.get_event_loop()
        if loop.is_running():
            # If loop is running, we need to use a different approach
            import concurrent.futures
            with concurrent.futures.ThreadPoolExecutor() as executor:
                future = executor.submit(asyncio.run, collect_company_profile_async(company, inn, tenders_dir, research_model, refresh_token))
                return future.result()
        else:
            return loop.run_until_complete(collect_company_profile_async(company, inn, tenders_dir, research_model, refresh_token))
    except RuntimeError:
        # No event loop exists, create a new one
        return asyncio.run(collect_company_profile_async(company, inn, tenders_dir, research_model, refresh_token))

# --------------------------------------------------------------------
# New functionality: Excel processing and email generation
# --------------------------------------------------------------------

def read_companies_from_excel(
    excel_path: str,
    company_column: str = "D",
    inn_column: str = "E",
    am_column: str = "AM"
) -> List[Tuple[str, str, str]]:
    """Read companies, their INNs and AMs from Excel file with deduplication

    Args:
        excel_path: Path to Excel file
        company_column: Name or letter of the column containing company names (default: "D")
        inn_column: Name or letter of the column containing INNs (default: "E")
        am_column: Name of the column containing AM names (default: "AM")

    Returns:
        List of tuples (company_name, inn, am_name) - deduplicated by company name and INN
    """
    if pd is None:
        raise ImportError("pandas не установлен")

    def get_column_index(df, column_identifier):
        """Get column index by name or letter"""
        if isinstance(column_identifier, str) and len(column_identifier) == 1 and column_identifier.isalpha():
            # Convert letter to index (A=0, B=1, C=2, D=3, etc.)
            return ord(column_identifier.upper()) - ord('A')
        elif column_identifier in df.columns:
            return df.columns.get_loc(column_identifier)
        else:
            return None

    try:
        # Read Excel with all columns as strings to preserve INN leading zeros
        df = pd.read_excel(excel_path, engine="openpyxl", dtype=str, keep_default_na=False)
        companies = []

        # Print column names for debugging
        print(f"Доступные столбцы: {list(df.columns)}")

        # Get column indices
        company_col_index = get_column_index(df, company_column)
        inn_col_index = get_column_index(df, inn_column)

        if company_col_index is None:
            raise ValueError(f"Столбец с названиями компаний '{company_column}' не найден")

        if inn_col_index is None:
            print(f"Предупреждение: Столбец с ИНН '{inn_column}' не найден, ИНН не будут использоваться")

        # Find AM column
        am_col_index = None
        if am_column in df.columns:
            am_col_index = df.columns.get_loc(am_column)
        else:
            # Try to find AM column by common names
            am_variants = ["AM", "АМ", "Account Manager", "Менеджер", "Manager", "Ответственный"]
            for variant in am_variants:
                if variant in df.columns:
                    am_col_index = df.columns.get_loc(variant)
                    print(f"Найден столбец АМ: {variant}")
                    break

        if am_col_index is None:
            print("Предупреждение: Столбец АМ не найден, будет использовано значение по умолчанию")

        # Track processed companies to avoid duplicates
        processed_companies = set()  # Set of (company_name_lower, inn) tuples
        companies = []
        duplicates_count = 0

        # Process rows
        for index, row in df.iterrows():
            # Skip header row if it contains column names
            if index == 0 and company_col_index < len(row):
                cell_value = str(row.iloc[company_col_index]).lower()
                if any(keyword in cell_value for keyword in ["заказчик", "компания", "организация", "название"]):
                    continue

            # Get company name from specified column
            if company_col_index < len(row):
                company_value = row.iloc[company_col_index]
                if company_value is not None and str(company_value).lower() not in ["nan", "none", ""]:
                    company_name = str(company_value).strip()
                else:
                    company_name = ""
            else:
                company_name = ""

            # Get INN from specified column
            inn = ""
            if inn_col_index is not None and inn_col_index < len(row):
                inn_value = row.iloc[inn_col_index]
                if inn_value is not None and str(inn_value).lower() not in ["nan", "none", ""]:
                    inn = str(inn_value).strip()
                    # Handle cases where pandas converts INN to float (e.g., 1234567890.0)
                    if inn.endswith('.0'):
                        inn = inn[:-2]

            # Skip empty or invalid company names
            if not company_name or company_name.lower().strip() in ["nan", "", "заказчик (из контур.закупки)"]:
                continue

            # Create deduplication key
            # Use both company name and INN for deduplication
            company_key = (company_name.lower().strip(), inn.strip())

            # Check if this company was already processed
            if company_key in processed_companies:
                duplicates_count += 1
                print(f"  Пропущен дубликат: {company_name}" + (f" (ИНН: {inn})" if inn else ""))
                continue

            # Add to processed set
            processed_companies.add(company_key)

            # Get AM name
            if am_col_index is not None and am_col_index < len(row):
                am_value = row.iloc[am_col_index]
                if am_value is not None and str(am_value).lower() not in ["nan", "none", ""]:
                    am_name = str(am_value).strip()
                else:
                    am_name = "AM_Default"
            else:
                am_name = "AM_Default"

            companies.append((company_name, inn, am_name))

        print(f"Обработано {len(companies)} уникальных компаний")
        if duplicates_count > 0:
            print(f"Пропущено {duplicates_count} дубликатов")
        return companies

    except Exception as e:
        raise Exception(f"Ошибка при чтении Excel файла: {e}")

def read_pdf_content(pdf_path: str) -> str:
    """Read content from PDF file"""
    try:
        import PyPDF2
        with open(pdf_path, 'rb') as file:
            reader = PyPDF2.PdfReader(file)
            text = ""
            for page in reader.pages:
                text += page.extract_text()
        return text
    except ImportError:
        raise ImportError("PyPDF2 не установлен. Установите: pip install PyPDF2")
    except Exception as e:
        raise Exception(f"Ошибка при чтении PDF файла {pdf_path}: {e}")

async def generate_personalized_email(
    company_name: str,
    company_profile: str,
    template_pdf_path: str,
    nova_ai_pdf_path: str,
    email_model: str = EMAIL_MODEL,
    reasoning_effort: str = O4_REASONING_EFFORT
) -> str:
    """Generate personalized email using GPT-4"""
    if not OPENAI_AVAILABLE:
        raise ImportError("openai package required")
    
    # Read template and Nova AI positioning
    template_content = read_pdf_content(template_pdf_path)
    nova_ai_content = read_pdf_content(nova_ai_pdf_path)
    
    # Prepare the prompt
    user_content = f"""
Компания: {company_name}

Профиль компании:
{company_profile}

Шаблон письма:
{template_content}

Позиционирование Nova AI:
{nova_ai_content}
"""
    
    client = openai.AsyncClient(api_key=os.getenv("OPENAI_API_KEY"))  # type: ignore[arg-type]
    
    msgs: List[ChatCompletionMessageParam] = [
        {"role": "system", "content": EMAIL_GENERATION_PROMPT},
        {"role": "user", "content": user_content},
    ]
    
    # Use o4-mini model for email generation with reasoning effort
    try:
        resp: ChatCompletion = await client.chat.completions.create(
            model=email_model,
            messages=msgs,
            timeout=TIMEOUT_SECS,
            reasoning_effort=reasoning_effort  # o3 specific parameter
        )
    except Exception as e:
        # Fallback to standard parameters if reasoning_effort is not supported
        if "reasoning_effort" in str(e) or "unexpected keyword argument" in str(e):
            print(f"  ⚠ Параметр reasoning_effort не поддерживается, используем стандартные параметры")
            resp: ChatCompletion = await client.chat.completions.create(
                model=email_model,
                messages=msgs,
                timeout=TIMEOUT_SECS,
            )
        else:
            raise e
    
    return resp.choices[0].message.content or ""

def create_word_document(email_content: str, company_profile: str, output_path: str, company_name: str) -> None:
    """Create Word document with new structure: ML/AI projects, Contacts, Tenders, Email"""
    if not DOCX_AVAILABLE:
        raise ImportError("python-docx не установлен. Установите: pip install python-docx")

    # Create new document
    doc = Document()

    # Add title
    title = doc.add_heading(f'Ценностное предложение для {company_name}', 0)
    title.alignment = WD_ALIGN_PARAGRAPH.CENTER

    # Add spacing
    doc.add_paragraph()

    # Parse company profile sections
    sections_content = {}
    current_section = None
    current_content = []

    for line in company_profile.split('\n'):
        if line.startswith('### '):
            # Save previous section
            if current_section:
                sections_content[current_section] = '\n'.join(current_content)

            # Start new section
            current_section = line.replace('### ', '').strip()
            current_content = []
        elif line.strip():
            current_content.append(line)

    # Save last section
    if current_section:
        sections_content[current_section] = '\n'.join(current_content)

    # Add sections in the new order (only profile sections, not email)
    section_order = [
        "1. Основные ML/AI‑проекты",
        "2. Контакты LPR + телефоны/e‑mail",
        "3. Релевантные закупки с kontur.zakupki"
    ]

    for section_title in section_order:
        if section_title in sections_content:
            doc.add_heading(section_title, level=1)
            content = sections_content[section_title]

            # Handle different content types
            if content and content != "Не найдено":
                paragraphs = content.split('\n')
                for para in paragraphs:
                    if para.strip():
                        doc.add_paragraph(para.strip())
            else:
                doc.add_paragraph("Информация не найдена")

            doc.add_paragraph()  # Add spacing

    # Add separator
    doc.add_page_break()

    # Add email section
    doc.add_heading('Кастомизированное письмо', level=1)

    # Add email content
    if email_content and email_content.strip():
        email_paragraphs = email_content.split('\n\n')
        for para in email_paragraphs:
            if para.strip():
                doc.add_paragraph(para.strip())
    else:
        doc.add_paragraph("Письмо не сгенерировано")

    # Save document
    doc.save(output_path)

def create_pdf_document_fallback(email_content: str, company_profile: str, output_path: str, company_name: str) -> None:
    """Fallback PDF creation function (if Word is not available)"""
    if not REPORTLAB_AVAILABLE:
        raise ImportError("reportlab не установлен. Установите: pip install reportlab")
    
    # Create the PDF document
    doc = SimpleDocTemplate(output_path, pagesize=A4)
    styles = getSampleStyleSheet()
    
    # Create custom style for Russian text
    custom_style = ParagraphStyle(
        'CustomStyle',
        parent=styles['Normal'],
        fontName='Helvetica',
        fontSize=12,
        spaceAfter=12,
        encoding='utf-8'
    )
    
    # Build the document
    story = []
    
    # Add title
    title_style = ParagraphStyle(
        'TitleStyle',
        parent=styles['Title'],
        fontName='Helvetica-Bold',
        fontSize=16,
        spaceAfter=20
    )
    
    story.append(Paragraph(f"Ценностное предложение для {company_name}", title_style))
    story.append(Spacer(1, 0.2*inch))
    
    # Add company profile
    story.append(Paragraph("Профиль компании", title_style))
    story.append(Spacer(1, 0.1*inch))
    
    profile_paragraphs = company_profile.split('\n\n')
    for para in profile_paragraphs:
        if para.strip():
            story.append(Paragraph(para.strip(), custom_style))
            story.append(Spacer(1, 0.05*inch))
    
    story.append(Spacer(1, 0.2*inch))
    
    # Add email content
    story.append(Paragraph("Персонализированное письмо", title_style))
    story.append(Spacer(1, 0.1*inch))
    
    email_paragraphs = email_content.split('\n\n')
    for para in email_paragraphs:
        if para.strip():
            story.append(Paragraph(para.strip(), custom_style))
            story.append(Spacer(1, 0.1*inch))
    
    # Build PDF
    doc.build(story)

def create_am_folders_and_documents(
    companies_data: List[Tuple[str, str, str, str, str]],  # (company, inn, am, profile, email)
    output_dir: str
) -> None:
    """Create AM folders and Word/PDF documents for each company"""

    # Group companies by AM
    am_companies: Dict[str, List[Tuple[str, str, str, str]]] = {}
    for company, inn, am, profile, email in companies_data:
        if am not in am_companies:
            am_companies[am] = []
        am_companies[am].append((company, inn, profile, email))
    
    # Create output directory if it doesn't exist
    Path(output_dir).mkdir(parents=True, exist_ok=True)
    
    # Create folders and documents for each AM
    for am_name, companies in am_companies.items():
        am_folder = Path(output_dir) / am_name
        am_folder.mkdir(exist_ok=True)

        for company_name, inn, profile, email in companies:
            # Create filename (sanitize company name)
            safe_company_name = re.sub(r'[^\w\s-]', '', company_name).strip()
            safe_company_name = re.sub(r'[-\s]+', '-', safe_company_name)
            
            # Try Word format first, fallback to PDF
            if DOCX_AVAILABLE:
                doc_filename = f"{safe_company_name}.docx"
                doc_path = am_folder / doc_filename
                
                try:
                    create_word_document(email, profile, str(doc_path), company_name)
                    print(f"✓ Создан Word документ: {doc_path}")
                except Exception as e:
                    print(f"✗ Ошибка создания Word документа для {company_name}: {e}")
                    # Try PDF fallback
                    if REPORTLAB_AVAILABLE:
                        pdf_filename = f"{safe_company_name}.pdf"
                        pdf_path = am_folder / pdf_filename
                        try:
                            create_pdf_document_fallback(email, profile, str(pdf_path), company_name)
                            print(f"✓ Создан PDF (fallback): {pdf_path}")
                        except Exception as pdf_e:
                            print(f"✗ Ошибка создания PDF для {company_name}: {pdf_e}")
            else:
                # Use PDF if Word is not available
                if REPORTLAB_AVAILABLE:
                    pdf_filename = f"{safe_company_name}.pdf"
                    pdf_path = am_folder / pdf_filename
                    try:
                        create_pdf_document_fallback(email, profile, str(pdf_path), company_name)
                        print(f"✓ Создан PDF: {pdf_path}")
                    except Exception as e:
                        print(f"✗ Ошибка создания PDF для {company_name}: {e}")
                else:
                    print(f"✗ Нет доступных библиотек для создания документов для {company_name}")

# --------------------------------------------------------------------
# Main processing function
# --------------------------------------------------------------------

async def process_companies_batch(
    excel_path: str,
    template_pdf_path: str,
    nova_ai_pdf_path: str,
    output_dir: str,
    tenders_dir: Optional[str] = None,
    company_column: str = "D",
    inn_column: str = "E",
    am_column: str = "AM",
    research_model: str = RESEARCH_MODEL,
    email_model: str = EMAIL_MODEL,
    reasoning_effort: str = O4_REASONING_EFFORT,
    refresh_token: Optional[str] = None
) -> None:
    """Main function to process all companies from Excel"""
    
    # Validate input files
    print("Проверка входных файлов...")
    for path, name in [(excel_path, "Excel"), (template_pdf_path, "Template"), (nova_ai_pdf_path, "Nova AI")]:
        if not os.path.exists(path):
            raise FileNotFoundError(f"{name} файл не найден: {path}")
    
    # Validate dependencies
    if not OPENAI_AVAILABLE:
        raise ImportError("OpenAI package не установлен. Установите: pip install openai")
    
    if not os.getenv("OPENAI_API_KEY"):
        raise EnvironmentError("OPENAI_API_KEY не установлен в переменных окружения")
    
    if pd is None:
        raise ImportError("pandas не установлен. Установите: pip install pandas openpyxl")
    
    try:
        import PyPDF2
    except ImportError:
        raise ImportError("PyPDF2 не установлен. Установите: pip install PyPDF2")
    
    if not DOCX_AVAILABLE and not REPORTLAB_AVAILABLE:
        raise ImportError("Не установлены библиотеки для создания документов. Установите: pip install python-docx или pip install reportlab")
    
    print("Чтение компаний из Excel файла...")
    try:
        companies = read_companies_from_excel(excel_path, company_column, inn_column, am_column)
    except Exception as e:
        raise Exception(f"Ошибка чтения Excel файла: {e}")

    if not companies:
        raise ValueError("В Excel файле не найдено ни одной компании для обработки")

    print(f"Найдено {len(companies)} компаний")

    companies_data = []
    failed_companies = []

    for i, (company_name, inn, am_name) in enumerate(companies, 1):
        inn_info = f" (ИНН: {inn})" if inn else ""
        print(f"\n[{i}/{len(companies)}] Обработка: {company_name}{inn_info} (АМ: {am_name})")

        try:
            # Collect company profile
            print("  Сбор профиля компании...")
            company_profile = await collect_company_profile_async(company_name, inn, tenders_dir, research_model, refresh_token)
            
            if not company_profile or len(company_profile.strip()) < 50:
                print("  ⚠ Предупреждение: Получен слишком короткий профиль компании")
            
            # Generate personalized email
            print("  Генерация персонализированного письма...")
            email_content = await generate_personalized_email(
                company_name,
                company_profile,
                template_pdf_path,
                nova_ai_pdf_path,
                email_model,
                reasoning_effort
            )
            
            if not email_content or len(email_content.strip()) < 100:
                print("  ⚠ Предупреждение: Получено слишком короткое письмо")
            
            companies_data.append((company_name, inn, am_name, company_profile, email_content))
            print(f"  ✓ Обработка завершена")
            
        except Exception as e:
            error_msg = f"Ошибка обработки {company_name}: {e}"
            print(f"  ✗ {error_msg}")
            failed_companies.append((company_name, str(e)))
            continue
    
    if not companies_data:
        raise Exception("Не удалось обработать ни одной компании")
    
    # Create AM folders and documents
    print(f"\nСоздание папок АМ и документов в {output_dir}...")
    try:
        create_am_folders_and_documents(companies_data, output_dir)
    except Exception as e:
        raise Exception(f"Ошибка создания документов: {e}")
    
    # Summary
    print(f"\n{'='*50}")
    print(f"✓ Обработка завершена!")
    print(f"✓ Успешно обработано: {len(companies_data)} компаний")
    print(f"✓ Создано документов: {len(companies_data)}")
    
    if failed_companies:
        print(f"✗ Ошибки обработки: {len(failed_companies)} компаний")
        for company, error in failed_companies:
            print(f"  - {company}: {error}")
    
    print(f"📁 Результаты сохранены в: {output_dir}")

# --------------------------------------------------------------------
# CLI interface
# --------------------------------------------------------------------

def _cli() -> None:
    parser = argparse.ArgumentParser(
        description="Enhanced Lead-finder with personalized email generation",
        formatter_class=argparse.RawTextHelpFormatter,
        epilog="""Примеры:
  # Базовое использование
  python leadgen_enhanced.py --excel companies.xlsx --template template.pdf --nova-ai nova_ai.pdf --output ./am_folders

  # С указанием столбцов компаний, ИНН, АМ и каталога тендеров
  python leadgen_enhanced.py --excel companies.xlsx --template template.pdf --nova-ai nova_ai.pdf --output ./am_folders --company-column "C" --inn-column "D" --am-column "Менеджер" --tenders ./tenders

  # Запуск тестов
  python leadgen_enhanced.py --test
""",
    )
    parser.add_argument("--excel", help="Excel файл с компаниями")
    parser.add_argument("--template", help="PDF файл с шаблоном письма Nova AI")
    parser.add_argument("--nova-ai", help="PDF файл с позиционированием Nova AI")
    parser.add_argument("--output", help="Директория для создания папок АМ с документами")
    parser.add_argument("--company-column", default="D", help="Столбец с названиями компаний (буква или название, по умолчанию: 'D')")
    parser.add_argument("--inn-column", default="E", help="Столбец с ИНН компаний (буква или название, по умолчанию: 'E')")
    parser.add_argument("--am-column", default="AM", help="Название столбца с именами АМ (по умолчанию: 'AM')")
    parser.add_argument("--tenders", help="Каталог *.xls[x] файлов с kontur.zakupki (опционально)")
    parser.add_argument("--research-model", default=RESEARCH_MODEL, help=f"Модель для исследования компаний (по умолчанию: {RESEARCH_MODEL})")
    parser.add_argument("--email-model", default=EMAIL_MODEL, help=f"Модель для генерации писем (по умолчанию: {EMAIL_MODEL})")
    parser.add_argument("--reasoning-effort", default=O4_REASONING_EFFORT, choices=["low", "medium", "high"], help=f"Уровень reasoning для o4-mini модели (по умолчанию: {O4_REASONING_EFFORT})")
    parser.add_argument("--refresh-token", help="Refresh токен для Setka API (опционально, для расширенного поиска контактов)")
    parser.add_argument("--test", action="store_true", help="Запустить unit-тесты и выйти")
    args = parser.parse_args()

    if args.test:
        # Run tests
        sys.argv = [sys.argv[0]]
        unittest.main()
        return

    if not all([args.excel, args.template, args.nova_ai, args.output]):
        parser.error("Обязательные параметры: --excel, --template, --nova-ai, --output")

    print("🚀 Запуск Enhanced Lead Generation System")
    print(f"📊 Excel файл: {args.excel}")
    print(f"📄 Шаблон письма: {args.template}")
    print(f"🤖 Nova AI позиционирование: {args.nova_ai}")
    print(f"📁 Выходная директория: {args.output}")
    print(f"🏢 Столбец компаний: {args.company_column}")
    print(f"🔢 Столбец ИНН: {args.inn_column}")
    print(f"👤 Столбец АМ: {args.am_column}")
    print(f"🔍 Модель исследования: {args.research_model}")
    print(f"✉️ Модель писем: {args.email_model}")
    print(f"🧠 Reasoning effort: {args.reasoning_effort}")
    if args.tenders:
        print(f"📋 Каталог тендеров: {args.tenders}")
    if args.refresh_token:
        print(f"🔑 Setka API токен: установлен")
        print("📞 Расширенный поиск контактов: включен")
    else:
        print("📞 Расширенный поиск контактов: отключен (нет Setka токена)")
    print("-" * 50)

    # Run the main processing
    loop = asyncio.new_event_loop()
    asyncio.set_event_loop(loop)
    try:
        loop.run_until_complete(process_companies_batch(
            args.excel,
            args.template,
            args.nova_ai,
            args.output,
            args.tenders,
            args.company_column,
            args.inn_column,
            args.am_column,
            args.research_model,
            args.email_model,
            args.reasoning_effort,
            args.refresh_token
        ))
    except Exception as e:
        print(f"\n❌ Критическая ошибка: {e}")
        sys.exit(1)
    finally:
        loop.close()

if __name__ == "__main__":
    _cli()