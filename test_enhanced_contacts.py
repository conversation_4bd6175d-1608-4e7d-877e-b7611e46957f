"""test_enhanced_contacts.py
Тест модернизированного функционала поиска контактов
"""

import os
import sys
import asyncio
import json

# Устанавливаем API ключ для тестирования
api_key = os.getenv("OPENAI_API_KEY")
if not api_key:
    print("❌ Установите OPENAI_API_KEY для тестирования")
    sys.exit(1)

# Предотвращаем запуск CLI
sys.argv = ['test']

async def test_structured_contacts():
    """Тест structured output для контактов"""
    print("🧪 ТЕСТ STRUCTURED OUTPUT ДЛЯ КОНТАКТОВ")
    print("=" * 60)
    
    try:
        from leadgen_enhanced import _run_subquery, SECTION_QUERIES
        import openai
        
        client = openai.AsyncClient(api_key=api_key)
        
        # Тестируем запрос контактов с JSON форматом
        company = "Сбербанк"
        inn = ""
        query = SECTION_QUERIES[2][0]  # Запрос для контактов
        
        print(f"🏢 Компания: {company}")
        print(f"📝 Запрос: {query[:100]}...")
        
        result = await _run_subquery(client, company, inn, query, 2)
        
        print(f"📊 Результат (первые 500 символов):")
        print(result[:500])
        
        # Пытаемся распарсить JSON
        try:
            data = json.loads(result)
            contacts = data.get("contacts", [])
            print(f"\n✅ JSON успешно распарсен")
            print(f"👥 Найдено контактов: {len(contacts)}")
            
            for i, contact in enumerate(contacts[:3], 1):
                print(f"  {i}. {contact.get('last_name', '')} {contact.get('first_name', '')} - {contact.get('position', '')}")
                print(f"     Уверенность: {contact.get('confidence', 'unknown')}")
            
            return True
            
        except json.JSONDecodeError as e:
            print(f"❌ Ошибка парсинга JSON: {e}")
            return False
            
    except Exception as e:
        print(f"❌ Ошибка теста: {e}")
        return False

async def test_email_domain_search():
    """Тест поиска домена и шаблонов email"""
    print("\n🧪 ТЕСТ ПОИСКА EMAIL ДОМЕНА")
    print("=" * 60)
    
    try:
        from leadgen_enhanced import search_email_domain_pattern
        import openai
        
        client = openai.AsyncClient(api_key=api_key)
        
        company = "Сбербанк"
        print(f"🏢 Компания: {company}")
        
        result = await search_email_domain_pattern(client, company)
        
        print(f"📧 Результат поиска домена:")
        print(f"  Домен: {result.get('domain', 'не найден')}")
        print(f"  Шаблоны: {result.get('patterns', [])}")
        print(f"  Уверенность: {result.get('confidence', 'unknown')}")
        
        return bool(result.get('domain'))
        
    except Exception as e:
        print(f"❌ Ошибка теста: {e}")
        return False

async def test_partial_name_resolution():
    """Тест расшифровки частичных имен"""
    print("\n🧪 ТЕСТ РАСШИФРОВКИ ЧАСТИЧНЫХ ИМЕН")
    print("=" * 60)
    
    try:
        from leadgen_enhanced import resolve_partial_name
        import openai
        
        client = openai.AsyncClient(api_key=api_key)
        
        company = "Сбербанк"
        position = "CTO"
        first_initial = "А"
        last_initial = "И"
        
        print(f"🏢 Компания: {company}")
        print(f"💼 Должность: {position}")
        print(f"👤 Имя начинается на: {first_initial}")
        print(f"👤 Фамилия начинается на: {last_initial}")
        
        result = await resolve_partial_name(client, company, position, first_initial, last_initial)
        
        print(f"🔍 Результат расшифровки:")
        print(f"  Имя: {result.get('first_name', 'не найдено')}")
        print(f"  Фамилия: {result.get('last_name', 'не найдена')}")
        print(f"  Уверенность: {result.get('confidence', 'unknown')}")
        print(f"  Источник: {result.get('source', 'не указан')}")
        
        return bool(result.get('first_name') and result.get('last_name'))
        
    except Exception as e:
        print(f"❌ Ошибка теста: {e}")
        return False

def test_email_generation():
    """Тест генерации вариантов email"""
    print("\n🧪 ТЕСТ ГЕНЕРАЦИИ EMAIL ВАРИАНТОВ")
    print("=" * 60)
    
    try:
        from leadgen_enhanced import generate_email_variants
        
        first_name = "Александр"
        last_name = "Иванов"
        domain = "sberbank.ru"
        patterns = [
            "name.surname@domain",
            "n.surname@domain",
            "name@domain"
        ]
        
        print(f"👤 Имя: {first_name}")
        print(f"👤 Фамилия: {last_name}")
        print(f"🌐 Домен: {domain}")
        print(f"📝 Шаблоны: {patterns}")
        
        emails = generate_email_variants(first_name, last_name, domain, patterns)
        
        print(f"📧 Сгенерированные email варианты:")
        for i, email in enumerate(emails, 1):
            print(f"  {i}. {email}")
        
        return len(emails) > 0
        
    except Exception as e:
        print(f"❌ Ошибка теста: {e}")
        return False

async def test_email_validation():
    """Тест валидации email"""
    print("\n🧪 ТЕСТ ВАЛИДАЦИИ EMAIL")
    print("=" * 60)
    
    try:
        from leadgen_enhanced import validate_email
        
        test_emails = [
            "<EMAIL>",
            "invalid-email",
            "<EMAIL>"
        ]
        
        results = []
        for email in test_emails:
            print(f"📧 Проверка: {email}")
            is_valid = await validate_email(email)
            print(f"  Результат: {'✅ валидный' if is_valid else '❌ невалидный'}")
            results.append(is_valid)
        
        return True  # Тест считается успешным если не упал
        
    except Exception as e:
        print(f"❌ Ошибка теста: {e}")
        return False

async def test_full_contacts_processing():
    """Тест полной обработки контактов"""
    print("\n🧪 ТЕСТ ПОЛНОЙ ОБРАБОТКИ КОНТАКТОВ")
    print("=" * 60)
    
    try:
        from leadgen_enhanced import process_contacts_with_emails
        import openai
        
        client = openai.AsyncClient(api_key=api_key)
        
        # Тестовые контакты
        test_contacts = [
            {
                "first_name": "Александр",
                "last_name": "Иванов",
                "position": "CTO",
                "confidence": "high",
                "source": "test"
            },
            {
                "first_name": "А****",
                "last_name": "П****",
                "position": "CIO",
                "confidence": "medium",
                "source": "test"
            }
        ]
        
        company = "Сбербанк"
        print(f"🏢 Компания: {company}")
        print(f"👥 Тестовых контактов: {len(test_contacts)}")
        
        result = await process_contacts_with_emails(client, company, test_contacts)
        
        print(f"📊 Результат обработки:")
        print(result)
        
        return "Контакты не найдены" not in result
        
    except Exception as e:
        print(f"❌ Ошибка теста: {e}")
        return False

async def main():
    """Главная функция тестирования"""
    print("🧪 ТЕСТИРОВАНИЕ МОДЕРНИЗИРОВАННОГО ФУНКЦИОНАЛА КОНТАКТОВ")
    print("=" * 80)
    
    tests = [
        ("Structured Output", test_structured_contacts()),
        ("Email Domain Search", test_email_domain_search()),
        ("Partial Name Resolution", test_partial_name_resolution()),
        ("Email Generation", test_email_generation()),
        ("Email Validation", test_email_validation()),
        ("Full Contacts Processing", test_full_contacts_processing())
    ]
    
    results = []
    for test_name, test_coro in tests:
        if asyncio.iscoroutine(test_coro):
            result = await test_coro
        else:
            result = test_coro
        results.append((test_name, result))
    
    print("\n📊 РЕЗУЛЬТАТЫ ТЕСТИРОВАНИЯ:")
    print("=" * 80)
    
    passed = 0
    for test_name, result in results:
        status = "✅ ПРОЙДЕН" if result else "❌ НЕ ПРОЙДЕН"
        print(f"  {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n🎯 ИТОГО: {passed}/{len(results)} тестов пройдено")
    
    if passed == len(results):
        print("🎉 ВСЕ ТЕСТЫ ПРОЙДЕНЫ! Модернизация завершена успешно.")
    else:
        print("⚠️  Есть проблемы, требующие внимания.")

if __name__ == "__main__":
    asyncio.run(main())
