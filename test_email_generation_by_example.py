"""test_email_generation_by_example.py
Тест правильной генерации email по примеру из закупок
"""

import os
import sys
import asyncio

# Устанавливаем API ключи для тестирования
openai_api_key = os.getenv("OPENAI_API_KEY")

if not openai_api_key:
    print("❌ Установите OPENAI_API_KEY для тестирования")
    sys.exit(1)

# Предотвращаем запуск CLI
sys.argv = ['test']

async def test_generate_email_by_example():
    """Тест генерации email по примеру"""
    print("\n🧪 ТЕСТ ГЕНЕРАЦИИ EMAIL ПО ПРИМЕРУ")
    print("=" * 60)
    
    try:
        from leadgen_enhanced import generate_email_by_example
        import openai
        
        client = openai.AsyncClient(api_key=openai_api_key)
        
        # Тестовые случаи
        test_cases = [
            {
                "name": "Пример m.zinovev → v.stroykov",
                "first_name": "Валерий",
                "last_name": "Стройков", 
                "email_example": "m.zinovev",
                "domain": "rfi.mnr.gov.ru",
                "expected_pattern": "v.stroykov"
            },
            {
                "name": "Пример ivan.petrov → nataliya.ivanova",
                "first_name": "Наталия",
                "last_name": "Иванова",
                "email_example": "ivan.petrov",
                "domain": "company.ru",
                "expected_pattern": "nataliya.ivanova"
            },
            {
                "name": "Полный email <EMAIL> → k.belov",
                "first_name": "Кирилл",
                "last_name": "Белов",
                "email_example": "<EMAIL>",
                "domain": "rfi.mnr.gov.ru",
                "expected_pattern": "k.belov"
            },
            {
                "name": "Пример ipetrov → erossiyskiy",
                "first_name": "Евгений",
                "last_name": "Российский",
                "email_example": "ipetrov",
                "domain": "company.ru",
                "expected_pattern": "erossiyskiy"
            }
        ]
        
        passed_tests = 0
        
        for i, test_case in enumerate(test_cases, 1):
            print(f"\n  Тест {i}: {test_case['name']}")
            print(f"    Имя: {test_case['first_name']} {test_case['last_name']}")
            print(f"    Пример: {test_case['email_example']}")
            print(f"    Домен: {test_case['domain']}")
            print(f"    Ожидаемый паттерн: {test_case['expected_pattern']}")
            
            result = await generate_email_by_example(
                client,
                test_case['first_name'],
                test_case['last_name'],
                test_case['email_example'],
                test_case['domain']
            )
            
            print(f"    Результат: {result}")
            
            # Проверяем результат
            if result:
                local_part = result.split('@')[0] if '@' in result else result
                expected_local = test_case['expected_pattern']
                
                # Проверяем, что стиль соответствует ожидаемому
                is_correct_style = local_part == expected_local
                has_correct_domain = test_case['domain'] in result
                
                if is_correct_style and has_correct_domain:
                    print(f"    ✅ УСПЕХ: Правильный стиль и домен")
                    passed_tests += 1
                elif has_correct_domain:
                    print(f"    ⚠️  ЧАСТИЧНО: Правильный домен, но стиль может отличаться")
                    print(f"       Ожидали: {expected_local}, получили: {local_part}")
                    passed_tests += 0.5
                else:
                    print(f"    ❌ НЕУДАЧА: Неправильный результат")
            else:
                print(f"    ❌ НЕУДАЧА: Пустой результат")
        
        print(f"\n📊 Результат: {passed_tests}/{len(test_cases)} тестов пройдено")
        
        return passed_tests >= len(test_cases) * 0.7  # 70% успешности
        
    except Exception as e:
        print(f"❌ Ошибка теста: {e}")
        return False

async def test_process_single_contact_with_example():
    """Тест обработки контакта с примером"""
    print("\n🧪 ТЕСТ ОБРАБОТКИ КОНТАКТА С ПРИМЕРОМ")
    print("=" * 60)
    
    try:
        from leadgen_enhanced import process_single_contact_with_example
        import openai
        
        client = openai.AsyncClient(api_key=openai_api_key)
        
        # Тестовый контакт
        test_contact = {
            "first_name": "Валерий",
            "last_name": "Стройков",
            "position": "Директор"
        }
        
        domain = "rfi.mnr.gov.ru"
        email_example = "m.zinovev"
        
        print(f"  Контакт: {test_contact['first_name']} {test_contact['last_name']}")
        print(f"  Должность: {test_contact['position']}")
        print(f"  Домен: {domain}")
        print(f"  Пример: {email_example}")
        
        result = await process_single_contact_with_example(
            client, test_contact, domain, email_example
        )
        
        print(f"  Результат: {result}")
        
        # Проверяем результат
        has_name = test_contact['last_name'] in result and test_contact['first_name'] in result
        has_position = test_contact['position'] in result
        has_email = '@' in result and domain in result
        
        print(f"\n  ✅ Содержит имя: {has_name}")
        print(f"  ✅ Содержит должность: {has_position}")
        print(f"  ✅ Содержит email: {has_email}")
        
        return has_name and has_position and has_email
        
    except Exception as e:
        print(f"❌ Ошибка теста: {e}")
        return False

async def test_full_workflow_with_example():
    """Тест полного workflow с примером из закупок"""
    print("\n🧪 ТЕСТ ПОЛНОГО WORKFLOW С ПРИМЕРОМ")
    print("=" * 60)
    
    try:
        from leadgen_enhanced import process_contacts_with_emails_new_logic
        import openai
        
        client = openai.AsyncClient(api_key=openai_api_key)
        
        # Тестовые контакты
        test_contacts = [
            {
                "first_name": "Валерий",
                "last_name": "Стройков",
                "position": "Директор",
                "source": "web_search"
            },
            {
                "first_name": "Наталия",
                "last_name": "Иванова",
                "position": "Зам. директора",
                "source": "setka_api"
            }
        ]
        
        company_name = "Российский Фонд Информации"
        
        print(f"  Компания: {company_name}")
        print(f"  Контактов: {len(test_contacts)}")
        
        # Имитируем наличие примера из закупок
        # (в реальности это будет получено из analyze_procurement_data)
        
        # Создаем временную директорию с тестовыми данными
        import tempfile
        import pandas as pd
        import shutil
        
        tenders_dir = tempfile.mkdtemp(prefix="test_workflow_")
        
        # Создаем тестовый Excel файл
        data = []
        columns = [chr(65 + i) for i in range(30)]  # A-Z + AA-AD
        
        # Заголовки
        header_row = [''] * 30
        header_row[26] = 'Заказчик'  # AA
        data.append(header_row)
        data.append([''] * 30)  # Пустая строка
        
        # Тестовые данные
        test_row = [''] * 30
        test_row[21] = 'Контакт: email: <EMAIL>'  # V
        test_row[26] = 'Российский Фонд Информации'  # AA
        data.append(test_row)
        
        df = pd.DataFrame(data)
        excel_path = f"{tenders_dir}/test.xlsx"
        df.to_excel(excel_path, index=False, header=False)
        
        print(f"  Создан тестовый файл: {excel_path}")
        
        result = await process_contacts_with_emails_new_logic(
            client, company_name, test_contacts, None, None, tenders_dir
        )
        
        print(f"\n  Результат workflow:")
        print(result[:500] + "..." if len(result) > 500 else result)
        
        # Проверяем результат
        has_contacts = len(result) > 50  # Достаточно длинный результат
        has_emails = '@' in result
        has_correct_domain = 'rfi.mnr.gov.ru' in result
        
        print(f"\n  ✅ Есть контакты: {has_contacts}")
        print(f"  ✅ Есть email: {has_emails}")
        print(f"  ✅ Правильный домен: {has_correct_domain}")
        
        # Удаляем тестовую директорию
        shutil.rmtree(tenders_dir)
        print(f"  🗑️ Удалена тестовая директория")
        
        return has_contacts and has_emails and has_correct_domain
        
    except Exception as e:
        print(f"❌ Ошибка теста: {e}")
        return False

def test_imports():
    """Тест импортов функций генерации email"""
    print("\n🧪 ТЕСТ ИМПОРТОВ ФУНКЦИЙ ГЕНЕРАЦИИ EMAIL")
    print("=" * 60)
    
    try:
        from leadgen_enhanced import (
            generate_email_by_example,
            process_single_contact_with_example,
            process_contacts_with_emails_new_logic
        )
        
        print("✅ Все функции генерации email импортированы успешно:")
        print("  • generate_email_by_example (GPT генерация по примеру)")
        print("  • process_single_contact_with_example (обработка контакта с примером)")
        print("  • process_contacts_with_emails_new_logic (полный workflow)")
        
        return True
        
    except Exception as e:
        print(f"❌ Ошибка импорта: {e}")
        return False

async def main():
    """Главная функция тестирования"""
    print("🧪 ТЕСТИРОВАНИЕ ГЕНЕРАЦИИ EMAIL ПО ПРИМЕРУ ИЗ ЗАКУПОК")
    print("=" * 80)
    
    # Сначала тестируем импорты
    imports_ok = test_imports()
    
    if not imports_ok:
        print("❌ Проблемы с импортами, остальные тесты пропущены")
        return
    
    # Асинхронные тесты
    async_tests = [
        ("Генерация email по примеру", test_generate_email_by_example()),
        ("Обработка контакта с примером", test_process_single_contact_with_example()),
        ("Полный workflow с примером", test_full_workflow_with_example())
    ]
    
    print("\n📊 РЕЗУЛЬТАТЫ ТЕСТИРОВАНИЯ:")
    print("=" * 80)
    
    passed = 1 if imports_ok else 0  # Импорты уже протестированы
    print(f"  Импорты функций генерации email: {'✅ ПРОЙДЕН' if imports_ok else '❌ НЕ ПРОЙДЕН'}")
    
    # Асинхронные тесты
    for test_name, test_coro in async_tests:
        try:
            result = await test_coro
            status = "✅ ПРОЙДЕН" if result else "❌ НЕ ПРОЙДЕН"
            print(f"  {test_name}: {status}")
            if result:
                passed += 1
        except Exception as e:
            print(f"  {test_name}: ❌ НЕ ПРОЙДЕН ({e})")
    
    total_tests = len(async_tests) + 1
    print(f"\n🎯 ИТОГО: {passed}/{total_tests} тестов пройдено")
    
    if passed == total_tests:
        print("🎉 ВСЕ ТЕСТЫ ПРОЙДЕНЫ!")
        print("\n✅ ГЕНЕРАЦИЯ EMAIL ПО ПРИМЕРУ:")
        print("  • GPT правильно анализирует примеры из закупок")
        print("  • Генерирует email в том же стиле (m.zinovev → v.stroykov)")
        print("  • Работает с полными email и локальными частями")
        print("  • Интегрирована в полный workflow обработки контактов")
        print("  • Приоритетно использует примеры вместо стандартных паттернов")
    elif passed > 2:
        print("⚠️  Частично работает - проверьте API ключи и логику")
    else:
        print("❌ Есть серьезные проблемы с генерацией email по примеру")

if __name__ == "__main__":
    asyncio.run(main())
