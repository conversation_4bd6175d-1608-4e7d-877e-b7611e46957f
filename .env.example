# Enhanced Lead Generation System - Environment Variables
# =====================================================

# OpenAI API Configuration
OPENAI_API_KEY=your_openai_api_key_here

# Network API Configuration (Сетка)
NETWORK_API_URL=https://api.setka.example.com
NETWORK_API_KEY=your_network_api_key_here

# Hunter.io API Configuration
HUNTER_IO_API_KEY=your_hunter_io_api_key_here

# Optional: Additional API configurations
# ADDITIONAL_API_KEY=your_additional_api_key_here
# ADDITIONAL_API_URL=https://api.additional.example.com

# Logging Configuration
LOG_LEVEL=INFO
LOG_FILE=leadgen.log

# Contact Search Configuration
ENABLE_CONTACT_SEARCH=true
MAX_CONTACTS_PER_COMPANY=50
EMAIL_VALIDATION_TIMEOUT=30

# GPT Model Configuration
RESEARCH_MODEL=gpt-4o-search-preview-2025-03-11
EMAIL_MODEL=o4-mini-2025-04-16
REASONING_EFFORT=medium

# Rate Limiting
MAX_CONCURRENT_REQUESTS=5
REQUEST_DELAY_SECONDS=1