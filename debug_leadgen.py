"""debug_leadgen.py
Отладка проблемы с ошибкой "4" в leadgen_enhanced.py
"""

import os
import asyncio
from leadgen_enhanced import SECTIONS, SECTION_QUERIES, collect_company_profile_async

def debug_sections():
    """Отладка секций"""
    print("🔍 ОТЛАДКА СЕКЦИЙ")
    print("=" * 50)
    
    print("📋 SECTIONS:")
    for idx, title in SECTIONS.items():
        print(f"  {idx}: {title}")
    print()
    
    print("📝 SECTION_QUERIES:")
    for idx, queries in SECTION_QUERIES.items():
        print(f"  {idx}: {len(queries)} запросов")
    print()
    
    print("🔍 Проверка соответствия:")
    for idx in SECTIONS.keys():
        if idx in SECTION_QUERIES:
            print(f"  ✅ Секция {idx}: есть в обоих")
        else:
            print(f"  ❌ Секция {idx}: есть в SECTIONS, но НЕТ в SECTION_QUERIES")
    
    for idx in SECTION_QUERIES.keys():
        if idx in SECTIONS:
            print(f"  ✅ Запрос {idx}: есть в обоих")
        else:
            print(f"  ❌ Запрос {idx}: есть в SECTION_QUERIES, но НЕТ в SECTIONS")
    print()

async def debug_collect():
    """Отладка функции collect"""
    print("🧪 ОТЛАДКА ФУНКЦИИ COLLECT")
    print("=" * 50)
    
    # Проверяем API ключ
    api_key = os.getenv("OPENAI_API_KEY")
    if not api_key:
        print("❌ OPENAI_API_KEY не установлен")
        return
    
    print(f"✅ API ключ найден: {api_key[:20]}...")
    
    try:
        print("🚀 Тестируем collect_company_profile_async...")
        profile = await collect_company_profile_async(
            company="Тестовая компания",
            inn="1234567890",
            tenders_dir=None
        )
        print("✅ Функция выполнена успешно!")
        print(f"📊 Длина профиля: {len(profile)} символов")
        
        # Показываем первые 300 символов
        print("\n📄 Превью профиля:")
        print("-" * 40)
        print(profile[:300] + "..." if len(profile) > 300 else profile)
        print("-" * 40)
        
    except Exception as e:
        print(f"❌ Ошибка: {type(e).__name__}: {e}")
        
        # Детальная диагностика
        if "4" in str(e):
            print("🔍 Ошибка связана с индексом 4")
        if "KeyError" in str(type(e).__name__):
            print("🔍 Ошибка KeyError - проблема с доступом к ключу")
        if "SECTION_QUERIES" in str(e):
            print("🔍 Ошибка связана с SECTION_QUERIES")

def debug_range():
    """Отладка range функций"""
    print("🔢 ОТЛАДКА RANGE")
    print("=" * 50)
    
    print("range(1, 3):", list(range(1, 3)))
    print("range(1, 4):", list(range(1, 4)))
    print("SECTIONS.keys():", list(SECTIONS.keys()))
    print("SECTION_QUERIES.keys():", list(SECTION_QUERIES.keys()))
    print()
    
    print("Проверка пересечений:")
    sections_keys = set(SECTIONS.keys())
    queries_keys = set(SECTION_QUERIES.keys())
    
    print(f"  SECTIONS keys: {sections_keys}")
    print(f"  SECTION_QUERIES keys: {queries_keys}")
    print(f"  Пересечение: {sections_keys & queries_keys}")
    print(f"  Только в SECTIONS: {sections_keys - queries_keys}")
    print(f"  Только в SECTION_QUERIES: {queries_keys - sections_keys}")

async def main():
    """Главная функция отладки"""
    print("🐛 ОТЛАДКА LEADGEN ENHANCED")
    print("=" * 70)
    print()
    
    # Отладка секций
    debug_sections()
    
    # Отладка range
    debug_range()
    
    # Отладка функции collect
    await debug_collect()
    
    print("\n🏁 ОТЛАДКА ЗАВЕРШЕНА")

if __name__ == "__main__":
    asyncio.run(main())
