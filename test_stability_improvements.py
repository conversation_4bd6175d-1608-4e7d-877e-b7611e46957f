"""test_stability_improvements.py
Тест улучшений стабильности: двойные запросы GPT, дедупликация, исправление Hunter.io
"""

import os
import sys
import asyncio

# Устанавливаем API ключи для тестирования
openai_api_key = os.getenv("OPENAI_API_KEY")

if not openai_api_key:
    print("❌ Установите OPENAI_API_KEY для тестирования")
    sys.exit(1)

# Предотвращаем запуск CLI
sys.argv = ['test']

def test_deduplicate_contacts():
    """Тест дедупликации контактов"""
    print("\n🧪 ТЕСТ ДЕДУПЛИКАЦИИ КОНТАКТОВ")
    print("=" * 60)
    
    try:
        from leadgen_enhanced import deduplicate_contacts
        
        # Тестовые контакты с дубликатами
        test_contacts = [
            {"first_name": "Иван", "last_name": "Петров", "position": "Директор", "source": "web_search"},
            {"first_name": "Мария", "last_name": "Сидорова", "position": "Менеджер", "source": "web_search"},
            {"first_name": "Иван", "last_name": "Петров", "position": "Генеральный директор", "source": "setka_api"},  # Дубликат
            {"first_name": "Анна", "last_name": "Козлова", "position": "Бухгалтер", "source": "setka_api"},
            {"first_name": "мария", "last_name": "СИДОРОВА", "position": "Зам. директора", "source": "setka_api"},  # Дубликат (разный регистр)
            {"first_name": "", "last_name": "Неполный", "position": "Должность", "source": "web_search"},  # Неполные данные
            {"first_name": "Петр", "last_name": "", "position": "Должность", "source": "web_search"},  # Неполные данные
        ]
        
        print(f"  Входные контакты: {len(test_contacts)}")
        for i, contact in enumerate(test_contacts, 1):
            print(f"    {i}. {contact.get('last_name', '')} {contact.get('first_name', '')} - {contact.get('position', '')} ({contact.get('source', '')})")
        
        unique_contacts = deduplicate_contacts(test_contacts)
        
        print(f"\n  Уникальные контакты: {len(unique_contacts)}")
        
        # Проверяем результат
        expected_unique = 3  # Иван Петров, Мария Сидорова, Анна Козлова
        correct_count = len(unique_contacts) == expected_unique
        
        # Проверяем, что дубликаты удалены
        names = [(c.get('first_name', '').lower(), c.get('last_name', '').lower()) for c in unique_contacts]
        no_duplicates = len(names) == len(set(names))
        
        # Проверяем, что неполные данные исключены
        all_complete = all(c.get('first_name') and c.get('last_name') for c in unique_contacts)
        
        print(f"\n✅ Правильное количество: {correct_count} (ожидали {expected_unique}, получили {len(unique_contacts)})")
        print(f"✅ Нет дубликатов: {no_duplicates}")
        print(f"✅ Все данные полные: {all_complete}")
        
        return correct_count and no_duplicates and all_complete
        
    except Exception as e:
        print(f"❌ Ошибка теста: {e}")
        return False

async def test_double_gpt_queries():
    """Тест двойных GPT запросов для стабильности"""
    print("\n🧪 ТЕСТ ДВОЙНЫХ GPT ЗАПРОСОВ")
    print("=" * 60)
    
    try:
        from leadgen_enhanced import _gather_section
        import openai
        
        client = openai.AsyncClient(api_key=openai_api_key)
        
        # Тестируем секцию контактов (idx=2)
        company = "Сбербанк"
        inn = "1234567890"
        section_idx = 2
        model = "gpt-4o-mini"
        refresh_token = None
        tenders_dir = None
        
        print(f"🏢 Компания: {company}")
        print(f"📊 Секция: {section_idx} (контакты)")
        print(f"🔄 Ожидаем двойные запросы для каждого промпта")
        
        result = await _gather_section(client, company, inn, section_idx, model, refresh_token, tenders_dir)
        
        print(f"\n📊 Результат:")
        print(f"Длина результата: {len(result)} символов")
        print(f"Первые 300 символов:")
        print(result[:300])
        
        # Проверяем результат
        has_content = len(result) > 50  # Достаточно длинный результат
        has_contacts = "•" in result or "Контакты" in result
        
        print(f"\n✅ Есть содержимое: {has_content}")
        print(f"✅ Есть контакты: {has_contacts}")
        
        return has_content and has_contacts
        
    except Exception as e:
        print(f"❌ Ошибка теста: {e}")
        return False

async def test_hunter_io_function():
    """Тест исправленной функции Hunter.io"""
    print("\n🧪 ТЕСТ ИСПРАВЛЕННОЙ ФУНКЦИИ HUNTER.IO")
    print("=" * 60)
    
    try:
        from leadgen_enhanced import search_emails_hunter_io
        
        # Тестируем без API ключа (должно корректно обработать)
        domain = "example.com"
        hunter_api_key = None
        
        print(f"🌐 Домен: {domain}")
        print(f"🔑 API ключ: {hunter_api_key}")
        print(f"📝 Ожидаем корректную обработку отсутствия API ключа")
        
        result = await search_emails_hunter_io(domain, hunter_api_key)
        
        print(f"\n📊 Результат: {result}")
        
        # Проверяем результат
        is_list = isinstance(result, list)
        is_empty = len(result) == 0  # Без API ключа должен вернуть пустой список
        
        print(f"\n✅ Возвращает список: {is_list}")
        print(f"✅ Пустой список (без API ключа): {is_empty}")
        
        return is_list and is_empty
        
    except Exception as e:
        print(f"❌ Ошибка теста: {e}")
        return False

async def test_find_email_pattern_with_hunter():
    """Тест функции поиска паттерна с исправленным Hunter.io"""
    print("\n🧪 ТЕСТ ПОИСКА ПАТТЕРНА С ИСПРАВЛЕННЫМ HUNTER.IO")
    print("=" * 60)
    
    try:
        from leadgen_enhanced import find_email_pattern_for_contacts
        import openai
        
        client = openai.AsyncClient(api_key=openai_api_key)
        
        # Тестовые контакты
        contacts = [
            {"first_name": "Иван", "last_name": "Петров", "position": "Менеджер"},
            {"first_name": "Мария", "last_name": "Сидорова", "position": "Директор"}
        ]
        
        # Неизвестная компания (чтобы дойти до Hunter.io)
        company_name = "Неизвестная Тестовая Компания ООО"
        hunter_api_key = None  # Без API ключа
        tenders_dir = None
        
        print(f"🏢 Компания: {company_name}")
        print(f"👥 Контактов: {len(contacts)}")
        print(f"🔑 Hunter.io API: {hunter_api_key}")
        print(f"📁 Директория закупок: {tenders_dir}")
        print(f"📝 Ожидаем корректную обработку без ошибок")
        
        domain, pattern, example = await find_email_pattern_for_contacts(
            client, company_name, contacts, hunter_api_key, tenders_dir
        )
        
        print(f"\n📊 Результат:")
        print(f"  Домен: {domain}")
        print(f"  Паттерн: {pattern}")
        print(f"  Пример: {example}")
        
        # Проверяем, что функция отработала без ошибок
        no_error = True  # Если дошли до этой точки, значит ошибок не было
        expected_empty = not domain and not pattern and not example  # Ожидаем пустой результат
        
        print(f"\n✅ Функция отработала без ошибок: {no_error}")
        print(f"✅ Пустой результат (ожидаемо): {expected_empty}")
        
        return no_error and expected_empty
        
    except Exception as e:
        print(f"❌ Ошибка теста: {e}")
        return False

async def test_full_workflow_stability():
    """Тест полного workflow с улучшениями стабильности"""
    print("\n🧪 ТЕСТ ПОЛНОГО WORKFLOW С УЛУЧШЕНИЯМИ")
    print("=" * 60)

    try:
        from leadgen_enhanced import process_contacts_with_emails_new_logic
        import openai

        client = openai.AsyncClient(api_key=openai_api_key)

        # Тестовые контакты (имитируем результат веб-поиска)
        test_contacts = [
            {"first_name": "Иван", "last_name": "Петров", "position": "Директор", "source": "web_search"},
            {"first_name": "Мария", "last_name": "Сидорова", "position": "Менеджер", "source": "web_search"}
        ]

        company_name = "Сбербанк"
        hunter_api_key = None
        refresh_token = None
        tenders_dir = None

        print(f"🏢 Компания: {company_name}")
        print(f"👥 Контактов: {len(test_contacts)}")
        print(f"📁 Директория закупок: {tenders_dir}")
        print(f"🔑 Hunter.io API: {hunter_api_key}")
        print(f"🔑 Setka refresh token: {refresh_token}")
        print(f"📝 Тестируем workflow обработки контактов с улучшениями")

        result = await process_contacts_with_emails_new_logic(
            client, company_name, test_contacts, hunter_api_key, refresh_token, tenders_dir
        )

        print(f"\n📊 Результат workflow:")
        print(f"Длина результата: {len(result)} символов")
        print(f"Первые 500 символов:")
        print(result[:500])

        # Проверяем результат
        has_content = len(result) > 100
        has_structure = "Контакты" in result or "•" in result
        no_errors = "search_emails_hunter_io() takes from 1 to 2 positional arguments" not in result

        print(f"\n✅ Есть содержимое: {has_content}")
        print(f"✅ Правильная структура: {has_structure}")
        print(f"✅ Нет ошибок Hunter.io: {no_errors}")

        return has_content and has_structure and no_errors

    except Exception as e:
        print(f"❌ Ошибка теста: {e}")
        return False

def test_imports():
    """Тест импортов улучшенных функций"""
    print("\n🧪 ТЕСТ ИМПОРТОВ УЛУЧШЕННЫХ ФУНКЦИЙ")
    print("=" * 60)
    
    try:
        from leadgen_enhanced import (
            deduplicate_contacts,
            search_emails_hunter_io,
            find_email_pattern_for_contacts,
            process_contacts_with_emails_new_logic
        )

        print("✅ Все улучшенные функции импортированы успешно:")
        print("  • deduplicate_contacts (новая функция дедупликации)")
        print("  • search_emails_hunter_io (исправленная функция Hunter.io)")
        print("  • find_email_pattern_for_contacts (обновленная)")
        print("  • process_contacts_with_emails_new_logic (с двойными GPT запросами)")
        
        return True
        
    except Exception as e:
        print(f"❌ Ошибка импорта: {e}")
        return False

async def main():
    """Главная функция тестирования"""
    print("🧪 ТЕСТИРОВАНИЕ УЛУЧШЕНИЙ СТАБИЛЬНОСТИ")
    print("=" * 80)
    
    # Сначала тестируем импорты
    imports_ok = test_imports()
    
    if not imports_ok:
        print("❌ Проблемы с импортами, остальные тесты пропущены")
        return
    
    # Синхронные тесты
    sync_tests = [
        ("Дедупликация контактов", test_deduplicate_contacts())
    ]
    
    # Асинхронные тесты
    async_tests = [
        ("Двойные GPT запросы", test_double_gpt_queries()),
        ("Исправленная функция Hunter.io", test_hunter_io_function()),
        ("Поиск паттерна с Hunter.io", test_find_email_pattern_with_hunter()),
        ("Полный workflow с улучшениями", test_full_workflow_stability())
    ]
    
    print("\n📊 РЕЗУЛЬТАТЫ ТЕСТИРОВАНИЯ:")
    print("=" * 80)
    
    passed = 1 if imports_ok else 0  # Импорты уже протестированы
    print(f"  Импорты улучшенных функций: {'✅ ПРОЙДЕН' if imports_ok else '❌ НЕ ПРОЙДЕН'}")
    
    # Синхронные тесты
    for test_name, result in sync_tests:
        status = "✅ ПРОЙДЕН" if result else "❌ НЕ ПРОЙДЕН"
        print(f"  {test_name}: {status}")
        if result:
            passed += 1
    
    # Асинхронные тесты
    for test_name, test_coro in async_tests:
        try:
            result = await test_coro
            status = "✅ ПРОЙДЕН" if result else "❌ НЕ ПРОЙДЕН"
            print(f"  {test_name}: {status}")
            if result:
                passed += 1
        except Exception as e:
            print(f"  {test_name}: ❌ НЕ ПРОЙДЕН ({e})")
    
    total_tests = len(sync_tests) + len(async_tests) + 1
    print(f"\n🎯 ИТОГО: {passed}/{total_tests} тестов пройдено")
    
    if passed == total_tests:
        print("🎉 ВСЕ ТЕСТЫ ПРОЙДЕНЫ!")
        print("\n✅ УЛУЧШЕНИЯ СТАБИЛЬНОСТИ:")
        print("  • Двойные GPT запросы для повышения стабильности поиска ЛПР")
        print("  • Умная дедупликация контактов с подробным логированием")
        print("  • Исправлена ошибка Hunter.io API (правильное количество параметров)")
        print("  • Полная интеграция всех улучшений в workflow")
        print("  • Повышенная надежность и стабильность результатов")
    elif passed > 3:
        print("⚠️  Частично работает - проверьте API ключи и настройки")
    else:
        print("❌ Есть серьезные проблемы с улучшениями стабильности")

if __name__ == "__main__":
    asyncio.run(main())
