"""test_tenders_integration.py
Тест интеграции анализа email с существующим сканированием закупок
"""

import os
import sys
import asyncio
import pandas as pd
import tempfile
import shutil

# Устанавливаем API ключи для тестирования
openai_api_key = os.getenv("OPENAI_API_KEY")

if not openai_api_key:
    print("❌ Установите OPENAI_API_KEY для тестирования")
    sys.exit(1)

# Предотвращаем запуск CLI
sys.argv = ['test']

def create_test_tenders_with_correct_structure():
    """Создаем тестовую директорию с правильной структурой Excel файлов"""
    print("📁 Создание тестовой директории с правильной структурой закупок...")
    
    # Создаем временную директорию
    tenders_dir = tempfile.mkdtemp(prefix="test_tenders_correct_")
    
    # Создаем тестовый Excel файл с правильной структурой
    # Столбцы: A, B (название), C (цена), ..., K (дата), ..., V (контакт), ..., AA (компания)
    data = []
    
    # Создаем 30 столбцов (A-AD)
    columns = [chr(65 + i) for i in range(26)]  # A-Z
    columns.extend(['AA', 'AB', 'AC', 'AD'])  # AA-AD
    
    # Заголовки (строка 0)
    header_row = [''] * 30
    header_row[1] = 'Название закупки'  # B
    header_row[2] = 'НМЦ'  # C
    header_row[10] = 'Дата'  # K
    header_row[21] = 'Контактное лицо'  # V (индекс 21)
    header_row[26] = 'Заказчик'  # AA (индекс 26)
    data.append(header_row)
    
    # Пустая строка (строка 1)
    data.append([''] * 30)
    
    # Тестовые данные (строки 2+)
    test_rows = [
        {
            'B': 'Закупка IT услуг и консультаций',
            'C': '1500000',
            'K': '2024-12-15',
            'V': 'Ответственный: Стройков Валерий Александрович, тел: +7(495)987-65-43, email: <EMAIL>',
            'AA': 'Российский Фонд Информации по Природным Ресурсам'
        },
        {
            'B': 'Поставка оборудования',
            'C': '2300000',
            'K': '2024-11-20',
            'V': 'Контакт: Иванова Наталия Петровна, email: <EMAIL>, доп: <EMAIL>',
            'AA': 'Российский Фонд Информации по Природным Ресурсам'
        },
        {
            'B': 'Разработка ПО',
            'C': '800000',
            'K': '2024-10-10',
            'V': 'Менеджер: Российский Евгений, email: <EMAIL>, тел: +7(495)123-45-67',
            'AA': 'Российский Фонд Информации по Природным Ресурсам'
        },
        {
            'B': 'Другая закупка',
            'C': '500000',
            'K': '2024-09-01',
            'V': 'Контакт: <EMAIL>',
            'AA': 'Другая Компания ООО'
        },
        {
            'B': 'Еще одна закупка',
            'C': '300000',
            'K': '2024-08-15',
            'V': 'Связь: <EMAIL>',
            'AA': 'Третья Компания'
        }
    ]
    
    for row_data in test_rows:
        row = [''] * 30
        row[1] = row_data['B']    # Столбец B (индекс 1)
        row[2] = row_data['C']    # Столбец C (индекс 2)
        row[10] = row_data['K']   # Столбец K (индекс 10)
        row[21] = row_data['V']   # Столбец V (индекс 21)
        row[26] = row_data['AA']  # Столбец AA (индекс 26)
        data.append(row)
    
    # Создаем DataFrame и сохраняем
    df = pd.DataFrame(data)
    excel_path = os.path.join(tenders_dir, "test_zakupki.xlsx")
    df.to_excel(excel_path, index=False, header=False)
    
    print(f"  ✅ Создан файл: test_zakupki.xlsx ({len(data)} строк)")
    print(f"✅ Создана тестовая директория: {tenders_dir}")
    
    return tenders_dir

def test_extract_contacts_from_tenders():
    """Тест извлечения контактов из закупок"""
    print("\n🧪 ТЕСТ ИЗВЛЕЧЕНИЯ КОНТАКТОВ ИЗ ЗАКУПОК")
    print("=" * 60)
    
    try:
        from leadgen_enhanced import _extract_contacts_from_tenders
        
        # Создаем тестовую директорию
        tenders_dir = create_test_tenders_with_correct_structure()
        
        company_name = "Российский Фонд Информации по Природным Ресурсам"
        
        print(f"🏢 Компания: {company_name}")
        print(f"📁 Директория: {tenders_dir}")
        
        contacts = _extract_contacts_from_tenders(tenders_dir, company_name)
        
        print(f"\n📊 Результат извлечения контактов:")
        print(f"  Найдено записей: {len(contacts)}")
        
        for i, contact in enumerate(contacts, 1):
            print(f"  Запись {i}: {contact[:100]}...")
        
        # Проверяем результат
        has_contacts = len(contacts) > 0
        has_emails = any('@' in contact for contact in contacts)
        has_rfimnr = any('rfimnr.ru' in contact for contact in contacts)
        
        print(f"\n✅ Контакты найдены: {has_contacts}")
        print(f"✅ Содержат email: {has_emails}")
        print(f"✅ Содержат rfimnr.ru: {has_rfimnr}")
        
        # Удаляем тестовую директорию
        shutil.rmtree(tenders_dir)
        print(f"🗑️ Удалена тестовая директория")
        
        return has_contacts and has_emails and has_rfimnr
        
    except Exception as e:
        print(f"❌ Ошибка теста: {e}")
        return False

def test_scan_tenders_format():
    """Тест формата вывода _scan_tenders"""
    print("\n🧪 ТЕСТ ФОРМАТА ВЫВОДА _scan_tenders")
    print("=" * 60)
    
    try:
        from leadgen_enhanced import _scan_tenders
        
        # Создаем тестовую директорию
        tenders_dir = create_test_tenders_with_correct_structure()
        
        company_name = "Российский Фонд Информации по Природным Ресурсам"
        
        print(f"🏢 Компания: {company_name}")
        print(f"📁 Директория: {tenders_dir}")
        
        result = _scan_tenders(tenders_dir, company_name)
        
        print(f"\n📊 Результат сканирования:")
        print(f"Длина результата: {len(result)} символов")
        print(f"Первые 500 символов:")
        print(result[:500])
        
        # Проверяем формат
        has_content = len(result) > 10 and result != "Не найдено"
        has_contact_info = "Контактное лицо:" in result
        has_email = "@" in result
        has_structure = "Название закупки:" in result and "НМЦ:" in result
        
        print(f"\n✅ Есть содержимое: {has_content}")
        print(f"✅ Есть контактная информация: {has_contact_info}")
        print(f"✅ Есть email: {has_email}")
        print(f"✅ Правильная структура: {has_structure}")
        
        # Удаляем тестовую директорию
        shutil.rmtree(tenders_dir)
        print(f"🗑️ Удалена тестовая директория")
        
        return has_content and has_contact_info and has_email and has_structure
        
    except Exception as e:
        print(f"❌ Ошибка теста: {e}")
        return False

async def test_procurement_analysis_integration():
    """Тест интеграции анализа закупок с новой логикой"""
    print("\n🧪 ТЕСТ ИНТЕГРАЦИИ АНАЛИЗА ЗАКУПОК")
    print("=" * 60)
    
    try:
        from leadgen_enhanced import analyze_procurement_data
        import openai
        
        client = openai.AsyncClient(api_key=openai_api_key)
        
        # Создаем тестовую директорию
        tenders_dir = create_test_tenders_with_correct_structure()
        
        company_name = "Российский Фонд Информации по Природным Ресурсам"
        
        print(f"🏢 Компания: {company_name}")
        print(f"📁 Директория: {tenders_dir}")
        
        result = await analyze_procurement_data(client, company_name, tenders_dir)
        
        print(f"\n📊 Результат интеграции:")
        print(f"  Домен: {result.get('domain', '')}")
        print(f"  Паттерн: {result.get('pattern', '')}")
        print(f"  Пример: {result.get('example', '')}")
        
        # Проверяем результат
        has_domain = bool(result.get('domain'))
        has_example = bool(result.get('example'))
        correct_domain = 'rfimnr.ru' in result.get('domain', '')
        
        print(f"\n✅ Домен найден: {has_domain}")
        print(f"✅ Пример найден: {has_example}")
        print(f"✅ Правильный домен: {correct_domain}")
        
        # Удаляем тестовую директорию
        shutil.rmtree(tenders_dir)
        print(f"🗑️ Удалена тестовая директория")
        
        return has_domain and has_example and correct_domain
        
    except Exception as e:
        print(f"❌ Ошибка теста: {e}")
        return False

async def test_full_workflow_integration():
    """Тест полной интеграции в workflow"""
    print("\n🧪 ТЕСТ ПОЛНОЙ ИНТЕГРАЦИИ В WORKFLOW")
    print("=" * 60)
    
    try:
        from leadgen_enhanced import find_email_pattern_for_contacts
        import openai
        
        client = openai.AsyncClient(api_key=openai_api_key)
        
        # Создаем тестовую директорию
        tenders_dir = create_test_tenders_with_correct_structure()
        
        # Тестовые контакты
        test_contacts = [
            {
                "first_name": "Алексей",
                "last_name": "Петров",
                "position": "Менеджер",
                "source": "web_search"
            }
        ]
        
        company_name = "Российский Фонд Информации по Природным Ресурсам"
        
        print(f"🏢 Компания: {company_name}")
        print(f"👥 Контактов: {len(test_contacts)}")
        print(f"📁 Директория: {tenders_dir}")
        
        # Вызываем полную функцию поиска паттерна
        domain, pattern, example = await find_email_pattern_for_contacts(
            client, company_name, test_contacts, None, tenders_dir
        )
        
        print(f"\n📊 Результат полной интеграции:")
        print(f"  Домен: {domain}")
        print(f"  Паттерн: {pattern}")
        print(f"  Пример: {example}")
        
        # Проверяем результат
        has_domain = bool(domain)
        has_example = bool(example)
        correct_domain = 'rfimnr.ru' in domain
        
        print(f"\n✅ Домен найден: {has_domain}")
        print(f"✅ Пример найден: {has_example}")
        print(f"✅ Правильный домен: {correct_domain}")
        
        # Удаляем тестовую директорию
        shutil.rmtree(tenders_dir)
        print(f"🗑️ Удалена тестовая директория")
        
        return has_domain and has_example and correct_domain
        
    except Exception as e:
        print(f"❌ Ошибка теста: {e}")
        return False

def test_imports():
    """Тест импортов интегрированных функций"""
    print("\n🧪 ТЕСТ ИМПОРТОВ ИНТЕГРИРОВАННЫХ ФУНКЦИЙ")
    print("=" * 60)
    
    try:
        from leadgen_enhanced import (
            _scan_tenders,
            _extract_contacts_from_tenders,
            analyze_procurement_data,
            find_email_pattern_for_contacts
        )
        
        print("✅ Все интегрированные функции импортированы успешно:")
        print("  • _scan_tenders (существующая функция)")
        print("  • _extract_contacts_from_tenders (новая функция)")
        print("  • analyze_procurement_data (обновленная)")
        print("  • find_email_pattern_for_contacts (интегрированная)")
        
        return True
        
    except Exception as e:
        print(f"❌ Ошибка импорта: {e}")
        return False

async def main():
    """Главная функция тестирования"""
    print("🧪 ТЕСТИРОВАНИЕ ИНТЕГРАЦИИ С СУЩЕСТВУЮЩИМ СКАНИРОВАНИЕМ ЗАКУПОК")
    print("=" * 80)
    
    # Сначала тестируем импорты
    imports_ok = test_imports()
    
    if not imports_ok:
        print("❌ Проблемы с импортами, остальные тесты пропущены")
        return
    
    # Синхронные тесты
    sync_tests = [
        ("Извлечение контактов из закупок", test_extract_contacts_from_tenders()),
        ("Формат вывода _scan_tenders", test_scan_tenders_format())
    ]
    
    # Асинхронные тесты
    async_tests = [
        ("Интеграция анализа закупок", test_procurement_analysis_integration()),
        ("Полная интеграция в workflow", test_full_workflow_integration())
    ]
    
    print("\n📊 РЕЗУЛЬТАТЫ ТЕСТИРОВАНИЯ:")
    print("=" * 80)
    
    passed = 1 if imports_ok else 0  # Импорты уже протестированы
    print(f"  Импорты интегрированных функций: {'✅ ПРОЙДЕН' if imports_ok else '❌ НЕ ПРОЙДЕН'}")
    
    # Синхронные тесты
    for test_name, result in sync_tests:
        status = "✅ ПРОЙДЕН" if result else "❌ НЕ ПРОЙДЕН"
        print(f"  {test_name}: {status}")
        if result:
            passed += 1
    
    # Асинхронные тесты
    for test_name, test_coro in async_tests:
        try:
            result = await test_coro
            status = "✅ ПРОЙДЕН" if result else "❌ НЕ ПРОЙДЕН"
            print(f"  {test_name}: {status}")
            if result:
                passed += 1
        except Exception as e:
            print(f"  {test_name}: ❌ НЕ ПРОЙДЕН ({e})")
    
    total_tests = len(sync_tests) + len(async_tests) + 1
    print(f"\n🎯 ИТОГО: {passed}/{total_tests} тестов пройдено")
    
    if passed == total_tests:
        print("🎉 ВСЕ ТЕСТЫ ПРОЙДЕНЫ!")
        print("\n✅ ИНТЕГРАЦИЯ С СУЩЕСТВУЮЩИМ СКАНИРОВАНИЕМ:")
        print("  • Использует данные из существующей функции _scan_tenders")
        print("  • Извлекает контакты из столбца V (индекс 21)")
        print("  • Анализирует email из контактной информации")
        print("  • Полная интеграция в workflow поиска email")
        print("  • Нет дублирования сканирования файлов")
        print("  • Использует уже собранные данные для анализа")
    elif passed > 2:
        print("⚠️  Частично работает - проверьте API ключи")
    else:
        print("❌ Есть серьезные проблемы с интеграцией")

if __name__ == "__main__":
    asyncio.run(main())
