#!/usr/bin/env python3
"""
Тест для проверки нового формата поиска по закупкам
"""

import pandas as pd
import tempfile
import os
from leadgen_enhanced import _scan_tenders

def test_new_tenders_format():
    """Тест нового формата поиска по закупкам"""
    
    print("🧪 Тестирование нового формата поиска по закупкам...")
    
    # Создаем тестовые данные закупок с правильной структурой
    test_data = []

    # Создаем 30 столбцов (A-AD), столбец AA (26) содержит названия компаний
    for i in range(5):  # 5 строк данных (0-заголовки, 1-пустая, 2,3,4-данные)
        row = [''] * 30  # 30 пустых столбцов
        if i == 0:
            # Заголовки
            row[26] = "Заказчик"  # Столбец AA
            row[1] = "Название закупки"  # Столбец B
            row[2] = "НМЦ"  # Столбец C
            row[10] = "Дата"  # Столбец K
            row[21] = "Контактное лицо"  # Столбец V
        elif i == 1:
            # Пустая строка (часто бывает в Excel файлах)
            pass
        elif i == 2:
            # Первая закупка
            row[26] = 'ООО "Тестовая Компания"'  # Полное название с кавычками
            row[1] = "Поставка компьютерного оборудования"  # Столбец B
            row[2] = "1500000"  # Столбец C
            row[10] = "2024-01-15"  # Столбец K
            row[21] = "Иванов И.И., тел. +7(495)123-45-67"  # Столбец V
        elif i == 3:
            # Вторая закупка для той же компании
            row[26] = 'ООО "Тестовая Компания"'  # Полное название с кавычками
            row[1] = "Разработка программного обеспечения"  # Столбец B
            row[2] = "2300000"  # Столбец C
            row[10] = "2024-02-20"  # Столбец K
            row[21] = "Петров П.П., email: <EMAIL>"  # Столбец V
        elif i == 4:
            # Закупка другой компании
            row[26] = "АО Другая Компания"
            row[1] = "Закупка канцелярских товаров"
            row[2] = "50000"
            row[10] = "2024-01-10"
            row[21] = "Сидоров С.С."
        
        test_data.append(row)
    
    # Создаем временный Excel файл
    with tempfile.NamedTemporaryFile(suffix='.xlsx', delete=False) as tmp_file:
        df = pd.DataFrame(test_data)
        df.to_excel(tmp_file.name, index=False, header=False, engine='openpyxl')
        tmp_path = tmp_file.name
    
    # Создаем временную директорию для тендеров
    with tempfile.TemporaryDirectory() as tmp_dir:
        # Перемещаем файл в директорию
        final_path = os.path.join(tmp_dir, "tenders.xlsx")
        os.rename(tmp_path, final_path)
        
        try:
            print("\n📋 Тест 1: Поиск с полным названием компании (с кавычками)")
            result1 = _scan_tenders(tmp_dir, 'ООО "Тестовая Компания"')
            print(f"Результат:\n{result1}")
            
            # Проверяем, что найдены обе закупки
            if "Не найдено" in result1:
                print("❌ Ошибка: Не найдены закупки для компании с полным названием")
                return False
            
            # Проверяем формат вывода
            expected_elements = [
                "Название закупки:",
                "Дата:",
                "НМЦ:",
                "Контактное лицо:",
                "Поставка компьютерного оборудования",
                "Разработка программного обеспечения",
                "1500000 руб",
                "2300000 руб"
            ]
            
            all_found = True
            for element in expected_elements:
                if element not in result1:
                    print(f"❌ Не найден элемент: '{element}'")
                    all_found = False
            
            if all_found:
                print("✅ Все элементы найдены в правильном формате")
            else:
                return False
            
            print("\n📋 Тест 2: Поиск с частичным названием (без кавычек)")
            result2 = _scan_tenders(tmp_dir, "Тестовая Компания")
            print(f"Результат:\n{result2}")
            
            if "Не найдено" in result2:
                print("✅ Правильно: частичное название не найдено (поиск по полному названию)")
            else:
                print("❌ Ошибка: найдено по частичному названию (должен искать точное совпадение)")
                return False
            
            print("\n📋 Тест 3: Поиск несуществующей компании")
            result3 = _scan_tenders(tmp_dir, "Несуществующая Компания")
            print(f"Результат: {result3}")
            
            if "Не найдено" in result3:
                print("✅ Правильно: несуществующая компания не найдена")
            else:
                print("❌ Ошибка: найдена несуществующая компания")
                return False
            
            print("\n📋 Тест 4: Поиск другой компании")
            result4 = _scan_tenders(tmp_dir, "АО Другая Компания")
            print(f"Результат:\n{result4}")
            
            if "Не найдено" in result4:
                print("❌ Ошибка: Не найдена другая компания")
                return False
            
            if "Закупка канцелярских товаров" in result4 and "50000 руб" in result4:
                print("✅ Найдена другая компания с правильными данными")
            else:
                print("❌ Ошибка: неправильные данные для другой компании")
                return False
            
            return True
            
        except Exception as e:
            print(f"💥 Ошибка при выполнении теста: {e}")
            import traceback
            traceback.print_exc()
            return False

if __name__ == "__main__":
    print("🚀 Запуск теста нового формата поиска по закупкам...")
    
    try:
        if test_new_tenders_format():
            print("\n🎉 Тест нового формата пройден успешно!")
            print("✅ Поиск работает с полными названиями компаний")
            print("✅ Используется столбец AA для поиска")
            print("✅ Новый формат вывода работает правильно")
        else:
            print("\n❌ Тест нового формата не пройден!")
            
    except Exception as e:
        print(f"\n💥 Ошибка при выполнении теста: {e}")
        import traceback
        traceback.print_exc()
