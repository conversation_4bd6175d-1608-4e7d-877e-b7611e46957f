# Устранение неполадок Enhanced Lead Generation System

## Проблема: "Cannot run the event loop while another loop is running"

### Описание
Эта ошибка возникает при конфликте asyncio event loop'ов в Python.

### Решение
✅ **ИСПРАВЛЕНО** в версии leadgen_enhanced.py

Проблема была в функции `collect_company_profile`, которая создавала новый event loop внутри уже работающего async контекста.

### Изменения:
1. Создана async версия `collect_company_profile_async()`
2. Обновлена sync версия с правильной обработкой event loop'ов
3. Основная функция `process_companies_batch` теперь использует async версию

### Проверка исправления:
```bash
python3 -m py_compile leadgen_enhanced.py
```

## Другие возможные проблемы:

### 1. Команда `pip` не найдена
**Решение:** Используйте `pip3` вместо `pip`
```bash
pip3 install -r requirements.txt
```

### 2. Отсутствует OPENAI_API_KEY
**Решение:** Установите переменную окружения
```bash
export OPENAI_API_KEY="your-api-key-here"
```

### 3. Файлы не найдены
**Решение:** Проверьте пути к файлам, используйте полные пути
```bash
python3 leadgen_enhanced.py \
  --excel '/полный/путь/к/файлу.xlsx' \
  --template '/полный/путь/к/template.pdf' \
  --nova-ai '/полный/путь/к/nova_ai.pdf' \
  --output '/полный/путь/к/выходной/папке'
```

### 4. Проблемы с кодировкой PDF
**Решение:** Убедитесь, что PDF файлы содержат текст (не сканированные изображения)

### 5. Ошибки API OpenAI
**Возможные причины:**
- Неверный API ключ
- Превышен лимит запросов
- Проблемы с интернет соединением

**Решение:** Проверьте API ключ и лимиты в панели OpenAI

## Логи и отладка

Система выводит подробные логи процесса:
- ✅ Успешные операции
- ⚠ Предупреждения
- ✗ Ошибки

Для дополнительной отладки можно запустить тест:
```bash
python3 test_sample.py
## Модели OpenAI

### Используемые модели:
- **gpt-4o-search-preview-2025-03-11** - для исследования компаний
- **o4-mini-2025-04-16** - для генерации персонализированных писем

### Возможные проблемы с моделями:

#### 1. Модель o4-mini не поддерживает reasoning_effort
**Симптом:** Ошибка "unexpected keyword argument 'reasoning_effort'"
**Решение:** Система автоматически переключится на стандартные параметры

#### 2. Модель недоступна
**Симптом:** Ошибка "model not found" или "model not available"
**Решение:** Проверьте доступ к модели в вашем OpenAI аккаунте или используйте альтернативную:
```bash
python3 leadgen_enhanced.py \
  --email-model "gpt-4o" \
  --research-model "gpt-4o" \
  # ... другие параметры
```

#### 3. Превышен лимит токенов
**Решение:** Используйте более низкий reasoning_effort:
```bash
--reasoning-effort "low"
```

## Проблема с моделью o3: Organization verification required

### Описание
Ошибка: `Your organization must be verified to use the model 'o3-2025-04-16'`

### Причина
Модель o3 требует верификации организации в OpenAI для доступа.

### Решение
✅ **ИСПРАВЛЕНО**: Заменена модель o3 на o4-mini-2025-04-16, которая доступна без дополнительной верификации.

Если вы хотите использовать o3:
1. Перейдите на https://platform.openai.com/settings/organization/general
2. Нажмите "Verify Organization"
3. Подождите до 15 минут для активации доступа
4. Используйте параметр: `--email-model "o3-2025-04-16"`

## Проблемы с русским языком в документах

### Описание
PDF документы отображали русские символы как черные квадраты из-за отсутствия поддержки кириллицы в reportlab.

### Решение
✅ **ИСПРАВЛЕНО**: Заменен PDF формат на Word (.docx) с полной поддержкой русского языка.

### Изменения:
1. **Основной формат**: Word документы (.docx) с нативной поддержкой кириллицы
2. **Fallback**: PDF с улучшенной поддержкой русского языка (если Word недоступен)
3. **Структура документа**: Профиль компании + персонализированное письмо

### Установка зависимостей:
```bash
pip3 install python-docx
```
