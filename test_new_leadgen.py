"""test_new_leadgen.py
-----------------------------------------------------------------------
Тестирование обновленной функциональности leadgen с двумя поисковыми запросами
-----------------------------------------------------------------------
"""

import asyncio
import os
from leadgen_enhanced import collect_company_profile_async, SECTIONS

async def test_new_structure():
    """Тест новой структуры с двумя поисковыми запросами"""
    
    print("🔍 ТЕСТИРОВАНИЕ НОВОЙ СТРУКТУРЫ LEADGEN")
    print("=" * 60)
    
    # Проверяем новые секции
    print("📋 Новые секции:")
    for idx, title in SECTIONS.items():
        print(f"  {idx}. {title}")
    print()
    
    # Тестовая компания
    test_company = "Сбербанк"
    
    print(f"🏢 Тестируем на компании: {test_company}")
    print()
    
    # Проверяем наличие API ключа
    api_key = os.getenv("OPENAI_API_KEY")
    if not api_key:
        print("❌ OPENAI_API_KEY не установлен")
        print("Установите переменную окружения: export OPENAI_API_KEY='your_key'")
        return
    
    print(f"✅ API ключ найден: {api_key[:20]}...")
    print()
    
    try:
        print("🚀 Запуск сбора профиля компании...")
        profile = await collect_company_profile_async(
            company=test_company,
            inn="",
            tenders_dir=None
        )
        
        print("✅ Профиль успешно собран!")
        print(f"📊 Длина профиля: {len(profile)} символов")
        print()
        
        # Показываем первые 500 символов
        print("📄 Превью профиля:")
        print("-" * 40)
        print(profile[:500] + "..." if len(profile) > 500 else profile)
        print("-" * 40)
        print()
        
        # Анализируем структуру
        sections_found = []
        for line in profile.split('\n'):
            if line.startswith('### '):
                sections_found.append(line.replace('### ', '').strip())
        
        print("📋 Найденные секции:")
        for section in sections_found:
            print(f"  ✓ {section}")
        print()
        
        print("🎯 РЕЗУЛЬТАТ ТЕСТИРОВАНИЯ:")
        print(f"  • Секций найдено: {len(sections_found)}")
        print(f"  • Ожидалось секций: {len(SECTIONS)}")
        print(f"  • Размер профиля: {len(profile)} символов")
        
        if len(sections_found) >= 2:
            print("  ✅ Тест ПРОЙДЕН - основные секции найдены")
        else:
            print("  ⚠️  Тест частично пройден - не все секции найдены")
            
    except Exception as e:
        print(f"❌ Ошибка при тестировании: {e}")
        print("Возможные причины:")
        print("  - Проблемы с API OpenAI")
        print("  - Неверный API ключ")
        print("  - Проблемы с интернет-соединением")

def test_prompts():
    """Тест новых промптов"""
    print("\n🔧 ТЕСТИРОВАНИЕ НОВЫХ ПРОМПТОВ")
    print("=" * 60)
    
    from leadgen_enhanced import ML_AI_SYSTEM_PROMPT, CONTACTS_SYSTEM_PROMPT, SECTION_QUERIES
    
    print("📝 ML/AI System Prompt:")
    print(f"  Длина: {len(ML_AI_SYSTEM_PROMPT)} символов")
    print(f"  Содержит 'ML/AI': {'✅' if 'ML/AI' in ML_AI_SYSTEM_PROMPT else '❌'}")
    print(f"  Содержит 'hiring': {'✅' if 'hiring' in ML_AI_SYSTEM_PROMPT else '❌'}")
    print()
    
    print("📝 Contacts System Prompt:")
    print(f"  Длина: {len(CONTACTS_SYSTEM_PROMPT)} символов")
    print(f"  Содержит 'CTO': {'✅' if 'CTO' in CONTACTS_SYSTEM_PROMPT else '❌'}")
    print(f"  Содержит 'LinkedIn': {'✅' if 'LinkedIn' in CONTACTS_SYSTEM_PROMPT else '❌'}")
    print()
    
    print("📝 Section Queries:")
    for idx, queries in SECTION_QUERIES.items():
        print(f"  Секция {idx}: {len(queries)} запросов")
        for i, query in enumerate(queries, 1):
            print(f"    {i}. Длина: {len(query)} символов")
    print()

def test_document_structure():
    """Тест структуры документа"""
    print("\n📄 ТЕСТИРОВАНИЕ СТРУКТУРЫ ДОКУМЕНТА")
    print("=" * 60)
    
    # Мок данные для тестирования
    mock_profile = """**Профиль компании «Тестовая компания»**

### 1. Основные ML/AI‑проекты

• Проект распознавания речи — https://example.com, 15.01.2024
• Система рекомендаций — https://example.com, 20.02.2024

### 2. Контакты LPR + телефоны/e‑mail

• Иванов Петр — CTO
• Петрова Анна — Директор ИТ

### 3. Релевантные закупки с kontur.zakupki

Название закупки: Разработка ML системы.
Дата: 01.03.2024.
НМЦ: 5000000 руб.
Контактное лицо: Сидоров И.И.
"""
    
    mock_email = """Уважаемые коллеги!

Мы изучили ваши ML/AI проекты и готовы предложить решение.

С уважением,
Команда Nova AI"""
    
    try:
        from leadgen_enhanced import create_word_document
        
        test_output = "./test_document.docx"
        
        print("📝 Создание тестового документа...")
        create_word_document(
            email_content=mock_email,
            company_profile=mock_profile,
            output_path=test_output,
            company_name="Тестовая компания"
        )
        
        if os.path.exists(test_output):
            print(f"✅ Документ создан: {test_output}")
            print(f"📊 Размер файла: {os.path.getsize(test_output)} байт")
            
            # Удаляем тестовый файл
            os.remove(test_output)
            print("🗑️  Тестовый файл удален")
        else:
            print("❌ Документ не создан")
            
    except ImportError as e:
        print(f"⚠️  Пропуск теста документа: {e}")
    except Exception as e:
        print(f"❌ Ошибка создания документа: {e}")

async def main():
    """Главная функция тестирования"""
    print("🧪 КОМПЛЕКСНОЕ ТЕСТИРОВАНИЕ ОБНОВЛЕННОГО LEADGEN")
    print("=" * 70)
    print()
    
    # Тест промптов
    test_prompts()
    
    # Тест структуры документа
    test_document_structure()
    
    # Тест новой структуры (требует API ключ)
    await test_new_structure()
    
    print("\n🏁 ТЕСТИРОВАНИЕ ЗАВЕРШЕНО")
    print("=" * 70)

if __name__ == "__main__":
    asyncio.run(main())
