#!/usr/bin/env python3
"""
Тест для проверки извлечения названий компаний из кавычек
"""

from leadgen_enhanced import _extract_company_name_from_quotes

def test_quote_extraction():
    """Тест извлечения названий из различных типов кавычек"""
    
    test_cases = [
        # (input, expected_output, description)
        ('ООО "Тестовая Компания"', 'Тестовая Компания', 'Обычные кавычки'),
        ('ООО «Тестовая Компания»', 'Тестовая Компания', 'Французские кавычки'),
        ('ООО "Тестовая Компания"', 'Тестовая Компания', 'Типографские кавычки'),
        ("ООО 'Тестовая Компания'", 'Тестовая Компания', 'Одинарные кавычки'),
        ('ООО "Внешние" и "Внутренние Кавычки"', 'Внутренние Кавычки', 'Несколько пар кавычек - выбираем самую длинную'),
        ('ООО Компания без кавычек', 'ООО Компания без кавычек', 'Без кавычек'),
        ('ООО "Компания с незакрытыми кавычками', 'ООО "Компания с незакрытыми кавычками', 'Незакрытые кавычки'),
        ('ООО ""', '', 'Пустые кавычки'),
        ('', '', 'Пустая строка'),
        ('ООО "Компания А" и АО "Компания Б с длинным названием"', 'Компания Б с длинным названием', 'Выбор самого длинного названия'),
        ('АО «Рога и Копыта»', 'Рога и Копыта', 'Французские кавычки с амперсандом'),
    ]
    
    print("🧪 Тестирование извлечения названий из кавычек...")
    
    all_passed = True
    
    for i, (input_text, expected, description) in enumerate(test_cases, 1):
        result = _extract_company_name_from_quotes(input_text)
        
        if result == expected:
            print(f"✅ Тест {i}: {description}")
            print(f"   Вход: '{input_text}'")
            print(f"   Результат: '{result}'")
        else:
            print(f"❌ Тест {i}: {description}")
            print(f"   Вход: '{input_text}'")
            print(f"   Ожидалось: '{expected}'")
            print(f"   Получено: '{result}'")
            all_passed = False
        print()
    
    return all_passed

def test_search_integration():
    """Тест интеграции с поисковыми функциями"""
    
    print("🔍 Тестирование интеграции с поиском...")
    
    # Тестируем, что функция _build_user_prompt использует чистое название
    from leadgen_enhanced import _build_user_prompt
    
    test_instruction = "Найди информацию о компании {company}"
    
    test_cases = [
        ('ООО "Тестовая Компания"', '1234567890', 'Найди информацию о компании Тестовая Компания'),
        ('АО «Рога и Копыта»', '', 'Найди информацию о компании Рога и Копыта'),
        ('Компания без кавычек', '9876543210', 'Найди информацию о компании Компания без кавычек'),
    ]
    
    all_passed = True
    
    for i, (company, inn, expected) in enumerate(test_cases, 1):
        result = _build_user_prompt(company, inn, test_instruction)
        
        if result == expected:
            print(f"✅ Интеграционный тест {i}")
            print(f"   Компания: '{company}'")
            print(f"   Результат: '{result}'")
        else:
            print(f"❌ Интеграционный тест {i}")
            print(f"   Компания: '{company}'")
            print(f"   Ожидалось: '{expected}'")
            print(f"   Получено: '{result}'")
            all_passed = False
        print()
    
    return all_passed

if __name__ == "__main__":
    print("🚀 Запуск тестов извлечения названий из кавычек...")
    
    try:
        test1_result = test_quote_extraction()
        test2_result = test_search_integration()
        
        if test1_result and test2_result:
            print("🎉 Все тесты пройдены успешно!")
        else:
            print("❌ Некоторые тесты не пройдены!")
            
    except Exception as e:
        print(f"💥 Ошибка при выполнении тестов: {e}")
        import traceback
        traceback.print_exc()
