"""test_company_search.py
-----------------------------------------------------------------------
Тесты и примеры использования функции поиска сотрудников компании
-----------------------------------------------------------------------
"""

import asyncio
import unittest
from unittest.mock import AsyncMock, patch, MagicMock
import json
from company_search import (
    SetkaAPIClient,
    extract_networks_from_search,
    extract_accounts_from_search,
    extract_employee_data,
    extract_employee_data_from_accounts,
    filter_by_position_keywords,
    search_company_employees,
    TokenInfo
)

# Тестовые данные
MOCK_SEARCH_RESPONSE = {
    "items": [
        {
            "type": "ACCOUNTS",
            "items": [
                {
                    "uuid": "ae487bf2-8bc3-4856-bfd9-de9a78c19228",
                    "id": "421417",
                    "first_name": "Иван",
                    "last_name": "Мангилёв",
                    "position": {
                        "type": "POSITION_RESPONSE",
                        "id": None,
                        "uuid": None,
                        "name": "Тестировщик"
                    },
                    "position_title": "Инженер-тестировщик"
                },
                {
                    "uuid": "ae487bf2-8bc3-4856-bfd9-de9a78c19229",
                    "id": "421418",
                    "first_name": "Анна",
                    "last_name": "Петрова",
                    "position": {
                        "type": "POSITION_RESPONSE",
                        "id": None,
                        "uuid": None,
                        "name": "Директор по ИТ"
                    },
                    "position_title": "Директор по информационным технологиям"
                }
            ]
        },
        {
            "type": "NETWORKS",
            "items": [
                {
                    "id": "e9791ecb-f7bb-470a-9b80-f36236f5cdfd",
                    "name": "Автоваз",
                    "type": "company",
                    "members_count": 9551
                }
            ]
        }
    ]
}

# Тестовые данные для поиска без Networks
MOCK_SEARCH_RESPONSE_NO_NETWORKS = {
    "items": [
        {
            "type": "ACCOUNTS",
            "items": [
                {
                    "uuid": "ae487bf2-8bc3-4856-bfd9-de9a78c19228",
                    "id": "421417",
                    "first_name": "Сергей",
                    "last_name": "Иванов",
                    "position": {
                        "type": "POSITION_RESPONSE",
                        "id": None,
                        "uuid": None,
                        "name": "CTO"
                    },
                    "position_title": "Технический директор"
                },
                {
                    "uuid": "ae487bf2-8bc3-4856-bfd9-de9a78c19229",
                    "id": "421418",
                    "first_name": "Мария",
                    "last_name": "Козлова",
                    "position": {
                        "type": "POSITION_RESPONSE",
                        "id": None,
                        "uuid": None,
                        "name": "Руководитель отдела ИТ"
                    },
                    "position_title": "Руководитель отдела информационных технологий"
                }
            ]
        },
        {
            "type": "COMMUNITIES",
            "items": []
        }
    ]
}

MOCK_MEMBERS_RESPONSE = {
    "members": [
        {
            "id": "64949",
            "uuid": "73bf5e36-09de-4227-b1a7-df4fb782d8f1",
            "first_name": "Родион",
            "last_name": "Антонов",
            "position": {
                "id": None,
                "name": "Руководитель проекта ИТ"
            }
        },
        {
            "id": "64950",
            "uuid": "73bf5e36-09de-4227-b1a7-df4fb782d8f2",
            "first_name": "Анна",
            "last_name": "Петрова",
            "position": {
                "id": None,
                "name": "Директор по информационным технологиям"
            }
        },
        {
            "id": "64951",
            "uuid": "73bf5e36-09de-4227-b1a7-df4fb782d8f3",
            "first_name": "Иван",
            "last_name": "Сидоров",
            "position": {
                "id": None,
                "name": "Менеджер по продажам"
            }
        },
        {
            "id": "64952",
            "uuid": "73bf5e36-09de-4227-b1a7-df4fb782d8f4",
            "first_name": "Мария",
            "last_name": "Козлова",
            "position": {
                "id": None,
                "name": "CTO"
            }
        }
    ]
}

class TestCompanySearch(unittest.TestCase):
    """Тесты для функций поиска сотрудников"""
    
    def test_extract_networks_from_search(self):
        """Тест извлечения Networks из результатов поиска"""
        networks = extract_networks_from_search(MOCK_SEARCH_RESPONSE)
        
        self.assertEqual(len(networks), 1)
        self.assertEqual(networks[0]["id"], "e9791ecb-f7bb-470a-9b80-f36236f5cdfd")
        self.assertEqual(networks[0]["name"], "Автоваз")
        self.assertEqual(networks[0]["members_count"], 9551)
    
    def test_extract_networks_from_search_empty(self):
        """Тест извлечения Networks из пустых результатов"""
        empty_response = {"items": []}
        networks = extract_networks_from_search(empty_response)

        self.assertEqual(len(networks), 0)

    def test_extract_accounts_from_search(self):
        """Тест извлечения Accounts из результатов поиска"""
        accounts = extract_accounts_from_search(MOCK_SEARCH_RESPONSE)

        self.assertEqual(len(accounts), 2)
        self.assertEqual(accounts[0]["first_name"], "Иван")
        self.assertEqual(accounts[0]["last_name"], "Мангилёв")
        self.assertEqual(accounts[1]["first_name"], "Анна")
        self.assertEqual(accounts[1]["last_name"], "Петрова")

    def test_extract_accounts_from_search_empty(self):
        """Тест извлечения Accounts из пустых результатов"""
        empty_response = {"items": []}
        accounts = extract_accounts_from_search(empty_response)

        self.assertEqual(len(accounts), 0)
    
    def test_extract_employee_data(self):
        """Тест извлечения данных сотрудников"""
        employees = extract_employee_data(MOCK_MEMBERS_RESPONSE)
        
        self.assertEqual(len(employees), 4)
        
        # Проверяем первого сотрудника
        self.assertEqual(employees[0], ("Антонов", "Родион", "Руководитель проекта ИТ"))
        
        # Проверяем второго сотрудника
        self.assertEqual(employees[1], ("Петрова", "Анна", "Директор по информационным технологиям"))
    
    def test_extract_employee_data_empty(self):
        """Тест извлечения данных из пустого списка участников"""
        empty_response = {"members": []}
        employees = extract_employee_data(empty_response)

        self.assertEqual(len(employees), 0)

    def test_extract_employee_data_from_accounts(self):
        """Тест извлечения данных сотрудников из accounts"""
        accounts = extract_accounts_from_search(MOCK_SEARCH_RESPONSE)
        employees = extract_employee_data_from_accounts(accounts)

        self.assertEqual(len(employees), 2)

        # Проверяем первого сотрудника
        self.assertEqual(employees[0], ("Мангилёв", "Иван", "Тестировщик"))

        # Проверяем второго сотрудника
        self.assertEqual(employees[1], ("Петрова", "Анна", "Директор по ИТ"))

    def test_extract_employee_data_from_accounts_no_networks(self):
        """Тест извлечения данных сотрудников из accounts когда нет Networks"""
        accounts = extract_accounts_from_search(MOCK_SEARCH_RESPONSE_NO_NETWORKS)
        employees = extract_employee_data_from_accounts(accounts)

        self.assertEqual(len(employees), 2)

        # Проверяем первого сотрудника
        self.assertEqual(employees[0], ("Иванов", "Сергей", "CTO"))

        # Проверяем второго сотрудника
        self.assertEqual(employees[1], ("Козлова", "Мария", "Руководитель отдела ИТ"))
    
    def test_filter_by_position_keywords(self):
        """Тест фильтрации сотрудников по ключевым словам"""
        test_employees = [
            ("Антонов", "Родион", "Руководитель проекта ИТ"),
            ("Петрова", "Анна", "Директор по информационным технологиям"),
            ("Сидоров", "Иван", "Менеджер по продажам"),
            ("Козлова", "Мария", "CTO")
        ]
        
        keywords = ["директор", "ИТ", "CTO"]
        filtered = filter_by_position_keywords(test_employees, keywords)
        
        # Должны остаться 3 сотрудника (исключая менеджера по продажам)
        self.assertEqual(len(filtered), 3)
        
        # Проверяем, что правильные сотрудники отфильтрованы
        filtered_names = [emp[0] for emp in filtered]
        self.assertIn("Антонов", filtered_names)  # ИТ
        self.assertIn("Петрова", filtered_names)  # директор
        self.assertIn("Козлова", filtered_names)  # CTO
        self.assertNotIn("Сидоров", filtered_names)  # менеджер по продажам
    
    def test_filter_by_position_keywords_case_insensitive(self):
        """Тест фильтрации с учетом регистра"""
        test_employees = [
            ("Иванов", "Петр", "it директор"),
            ("Петров", "Иван", "ДИРЕКТОР ПО ИТ"),
            ("Сидоров", "Анна", "cto компании")
        ]
        
        keywords = ["директор", "ИТ", "CTO"]
        filtered = filter_by_position_keywords(test_employees, keywords)
        
        # Все должны пройти фильтр
        self.assertEqual(len(filtered), 3)
    
    def test_setka_api_client_search_company(self):
        """Тест поиска компании через API клиент"""
        async def run_test():
            with patch('aiohttp.ClientSession.post') as mock_post, \
                 patch('aiohttp.ClientSession.get') as mock_get:

                # Мок для refresh токена
                mock_refresh_response = AsyncMock()
                mock_refresh_response.status = 200
                mock_refresh_response.json = AsyncMock(return_value={
                    "access_token": "new_access_token",
                    "refresh_token": "new_refresh_token",
                    "access_token_expired_at": "2025-07-19T12:00:00Z",
                    "refresh_token_expired_at": "2026-01-19T12:00:00Z"
                })
                mock_post.return_value.__aenter__.return_value = mock_refresh_response

                # Мок для поиска компании
                mock_search_response = AsyncMock()
                mock_search_response.status = 200
                mock_search_response.json = AsyncMock(return_value=MOCK_SEARCH_RESPONSE)
                mock_get.return_value.__aenter__.return_value = mock_search_response

                # Создаем клиент и выполняем поиск
                client = SetkaAPIClient("test_refresh_token")
                result = await client.search_company("Автоваз")

                # Проверяем результат
                self.assertEqual(result, MOCK_SEARCH_RESPONSE)

                # Проверяем, что токен был обновлен
                mock_post.assert_called_once()

                # Проверяем, что поиск был выполнен
                mock_get.assert_called_once()
                call_args = mock_get.call_args
                self.assertIn("text", call_args[1]["params"])
                self.assertEqual(call_args[1]["params"]["text"], "Автоваз")

        # Запускаем асинхронный тест
        asyncio.run(run_test())

    def test_setka_api_client_get_network_members(self):
        """Тест получения участников сети через API клиент"""
        async def run_test():
            with patch('aiohttp.ClientSession.post') as mock_post, \
                 patch('aiohttp.ClientSession.get') as mock_get:

                # Мок для refresh токена
                mock_refresh_response = AsyncMock()
                mock_refresh_response.status = 200
                mock_refresh_response.json = AsyncMock(return_value={
                    "access_token": "new_access_token",
                    "refresh_token": "new_refresh_token",
                    "access_token_expired_at": "2025-07-19T12:00:00Z",
                    "refresh_token_expired_at": "2026-01-19T12:00:00Z"
                })
                mock_post.return_value.__aenter__.return_value = mock_refresh_response

                # Мок для получения участников
                mock_members_response = AsyncMock()
                mock_members_response.status = 200
                mock_members_response.json = AsyncMock(return_value=MOCK_MEMBERS_RESPONSE)
                mock_get.return_value.__aenter__.return_value = mock_members_response

                # Создаем клиент и получаем участников
                client = SetkaAPIClient("test_refresh_token")
                result = await client.get_network_members("test_network_id")

                # Проверяем результат
                self.assertEqual(result, MOCK_MEMBERS_RESPONSE)

                # Проверяем, что токен был обновлен
                mock_post.assert_called_once()

                # Проверяем URL
                call_args = mock_get.call_args
                expected_url = "https://api.setka.ru/v2/networks/test_network_id/members/search"
                self.assertEqual(call_args[0][0], expected_url)

        # Запускаем асинхронный тест
        asyncio.run(run_test())

    def test_search_company_employees_no_networks(self):
        """Тест полного процесса поиска сотрудников без Networks (через Accounts)"""
        async def run_test():
            with patch('company_search.SetkaAPIClient.search_company') as mock_search, \
                 patch('company_search.SetkaAPIClient.get_current_refresh_token') as mock_get_token, \
                 patch('company_search.create_employees_report') as mock_create_report:

                # Настраиваем моки для случая без Networks
                mock_search.return_value = MOCK_SEARCH_RESPONSE_NO_NETWORKS
                mock_get_token.return_value = "new_refresh_token"
                mock_create_report.return_value = None

                # Выполняем поиск
                employees, new_refresh_token = await search_company_employees(
                    company_name="Малая компания",
                    refresh_token="test_refresh_token",
                    output_dir="./test_reports"
                )

                # Проверяем результат
                self.assertEqual(len(employees), 2)  # 2 сотрудника должны пройти фильтр
                self.assertEqual(new_refresh_token, "new_refresh_token")

                # Проверяем, что правильные сотрудники отфильтрованы
                employee_names = [emp[0] for emp in employees]
                self.assertIn("Иванов", employee_names)  # CTO
                self.assertIn("Козлова", employee_names)  # Руководитель отдела ИТ

                # Проверяем, что функции были вызваны
                mock_search.assert_called_once_with("Малая компания")
                mock_create_report.assert_called_once()
                mock_get_token.assert_called_once()

        # Запускаем асинхронный тест
        asyncio.run(run_test())

class TestIntegration(unittest.TestCase):
    """Интеграционные тесты"""

    def test_search_company_employees_full_flow(self):
        """Тест полного процесса поиска сотрудников"""
        async def run_test():
            with patch('company_search.SetkaAPIClient.search_company') as mock_search, \
                 patch('company_search.SetkaAPIClient.get_network_members') as mock_get_members, \
                 patch('company_search.SetkaAPIClient.get_current_refresh_token') as mock_get_token, \
                 patch('company_search.create_employees_report') as mock_create_report:

                # Настраиваем моки
                mock_search.return_value = MOCK_SEARCH_RESPONSE
                mock_get_members.return_value = MOCK_MEMBERS_RESPONSE
                mock_get_token.return_value = "new_refresh_token"
                mock_create_report.return_value = None

                # Выполняем поиск
                employees, new_refresh_token = await search_company_employees(
                    company_name="Автоваз",
                    refresh_token="test_refresh_token",
                    output_dir="./test_reports"
                )

                # Проверяем результат
                self.assertEqual(len(employees), 3)  # 3 сотрудника должны пройти фильтр
                self.assertEqual(new_refresh_token, "new_refresh_token")

                # Проверяем, что все функции были вызваны
                mock_search.assert_called_once_with("Автоваз")
                mock_get_members.assert_called_once_with("e9791ecb-f7bb-470a-9b80-f36236f5cdfd")
                mock_create_report.assert_called_once()
                mock_get_token.assert_called_once()

        # Запускаем асинхронный тест
        asyncio.run(run_test())

def run_example():
    """Пример использования функции поиска"""
    print("=" * 60)
    print("ПРИМЕР ИСПОЛЬЗОВАНИЯ ФУНКЦИИ ПОИСКА СОТРУДНИКОВ")
    print("=" * 60)

    # Замените на ваш реальный refresh токен
    REFRESH_TOKEN = "your_refresh_token_here"
    COMPANY_NAME = "Автоваз"

    print(f"Компания для поиска: {COMPANY_NAME}")
    print(f"Refresh токен: {REFRESH_TOKEN[:20]}..." if len(REFRESH_TOKEN) > 20 else REFRESH_TOKEN)
    print()

    # Пример синхронного вызова
    try:
        from company_search import search_company_employees_sync

        print("Запуск поиска...")
        employees, new_refresh_token = search_company_employees_sync(
            company_name=COMPANY_NAME,
            refresh_token=REFRESH_TOKEN,
            output_dir="./example_reports"
        )

        print(f"\n✅ Найдено {len(employees)} сотрудников:")
        for i, (last_name, first_name, position) in enumerate(employees, 1):
            print(f"  {i}. {last_name} {first_name} - {position}")

        print(f"\n📄 Отчет сохранен в папке: ./example_reports")
        print(f"🔑 Новый refresh токен: {new_refresh_token}")
        print("💡 Сохраните новый токен для следующего использования!")

    except Exception as e:
        print(f"❌ Ошибка: {e}")
        print("\nДля работы с реальным API необходимо:")
        print("1. Получить действующий refresh_token от API Setka.ru")
        print("2. Заменить 'your_refresh_token_here' на реальный токен")

if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == "--test":
        # Запуск тестов
        unittest.main(argv=[''], exit=False)
    elif len(sys.argv) > 1 and sys.argv[1] == "--example":
        # Запуск примера
        run_example()
    else:
        print("Использование:")
        print("  python test_company_search.py --test     # Запуск тестов")
        print("  python test_company_search.py --example  # Запуск примера")
