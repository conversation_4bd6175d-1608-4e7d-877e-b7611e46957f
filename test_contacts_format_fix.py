"""test_contacts_format_fix.py
Тест исправления конфликта форматов в системном и пользовательском промптах
"""

import os
import sys
import asyncio

# Устанавливаем API ключи для тестирования
openai_api_key = os.getenv("OPENAI_API_KEY")

if not openai_api_key:
    print("❌ Установите OPENAI_API_KEY для тестирования")
    sys.exit(1)

# Предотвращаем запуск CLI
sys.argv = ['test']

def test_json_format_parsing():
    """Тест парсинга JSON формата"""
    print("\n🧪 ТЕСТ ПАРСИНГА JSON ФОРМАТА")
    print("=" * 60)
    
    try:
        from leadgen_enhanced import parse_contacts_from_text
        
        # Тестовый ответ GPT в JSON формате (как требует пользовательский промпт)
        json_response = '''
        Найдены следующие контакты руководителей:
        
        ```json
        [
          ["Александр", "Иванов", "CTO"],
          ["Мария", "Петрова", "CIO"],
          ["Сергей", "Сидоров", "Технический директор"]
        ]
        ```
        
        Дополнительная информация о компании...
        '''
        
        print(f"📧 Тестовый JSON ответ:")
        print(f"  Содержит JSON массив: {'✅' if '[' in json_response and ']' in json_response else '❌'}")
        print(f"  Формат: [['Имя', 'Фамилия', 'Должность'], ...]")
        
        contacts = parse_contacts_from_text(json_response)
        
        print(f"\n📊 Результаты парсинга JSON:")
        print(f"  Найдено контактов: {len(contacts)}")
        
        expected_contacts = [
            ("Александр", "Иванов", "CTO"),
            ("Мария", "Петрова", "CIO"),
            ("Сергей", "Сидоров", "Технический директор")
        ]
        
        print(f"\n✅ Распарсенные контакты:")
        for i, contact in enumerate(contacts, 1):
            first_name = contact.get("first_name", "")
            last_name = contact.get("last_name", "")
            position = contact.get("position", "")
            source = contact.get("source", "")
            print(f"  {i}. {last_name} {first_name} - {position} (источник: {source})")
        
        # Проверяем результаты
        correct_count = len(contacts) == len(expected_contacts)
        correct_format = all(
            contact.get("first_name") and contact.get("last_name") and contact.get("position")
            for contact in contacts
        )
        correct_source = all(contact.get("source") == "web_search" for contact in contacts)
        
        print(f"\n📋 Проверки JSON формата:")
        print(f"  ✅ Правильное количество контактов: {correct_count} ({len(contacts)}/{len(expected_contacts)})")
        print(f"  ✅ Все поля заполнены: {correct_format}")
        print(f"  ✅ Правильный источник: {correct_source}")
        
        return correct_count and correct_format and correct_source
        
    except Exception as e:
        print(f"❌ Ошибка теста: {e}")
        return False

def test_fallback_format_parsing():
    """Тест fallback парсинга старого формата"""
    print("\n🧪 ТЕСТ FALLBACK ПАРСИНГА СТАРОГО ФОРМАТА")
    print("=" * 60)
    
    try:
        from leadgen_enhanced import parse_contacts_from_text
        
        # Тестовый ответ GPT в старом формате CONTACT_START/END
        old_format_response = '''
        Найдены следующие контакты:
        
        CONTACT_START
        FIRST_NAME: Александр
        LAST_NAME: Иванов
        POSITION: CTO
        CONFIDENCE: high
        CONTACT_END
        
        CONTACT_START
        FIRST_NAME: Мария
        LAST_NAME: Петрова
        POSITION: CIO
        CONFIDENCE: medium
        CONTACT_END
        
        Дополнительная информация...
        '''
        
        print(f"📧 Тестовый старый формат:")
        print(f"  Содержит CONTACT_START: {'✅' if 'CONTACT_START' in old_format_response else '❌'}")
        print(f"  Содержит CONTACT_END: {'✅' if 'CONTACT_END' in old_format_response else '❌'}")
        print(f"  Формат: CONTACT_START ... CONTACT_END")
        
        contacts = parse_contacts_from_text(old_format_response)
        
        print(f"\n📊 Результаты fallback парсинга:")
        print(f"  Найдено контактов: {len(contacts)}")
        
        print(f"\n✅ Распарсенные контакты:")
        for i, contact in enumerate(contacts, 1):
            first_name = contact.get("first_name", "")
            last_name = contact.get("last_name", "")
            position = contact.get("position", "")
            confidence = contact.get("confidence", "")
            source = contact.get("source", "")
            print(f"  {i}. {last_name} {first_name} - {position}")
            print(f"     Уверенность: {confidence}, Источник: {source}")
        
        # Проверяем результаты
        correct_count = len(contacts) == 2
        correct_format = all(
            contact.get("first_name") and contact.get("last_name") and contact.get("position")
            for contact in contacts
        )
        has_confidence = any(contact.get("confidence") for contact in contacts)
        correct_source = all(contact.get("source") == "web_search" for contact in contacts)
        
        print(f"\n📋 Проверки старого формата:")
        print(f"  ✅ Правильное количество контактов: {correct_count} ({len(contacts)}/2)")
        print(f"  ✅ Все поля заполнены: {correct_format}")
        print(f"  ✅ Есть поле confidence: {has_confidence}")
        print(f"  ✅ Правильный источник: {correct_source}")
        
        return correct_count and correct_format and correct_source
        
    except Exception as e:
        print(f"❌ Ошибка теста: {e}")
        return False

def test_prompts_consistency():
    """Тест согласованности промптов"""
    print("\n🧪 ТЕСТ СОГЛАСОВАННОСТИ ПРОМПТОВ")
    print("=" * 60)
    
    try:
        from leadgen_enhanced import CONTACTS_SYSTEM_PROMPT, SECTION_QUERIES
        
        print(f"📝 Анализ системного промпта:")
        has_json_format = "JSON" in CONTACTS_SYSTEM_PROMPT
        has_array_format = "array" in CONTACTS_SYSTEM_PROMPT
        has_old_format = "CONTACT_START" in CONTACTS_SYSTEM_PROMPT
        
        print(f"  Содержит JSON формат: {'✅' if has_json_format else '❌'}")
        print(f"  Содержит array формат: {'✅' if has_array_format else '❌'}")
        print(f"  Содержит старый формат: {'❌' if not has_old_format else '⚠️'}")
        
        print(f"\n📝 Анализ пользовательского запроса:")
        user_query = SECTION_QUERIES[2][0]
        user_has_json = "JSON" in user_query or "json" in user_query
        user_has_array = "массив" in user_query or "array" in user_query
        user_has_old = "CONTACT_START" in user_query
        
        print(f"  Содержит JSON формат: {'✅' if user_has_json else '❌'}")
        print(f"  Содержит array формат: {'✅' if user_has_array else '❌'}")
        print(f"  Содержит старый формат: {'❌' if not user_has_old else '⚠️'}")
        
        # Проверяем согласованность
        formats_match = (has_json_format and user_has_json) or (not has_json_format and not user_has_json)
        no_conflicts = not (has_old_format and user_has_json)
        
        print(f"\n📋 Проверки согласованности:")
        print(f"  ✅ Форматы совпадают: {formats_match}")
        print(f"  ✅ Нет конфликтов форматов: {no_conflicts}")
        
        if has_json_format and user_has_json:
            print(f"  🎯 Оба промпта используют JSON формат ✅")
        elif has_old_format and user_has_old:
            print(f"  🎯 Оба промпта используют старый формат ✅")
        else:
            print(f"  ⚠️ Форматы не согласованы")
        
        return formats_match and no_conflicts
        
    except Exception as e:
        print(f"❌ Ошибка теста: {e}")
        return False

async def test_real_gpt_response():
    """Тест реального ответа GPT с новым системным промптом"""
    print("\n🧪 ТЕСТ РЕАЛЬНОГО ОТВЕТА GPT")
    print("=" * 60)
    
    try:
        from leadgen_enhanced import _run_subquery, SECTION_QUERIES
        import openai
        
        client = openai.AsyncClient(api_key=openai_api_key)
        
        company = "Сбербанк"
        inn = ""
        section_idx = 2  # Контакты
        
        print(f"🏢 Компания: {company}")
        print(f"📝 Секция: {section_idx} (контакты)")
        print(f"🤖 Тестируем новый системный промпт...")
        
        # Выполняем запрос
        query = SECTION_QUERIES[section_idx][0]
        result = await _run_subquery(client, company, inn, query, section_idx)
        
        print(f"\n📊 Результат GPT:")
        print(f"  Длина ответа: {len(result)} символов")
        print(f"  Начало ответа: {result[:300]}...")
        
        # Проверяем формат ответа
        has_json = "[" in result and "]" in result
        has_old_format = "CONTACT_START" in result
        has_array_structure = "," in result and '"' in result
        
        print(f"\n📋 Анализ формата ответа:")
        print(f"  ✅ Содержит JSON структуру: {has_json}")
        print(f"  ❌ Содержит старый формат: {has_old_format}")
        print(f"  ✅ Содержит массив структуру: {has_array_structure}")
        
        # Пытаемся распарсить
        from leadgen_enhanced import parse_contacts_from_text
        contacts = parse_contacts_from_text(result)
        
        print(f"\n📊 Результаты парсинга:")
        print(f"  Найдено контактов: {len(contacts)}")
        
        if contacts:
            print(f"\n✅ Найденные контакты:")
            for i, contact in enumerate(contacts, 1):
                first_name = contact.get("first_name", "")
                last_name = contact.get("last_name", "")
                position = contact.get("position", "")
                print(f"  {i}. {last_name} {first_name} - {position}")
        
        # Проверяем успешность
        parsing_success = len(contacts) > 0
        format_correct = has_json and not has_old_format
        
        print(f"\n📋 Проверки реального ответа:")
        print(f"  ✅ Парсинг успешен: {parsing_success}")
        print(f"  ✅ Правильный формат: {format_correct}")
        
        return parsing_success
        
    except Exception as e:
        print(f"❌ Ошибка теста: {e}")
        return False

def test_imports():
    """Тест импортов обновленных функций"""
    print("\n🧪 ТЕСТ ИМПОРТОВ ОБНОВЛЕННЫХ ФУНКЦИЙ")
    print("=" * 60)
    
    try:
        from leadgen_enhanced import (
            CONTACTS_SYSTEM_PROMPT,
            SECTION_QUERIES,
            parse_contacts_from_text,
            _run_subquery
        )
        
        print("✅ Все функции импортированы успешно:")
        print("  • CONTACTS_SYSTEM_PROMPT (обновленный системный промпт)")
        print("  • SECTION_QUERIES (пользовательские запросы)")
        print("  • parse_contacts_from_text (парсер с поддержкой JSON)")
        print("  • _run_subquery (функция выполнения запросов)")
        
        # Проверяем наличие контактных запросов
        has_contacts = 2 in SECTION_QUERIES and len(SECTION_QUERIES[2]) > 0
        print(f"\n✅ Запросы для контактов: {has_contacts}")
        
        return has_contacts
        
    except Exception as e:
        print(f"❌ Ошибка импорта: {e}")
        return False

async def main():
    """Главная функция тестирования"""
    print("🧪 ТЕСТИРОВАНИЕ ИСПРАВЛЕНИЯ КОНФЛИКТА ФОРМАТОВ")
    print("=" * 80)
    
    # Синхронные тесты
    sync_tests = [
        ("Импорты обновленных функций", test_imports()),
        ("Парсинг JSON формата", test_json_format_parsing()),
        ("Fallback парсинг старого формата", test_fallback_format_parsing()),
        ("Согласованность промптов", test_prompts_consistency())
    ]
    
    # Асинхронные тесты
    async_tests = [
        ("Реальный ответ GPT", test_real_gpt_response())
    ]
    
    print("\n📊 РЕЗУЛЬТАТЫ ТЕСТИРОВАНИЯ:")
    print("=" * 80)
    
    passed = 0
    
    # Синхронные тесты
    for test_name, result in sync_tests:
        status = "✅ ПРОЙДЕН" if result else "❌ НЕ ПРОЙДЕН"
        print(f"  {test_name}: {status}")
        if result:
            passed += 1
    
    # Асинхронные тесты
    for test_name, test_coro in async_tests:
        try:
            result = await test_coro
            status = "✅ ПРОЙДЕН" if result else "❌ НЕ ПРОЙДЕН"
            print(f"  {test_name}: {status}")
            if result:
                passed += 1
        except Exception as e:
            print(f"  {test_name}: ❌ НЕ ПРОЙДЕН ({e})")
    
    total_tests = len(sync_tests) + len(async_tests)
    print(f"\n🎯 ИТОГО: {passed}/{total_tests} тестов пройдено")
    
    if passed == total_tests:
        print("🎉 ВСЕ ТЕСТЫ ПРОЙДЕНЫ!")
        print("\n✅ КОНФЛИКТ ФОРМАТОВ ИСПРАВЛЕН:")
        print("  • Системный промпт требует JSON формат")
        print("  • Пользовательский запрос требует JSON формат")
        print("  • Парсер поддерживает JSON с fallback на старый формат")
        print("  • Нет конфликтов между промптами")
        print("  • GPT возвращает правильный JSON формат")
    elif passed > 3:
        print("⚠️  Частично работает - проверьте форматы промптов")
    else:
        print("❌ Есть серьезные проблемы с согласованностью форматов")

if __name__ == "__main__":
    asyncio.run(main())
