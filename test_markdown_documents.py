"""test_markdown_documents.py
Тест создания Markdown документов вместо Word документов
"""

import os
import sys
import tempfile
from pathlib import Path

# Предотвращаем запуск CLI
sys.argv = ['test']

def test_markdown_document_creation():
    """Тест создания Markdown документа"""
    print("\n🧪 ТЕСТ СОЗДАНИЯ MARKDOWN ДОКУМЕНТА")
    print("=" * 60)
    
    try:
        from leadgen_enhanced import create_markdown_document
        
        # Тестовые данные
        company_name = "Тестовая Компания"
        
        company_profile = """### 1. Основные ML/AI‑проекты
Компания активно развивает проекты в области машинного обучения:
- Система рекомендаций для клиентов
- Автоматизация процессов с помощью ИИ
- Анализ больших данных

### 2. Контакты LPR + телефоны/e‑mail
• Иванов Александр — CTO (<EMAIL>)
• Петрова Мария — CIO (<EMAIL>)
• Сидоров Сергей — Директор ИТ (<EMAIL>)

### 3. Релевантные закупки с kontur.zakupki
Найдены следующие закупки:
- Разработка системы ИИ (2023)
- Внедрение ML платформы (2024)
- Консультации по автоматизации (2024)"""

        email_content = """Уважаемый Александр!

Мы изучили ваши ML/AI проекты и готовы предложить решение для автоматизации процессов.

Наша компания Nova AI специализируется на:
- Разработке систем машинного обучения
- Внедрении ИИ решений
- Консультациях по цифровизации

Предлагаем встречу для обсуждения возможностей сотрудничества.

С уважением,
Команда Nova AI"""

        # Создаем временный файл
        with tempfile.NamedTemporaryFile(mode='w', suffix='.md', delete=False, encoding='utf-8') as tmp_file:
            output_path = tmp_file.name
        
        print(f"📝 Создание Markdown документа...")
        print(f"  Компания: {company_name}")
        print(f"  Выходной файл: {output_path}")
        
        # Создаем документ
        create_markdown_document(email_content, company_profile, output_path, company_name)
        
        # Проверяем результат
        if os.path.exists(output_path):
            file_size = os.path.getsize(output_path)
            print(f"✅ Документ создан успешно")
            print(f"  Размер файла: {file_size} байт")
            
            # Читаем содержимое
            with open(output_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            print(f"  Длина содержимого: {len(content)} символов")
            
            # Проверяем структуру
            has_title = f"# Ценностное предложение для {company_name}" in content
            has_ml_section = "## 1. Основные ML/AI‑проекты" in content
            has_contacts_section = "## 2. Контакты LPR + телефоны/e‑mail" in content
            has_tenders_section = "## 3. Релевантные закупки с kontur.zakupki" in content
            has_email_section = "## Кастомизированное письмо" in content
            has_separator = "---" in content
            
            print(f"\n📋 Проверка структуры документа:")
            print(f"  ✅ Заголовок документа: {has_title}")
            print(f"  ✅ Секция ML/AI проектов: {has_ml_section}")
            print(f"  ✅ Секция контактов: {has_contacts_section}")
            print(f"  ✅ Секция закупок: {has_tenders_section}")
            print(f"  ✅ Секция письма: {has_email_section}")
            print(f"  ✅ Разделитель: {has_separator}")
            
            # Показываем начало документа
            print(f"\n📄 Начало документа:")
            lines = content.split('\n')
            for i, line in enumerate(lines[:10], 1):
                print(f"  {i:2d}: {line}")
            
            # Очищаем временный файл
            os.unlink(output_path)
            print(f"\n🗑️  Временный файл удален")
            
            # Проверяем все критерии
            all_checks = all([has_title, has_ml_section, has_contacts_section, 
                            has_tenders_section, has_email_section, has_separator])
            
            return all_checks and file_size > 0
            
        else:
            print(f"❌ Документ не создан")
            return False
            
    except Exception as e:
        print(f"❌ Ошибка теста: {e}")
        return False

def test_markdown_formatting():
    """Тест правильности Markdown форматирования"""
    print("\n🧪 ТЕСТ MARKDOWN ФОРМАТИРОВАНИЯ")
    print("=" * 60)
    
    try:
        from leadgen_enhanced import create_markdown_document
        
        # Тестовые данные с различными элементами
        company_name = "Форматирование Тест"
        
        company_profile = """### 1. Основные ML/AI‑проекты
Список проектов:
- Проект 1
- Проект 2
- Проект 3

Дополнительная информация.

### 2. Контакты LPR + телефоны/e‑mail
• Контакт 1
• Контакт 2

### 3. Релевантные закупки с kontur.zakupki
Не найдено"""

        email_content = """Строка 1

Строка 2

Строка 3"""

        # Создаем временный файл
        with tempfile.NamedTemporaryFile(mode='w', suffix='.md', delete=False, encoding='utf-8') as tmp_file:
            output_path = tmp_file.name
        
        print(f"📝 Тестирование форматирования...")
        
        # Создаем документ
        create_markdown_document(email_content, company_profile, output_path, company_name)
        
        # Читаем и анализируем
        with open(output_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        lines = content.split('\n')
        
        # Проверяем Markdown элементы
        has_h1_title = any(line.startswith('# ') for line in lines)
        has_h2_sections = any(line.startswith('## ') for line in lines)
        has_separator = '---' in lines
        has_empty_lines = '' in lines  # Пустые строки для читаемости
        
        # Проверяем правильную обработку "Не найдено"
        handles_not_found = "Информация не найдена" in content
        
        print(f"📋 Проверка Markdown элементов:")
        print(f"  ✅ H1 заголовки (#): {has_h1_title}")
        print(f"  ✅ H2 заголовки (##): {has_h2_sections}")
        print(f"  ✅ Разделитель (---): {has_separator}")
        print(f"  ✅ Пустые строки: {has_empty_lines}")
        print(f"  ✅ Обработка 'Не найдено': {handles_not_found}")
        
        # Проверяем кодировку UTF-8
        has_russian = any(ord(char) > 127 for char in content)
        print(f"  ✅ Поддержка русского языка: {has_russian}")
        
        # Очищаем
        os.unlink(output_path)
        
        return all([has_h1_title, has_h2_sections, has_separator, 
                   has_empty_lines, has_russian])
        
    except Exception as e:
        print(f"❌ Ошибка теста: {e}")
        return False

def test_markdown_vs_word_comparison():
    """Тест сравнения Markdown и Word форматов"""
    print("\n🧪 ТЕСТ СРАВНЕНИЯ MARKDOWN VS WORD")
    print("=" * 60)
    
    try:
        from leadgen_enhanced import create_markdown_document
        
        # Простые тестовые данные
        company_name = "Сравнение Форматов"
        company_profile = "### 1. Основные ML/AI‑проекты\nТестовый проект"
        email_content = "Тестовое письмо"
        
        # Создаем Markdown
        with tempfile.NamedTemporaryFile(mode='w', suffix='.md', delete=False, encoding='utf-8') as tmp_file:
            md_path = tmp_file.name
        
        create_markdown_document(email_content, company_profile, md_path, company_name)
        
        # Анализируем Markdown
        with open(md_path, 'r', encoding='utf-8') as f:
            md_content = f.read()
        
        md_size = os.path.getsize(md_path)
        md_lines = len(md_content.split('\n'))
        
        print(f"📊 Анализ Markdown документа:")
        print(f"  Размер файла: {md_size} байт")
        print(f"  Количество строк: {md_lines}")
        print(f"  Кодировка: UTF-8")
        print(f"  Читаемость: Высокая (обычный текст)")
        print(f"  Совместимость: Универсальная")
        print(f"  Редактирование: Любой текстовый редактор")
        
        # Проверяем читаемость
        is_readable = all(ord(char) < 128 or ord(char) > 127 for char in md_content if char.isprintable())
        has_formatting = any(line.startswith('#') for line in md_content.split('\n'))
        
        print(f"\n✅ Преимущества Markdown:")
        print(f"  • Читаемый исходный код: {is_readable}")
        print(f"  • Простое форматирование: {has_formatting}")
        print(f"  • Нет зависимостей от библиотек")
        print(f"  • Поддержка Git и версионирования")
        print(f"  • Конвертация в HTML/PDF при необходимости")
        print(f"  • Кроссплатформенность")
        
        # Очищаем
        os.unlink(md_path)
        
        return True
        
    except Exception as e:
        print(f"❌ Ошибка теста: {e}")
        return False

def test_imports():
    """Тест импортов обновленных функций"""
    print("\n🧪 ТЕСТ ИМПОРТОВ ОБНОВЛЕННЫХ ФУНКЦИЙ")
    print("=" * 60)
    
    try:
        from leadgen_enhanced import create_markdown_document
        
        print("✅ Функция импортирована успешно:")
        print("  • create_markdown_document (новая функция создания MD)")
        
        # Проверяем сигнатуру функции
        import inspect
        sig = inspect.signature(create_markdown_document)
        params = list(sig.parameters.keys())
        
        expected_params = ['email_content', 'company_profile', 'output_path', 'company_name']
        params_correct = params == expected_params
        
        print(f"\n📋 Проверка сигнатуры функции:")
        print(f"  Ожидаемые параметры: {expected_params}")
        print(f"  Фактические параметры: {params}")
        print(f"  ✅ Сигнатура корректна: {params_correct}")
        
        return params_correct
        
    except Exception as e:
        print(f"❌ Ошибка импорта: {e}")
        return False

def main():
    """Главная функция тестирования"""
    print("🧪 ТЕСТИРОВАНИЕ СОЗДАНИЯ MARKDOWN ДОКУМЕНТОВ")
    print("=" * 80)
    
    # Тесты
    tests = [
        ("Импорты обновленных функций", test_imports()),
        ("Создание Markdown документа", test_markdown_document_creation()),
        ("Markdown форматирование", test_markdown_formatting()),
        ("Сравнение Markdown vs Word", test_markdown_vs_word_comparison())
    ]
    
    print("\n📊 РЕЗУЛЬТАТЫ ТЕСТИРОВАНИЯ:")
    print("=" * 80)
    
    passed = 0
    for test_name, result in tests:
        status = "✅ ПРОЙДЕН" if result else "❌ НЕ ПРОЙДЕН"
        print(f"  {test_name}: {status}")
        if result:
            passed += 1
    
    total_tests = len(tests)
    print(f"\n🎯 ИТОГО: {passed}/{total_tests} тестов пройдено")
    
    if passed == total_tests:
        print("🎉 ВСЕ ТЕСТЫ ПРОЙДЕНЫ!")
        print("\n✅ ПЕРЕХОД НА MARKDOWN ДОКУМЕНТЫ:")
        print("  • Создание .md файлов вместо .docx")
        print("  • Правильное UTF-8 форматирование")
        print("  • Сохранение структуры документа")
        print("  • Поддержка всех секций")
        print("  • Читаемый исходный код")
        print("  • Нет зависимостей от python-docx")
        print("  • Универсальная совместимость")
        print("  • Простое редактирование")
    elif passed > 2:
        print("⚠️  Частично работает - проверьте создание документов")
    else:
        print("❌ Есть серьезные проблемы с созданием Markdown документов")

if __name__ == "__main__":
    main()
