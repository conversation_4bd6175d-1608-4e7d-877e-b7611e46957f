"""test_corrected_logic.py
Тест исправленной логики поиска контактов
"""

import os
import sys

# Предотвращаем запуск CLI
sys.argv = ['test']

def test_transliteration():
    """Тест транслитерации русских имен в английский"""
    print("🧪 ТЕСТ ТРАНСЛИТЕРАЦИИ")
    print("=" * 50)
    
    try:
        from leadgen_enhanced import transliterate_to_english
        
        test_cases = [
            ("Александр", "aleksandr"),
            ("Иванов", "ivanov"),
            ("Сергей", "sergey"),
            ("Петрова", "petrova"),
            ("Юлия", "yuliya"),
            ("Щербаков", "scherbakov")
        ]
        
        all_correct = True
        for russian, expected in test_cases:
            result = transliterate_to_english(russian)
            print(f"  {russian} → {result} (ожидалось: {expected})")
            if result != expected:
                all_correct = False
        
        return all_correct
        
    except Exception as e:
        print(f"❌ Ошибка теста: {e}")
        return False

def test_email_generation_with_transliteration():
    """Тест генерации email с транслитерацией"""
    print("\n🧪 ТЕСТ ГЕНЕРАЦИИ EMAIL С ТРАНСЛИТЕРАЦИЕЙ")
    print("=" * 50)
    
    try:
        from leadgen_enhanced import generate_email_variants
        
        # Тест с русскими именами
        first_name = "Александр"
        last_name = "Иванов"
        domain = "company.ru"
        patterns = ["name.surname@domain", "n.surname@domain"]
        
        emails = generate_email_variants(first_name, last_name, domain, patterns)
        
        print(f"👤 Русские имена: {first_name} {last_name}")
        print(f"🌐 Домен: {domain}")
        print(f"📧 Сгенерированные email:")
        
        expected_emails = ["<EMAIL>", "<EMAIL>"]
        
        for i, email in enumerate(emails, 1):
            print(f"  {i}. {email}")
        
        # Проверяем, что email на английском
        all_english = all(all(ord(c) < 128 for c in email.split('@')[0]) for email in emails)
        has_expected = any(expected in emails for expected in expected_emails)
        
        print(f"✅ Все email на английском: {all_english}")
        print(f"✅ Содержит ожидаемые варианты: {has_expected}")
        
        return all_english and has_expected
        
    except Exception as e:
        print(f"❌ Ошибка теста: {e}")
        return False

def test_company_name_extraction():
    """Тест извлечения 'сердца' названия компании"""
    print("\n🧪 ТЕСТ ИЗВЛЕЧЕНИЯ НАЗВАНИЯ КОМПАНИИ")
    print("=" * 50)
    
    try:
        from leadgen_enhanced import _extract_company_name_from_quotes
        
        test_cases = [
            ('Федеральное ГБУ «Российский Фонд Информации по Природным Ресурсам и Охране Окружающей Среды Минприроды России»', 
             'Российский Фонд Информации по Природным Ресурсам и Охране Окружающей Среды Минприроды России'),
            ('ООО "Сбербанк Технологии"', 'Сбербанк Технологии'),
            ('Яндекс', 'Яндекс'),
            ('АО «ВТБ»', 'ВТБ')
        ]
        
        all_correct = True
        for full_name, expected in test_cases:
            result = _extract_company_name_from_quotes(full_name)
            print(f"  '{full_name}' → '{result}'")
            print(f"    Ожидалось: '{expected}'")
            if result != expected:
                print(f"    ❌ Не совпадает!")
                all_correct = False
            else:
                print(f"    ✅ Правильно!")
        
        return all_correct
        
    except Exception as e:
        print(f"❌ Ошибка теста: {e}")
        return False

def test_contact_parsing():
    """Тест парсинга контактов"""
    print("\n🧪 ТЕСТ ПАРСИНГА КОНТАКТОВ")
    print("=" * 50)
    
    try:
        from leadgen_enhanced import parse_contacts_from_text
        
        # Тестовый текст с русскими именами
        test_text = """
        CONTACT_START
        FIRST_NAME: Александр
        LAST_NAME: Иванов
        POSITION: CTO
        CONFIDENCE: high
        CONTACT_END
        
        CONTACT_START
        FIRST_NAME: Мария
        LAST_NAME: Петрова
        POSITION: CIO
        CONFIDENCE: medium
        CONTACT_END
        """
        
        contacts = parse_contacts_from_text(test_text)
        
        print(f"📊 Найдено контактов: {len(contacts)}")
        
        for i, contact in enumerate(contacts, 1):
            print(f"  {i}. {contact.get('last_name', '')} {contact.get('first_name', '')} - {contact.get('position', '')}")
            print(f"     Уверенность: {contact.get('confidence', 'unknown')}")
        
        # Проверяем, что имена на русском (как и должно быть на входе)
        has_russian_names = any('Александр' in contact.get('first_name', '') for contact in contacts)
        
        return len(contacts) == 2 and has_russian_names
        
    except Exception as e:
        print(f"❌ Ошибка теста: {e}")
        return False

def main():
    """Главная функция тестирования"""
    print("🧪 ТЕСТИРОВАНИЕ ИСПРАВЛЕННОЙ ЛОГИКИ КОНТАКТОВ")
    print("=" * 80)
    
    tests = [
        ("Транслитерация", test_transliteration()),
        ("Email с транслитерацией", test_email_generation_with_transliteration()),
        ("Извлечение названия компании", test_company_name_extraction()),
        ("Парсинг контактов", test_contact_parsing())
    ]
    
    print("\n📊 РЕЗУЛЬТАТЫ ТЕСТИРОВАНИЯ:")
    print("=" * 80)
    
    passed = 0
    for test_name, result in tests:
        status = "✅ ПРОЙДЕН" if result else "❌ НЕ ПРОЙДЕН"
        print(f"  {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n🎯 ИТОГО: {passed}/{len(tests)} тестов пройдено")
    
    if passed == len(tests):
        print("🎉 ВСЕ ТЕСТЫ ПРОЙДЕНЫ!")
        print("\n✅ ИСПРАВЛЕННАЯ ЛОГИКА РАБОТАЕТ:")
        print("  • Русские имена транслитерируются в английский для email")
        print("  • Используется 'сердце' названия компании для Setka API")
        print("  • Одинаковая обработка для веб-поиска и Setka API")
        print("  • Email генерируются на английском языке")
    else:
        print("⚠️  Есть проблемы, требующие внимания.")

if __name__ == "__main__":
    main()
