"""company_search.py
-----------------------------------------------------------------------
Функция поиска сотрудников компании по должностям через API Setka.ru
-----------------------------------------------------------------------
Функция выполняет последовательный поиск:
1. Поиск компании по названию
2. Получение списка Networks (если есть)
3. Извлечение данных сотрудников из первой Network
4. Фильтрация по ключевым словам в должностях
5. Создание отчета в Word документе
"""

import asyncio
import os
import re
from typing import List, Dict, Any, Optional, Tuple, NamedTuple
import aiohttp
import json
from pathlib import Path
from datetime import datetime

try:
    from docx import Document  # type: ignore
    from docx.shared import Inches  # type: ignore
    from docx.enum.text import WD_ALIGN_PARAGRAPH  # type: ignore
    DOCX_AVAILABLE = True
except ModuleNotFoundError:
    print("Warning: python-docx не установлен. Установите: pip install python-docx")
    Document = None
    DOCX_AVAILABLE = False

# Конфигурация API
SETKA_API_BASE = "https://api.setka.ru"
REFRESH_TOKEN_ENDPOINT = f"{SETKA_API_BASE}/v1/oauth/refresh"
SEARCH_ENDPOINT = f"{SETKA_API_BASE}/v1/search/all"
NETWORK_MEMBERS_ENDPOINT = f"{SETKA_API_BASE}/v2/networks"

# Ключевые слова для фильтрации должностей
POSITION_KEYWORDS = ["директор", "ИТ", "CIO", "CTO", "IT", "информационных технологий",
                    "цифровых технологий", "цифровизации", "автоматизации", "разработки"]

class TokenInfo:
    """Класс для хранения информации о токенах"""
    def __init__(self, access_token: str, refresh_token: str,
                 access_token_expired_at: str, refresh_token_expired_at: str):
        self.access_token = access_token
        self.refresh_token = refresh_token
        self.access_token_expired_at = access_token_expired_at
        self.refresh_token_expired_at = refresh_token_expired_at

class SetkaAPIClient:
    """Клиент для работы с API Setka.ru с автоматическим обновлением токенов"""

    def __init__(self, refresh_token: str):
        self.refresh_token = refresh_token
        self.token_info: Optional[TokenInfo] = None

    async def refresh_access_token(self) -> TokenInfo:
        """Обновление access токена используя refresh токен"""
        async with aiohttp.ClientSession() as session:
            payload = {"refresh_token": self.refresh_token}

            async with session.post(REFRESH_TOKEN_ENDPOINT, json=payload) as response:
                if response.status == 200:
                    data = await response.json()

                    token_info = TokenInfo(
                        access_token=data["access_token"],
                        refresh_token=data["refresh_token"],
                        access_token_expired_at=data["access_token_expired_at"],
                        refresh_token_expired_at=data["refresh_token_expired_at"]
                    )

                    # Обновляем refresh токен для следующих запросов
                    self.refresh_token = token_info.refresh_token
                    self.token_info = token_info

                    return token_info
                else:
                    error_text = await response.text()
                    raise Exception(f"Ошибка обновления токена: {response.status} - {error_text}")

    async def get_headers(self) -> Dict[str, str]:
        """Получение заголовков с актуальным access токеном"""
        if self.token_info is None:
            await self.refresh_access_token()

        return {
            "Authorization": f"Bearer {self.token_info.access_token}",
            "Content-Type": "application/json"
        }
    
    async def search_company(self, company_name: str) -> Dict[str, Any]:
        """Поиск компании по названию"""
        headers = await self.get_headers()
        async with aiohttp.ClientSession() as session:
            params = {"text": company_name}
            async with session.get(SEARCH_ENDPOINT, headers=headers, params=params) as response:
                if response.status == 200:
                    return await response.json()
                elif response.status == 401:
                    # Токен истек, обновляем и повторяем запрос
                    print("  🔄 Access токен истек, обновляем...")
                    await self.refresh_access_token()
                    headers = await self.get_headers()
                    async with session.get(SEARCH_ENDPOINT, headers=headers, params=params) as response:
                        if response.status == 200:
                            return await response.json()
                        else:
                            raise Exception(f"Ошибка поиска компании после обновления токена: {response.status} - {await response.text()}")
                else:
                    raise Exception(f"Ошибка поиска компании: {response.status} - {await response.text()}")

    async def get_network_members(self, network_id: str, limit: int = 1000) -> Dict[str, Any]:
        """Получение участников сети компании"""
        headers = await self.get_headers()
        url = f"{NETWORK_MEMBERS_ENDPOINT}/{network_id}/members/search"
        params = {
            "limit": limit,
            "type": "working_now",
            "query": "",
            "cursor": ""
        }

        async with aiohttp.ClientSession() as session:
            async with session.get(url, headers=headers, params=params) as response:
                if response.status == 200:
                    return await response.json()
                elif response.status == 401:
                    # Токен истек, обновляем и повторяем запрос
                    print("  🔄 Access токен истек, обновляем...")
                    await self.refresh_access_token()
                    headers = await self.get_headers()
                    async with session.get(url, headers=headers, params=params) as response:
                        if response.status == 200:
                            return await response.json()
                        else:
                            raise Exception(f"Ошибка получения участников сети после обновления токена: {response.status} - {await response.text()}")
                else:
                    raise Exception(f"Ошибка получения участников сети: {response.status} - {await response.text()}")

    def get_current_refresh_token(self) -> str:
        """Получение текущего refresh токена"""
        return self.refresh_token

def extract_networks_from_search(search_result: Dict[str, Any]) -> List[Dict[str, Any]]:
    """Извлечение Networks из результатов поиска"""
    networks = []
    for item in search_result.get("items", []):
        if item.get("type") == "NETWORKS":
            networks.extend(item.get("items", []))
    return networks

def extract_accounts_from_search(search_result: Dict[str, Any]) -> List[Dict[str, Any]]:
    """Извлечение Accounts из результатов поиска"""
    accounts = []
    for item in search_result.get("items", []):
        if item.get("type") == "ACCOUNTS":
            accounts.extend(item.get("items", []))
    return accounts

def extract_employee_data(members_data: Dict[str, Any]) -> List[Tuple[str, str, str]]:
    """Извлечение данных сотрудников из members: [фамилия, имя, должность]"""
    employees = []
    for member in members_data.get("members", []):
        first_name = member.get("first_name", "").strip()
        last_name = member.get("last_name", "").strip()

        # Получаем должность из position
        position_info = member.get("position", {})
        position = position_info.get("name", "").strip() if position_info else ""

        # Если должность пустая, пропускаем
        if not position or not first_name or not last_name:
            continue

        employees.append((last_name, first_name, position))

    return employees

def extract_employee_data_from_accounts(accounts: List[Dict[str, Any]]) -> List[Tuple[str, str, str]]:
    """Извлечение данных сотрудников из accounts: [фамилия, имя, должность]"""
    employees = []
    for account in accounts:
        first_name = account.get("first_name", "").strip()
        last_name = account.get("last_name", "").strip()

        # Получаем должность из position или position_title
        position = ""
        if account.get("position"):
            position = account.get("position", {}).get("name", "").strip()
        elif account.get("position_title"):
            position = account.get("position_title", "").strip()

        # Если должность пустая, пропускаем
        if not position or not first_name or not last_name:
            continue

        employees.append((last_name, first_name, position))

    return employees

def filter_by_position_keywords(employees: List[Tuple[str, str, str]], 
                               keywords: List[str] = POSITION_KEYWORDS) -> List[Tuple[str, str, str]]:
    """Фильтрация сотрудников по ключевым словам в должности"""
    filtered_employees = []
    
    for last_name, first_name, position in employees:
        position_lower = position.lower()
        
        # Проверяем наличие любого из ключевых слов в должности
        if any(keyword.lower() in position_lower for keyword in keywords):
            filtered_employees.append((last_name, first_name, position))
    
    return filtered_employees

def create_employees_report(company_name: str, 
                          employees: List[Tuple[str, str, str]], 
                          output_path: str) -> None:
    """Создание отчета в Word документе"""
    if not DOCX_AVAILABLE:
        raise ImportError("python-docx не установлен. Установите: pip install python-docx")
    
    # Создаем новый документ
    doc = Document()
    
    # Добавляем заголовок
    title = doc.add_heading(f'Отчет по сотрудникам компании "{company_name}"', 0)
    title.alignment = WD_ALIGN_PARAGRAPH.CENTER
    
    # Добавляем информацию о поиске
    doc.add_paragraph()
    info_para = doc.add_paragraph()
    info_para.add_run("Дата создания отчета: ").bold = True
    info_para.add_run(f"{datetime.now().strftime('%d.%m.%Y %H:%M')}")
    
    info_para2 = doc.add_paragraph()
    info_para2.add_run("Критерии фильтрации: ").bold = True
    info_para2.add_run(f"{', '.join(POSITION_KEYWORDS)}")
    
    info_para3 = doc.add_paragraph()
    info_para3.add_run("Найдено сотрудников: ").bold = True
    info_para3.add_run(f"{len(employees)}")
    
    # Добавляем таблицу с сотрудниками
    if employees:
        doc.add_paragraph()
        doc.add_heading('Список сотрудников', level=1)
        
        # Создаем таблицу
        table = doc.add_table(rows=1, cols=3)
        table.style = 'Table Grid'
        
        # Заголовки таблицы
        header_cells = table.rows[0].cells
        header_cells[0].text = 'Фамилия'
        header_cells[1].text = 'Имя'
        header_cells[2].text = 'Должность'
        
        # Делаем заголовки жирными
        for cell in header_cells:
            for paragraph in cell.paragraphs:
                for run in paragraph.runs:
                    run.bold = True
        
        # Добавляем данные сотрудников
        for last_name, first_name, position in employees:
            row_cells = table.add_row().cells
            row_cells[0].text = last_name
            row_cells[1].text = first_name
            row_cells[2].text = position
    else:
        doc.add_paragraph()
        no_data_para = doc.add_paragraph()
        no_data_para.add_run("Сотрудники, соответствующие критериям поиска, не найдены.").italic = True
    
    # Сохраняем документ
    doc.save(output_path)

async def search_company_employees(company_name: str,
                                 refresh_token: str,
                                 output_dir: str = "./reports",
                                 keywords: Optional[List[str]] = None) -> Tuple[List[Tuple[str, str, str]], str]:
    """
    Основная функция поиска сотрудников компании по должностям

    Args:
        company_name: Название компании для поиска
        refresh_token: Refresh токен для API Setka.ru
        output_dir: Директория для сохранения отчета
        keywords: Список ключевых слов для фильтрации должностей

    Returns:
        Кортеж: (список сотрудников, новый refresh токен)
        - список сотрудников: List[Tuple[str, str, str]] - (фамилия, имя, должность)
        - новый refresh токен: str - обновленный refresh токен для последующего использования
    """
    if keywords is None:
        keywords = POSITION_KEYWORDS
    
    print(f"🔍 Начинаем поиск сотрудников компании: {company_name}")

    # Создаем клиент API с refresh токеном
    client = SetkaAPIClient(refresh_token)
    
    try:
        # Шаг 1: Поиск компании
        print("  1. Поиск компании...")
        search_result = await client.search_company(company_name)
        
        # Шаг 2: Извлечение Networks
        print("  2. Поиск Networks...")
        networks = extract_networks_from_search(search_result)

        all_employees = []

        if networks:
            print(f"  ✓ Найдено {len(networks)} Networks")

            # Шаг 3: Получение участников первой Network
            first_network = networks[0]
            network_id = first_network.get("id")
            network_name = first_network.get("name", "")
            members_count = first_network.get("members_count", 0)

            print(f"  3. Получение участников Network '{network_name}' (ID: {network_id})")
            print(f"     Общее количество участников: {members_count}")

            members_data = await client.get_network_members(network_id)

            # Шаг 4: Извлечение данных сотрудников из Network
            print("  4. Извлечение данных сотрудников из Network...")
            all_employees = extract_employee_data(members_data)
            print(f"     Извлечено {len(all_employees)} сотрудников с полными данными")

        else:
            print("  ⚠️  Networks не найдены, ищем в Accounts...")

            # Шаг 3 (альтернативный): Извлечение Accounts
            accounts = extract_accounts_from_search(search_result)

            if accounts:
                print(f"  ✓ Найдено {len(accounts)} Accounts")

                # Шаг 4 (альтернативный): Извлечение данных сотрудников из Accounts
                print("  4. Извлечение данных сотрудников из Accounts...")
                all_employees = extract_employee_data_from_accounts(accounts)
                print(f"     Извлечено {len(all_employees)} сотрудников с полными данными")
            else:
                print("  ❌ Не найдено ни Networks, ни Accounts для данной компании")
                # Получаем обновленный refresh токен даже при отсутствии данных
                updated_refresh_token = client.get_current_refresh_token()
                return [], updated_refresh_token
        
        # Шаг 5: Фильтрация по ключевым словам
        print("  5. Фильтрация по ключевым словам...")
        filtered_employees = filter_by_position_keywords(all_employees, keywords)
        print(f"     После фильтрации: {len(filtered_employees)} сотрудников")
        
        # Шаг 6: Создание отчета
        print("  6. Создание отчета...")
        Path(output_dir).mkdir(parents=True, exist_ok=True)
        
        # Создаем безопасное имя файла
        safe_company_name = re.sub(r'[^\w\s-]', '', company_name).strip()
        safe_company_name = re.sub(r'[-\s]+', '-', safe_company_name)
        
        report_filename = f"employees_report_{safe_company_name}.docx"
        report_path = Path(output_dir) / report_filename
        
        create_employees_report(company_name, filtered_employees, str(report_path))
        print(f"  ✓ Отчет сохранен: {report_path}")

        # Получаем обновленный refresh токен
        updated_refresh_token = client.get_current_refresh_token()
        print(f"  🔑 Обновленный refresh токен: {updated_refresh_token[:20]}...")

        return filtered_employees, updated_refresh_token
        
    except Exception as e:
        print(f"  ❌ Ошибка при поиске сотрудников: {e}")
        # Возвращаем текущий refresh токен даже в случае ошибки
        updated_refresh_token = client.get_current_refresh_token()
        raise Exception(f"Ошибка поиска: {e}. Refresh токен: {updated_refresh_token}")

# Функция для синхронного вызова
def search_company_employees_sync(company_name: str,
                                refresh_token: str,
                                output_dir: str = "./reports",
                                keywords: Optional[List[str]] = None) -> Tuple[List[Tuple[str, str, str]], str]:
    """
    Синхронная обертка для основной функции поиска

    Returns:
        Кортеж: (список сотрудников, новый refresh токен)
    """
    return asyncio.run(search_company_employees(company_name, refresh_token, output_dir, keywords))

if __name__ == "__main__":
    # Пример использования
    import sys

    if len(sys.argv) < 3:
        print("Использование: python company_search.py <название_компании> <refresh_token>")
        print("Пример: python company_search.py 'Автоваз' 'your_refresh_token_here'")
        sys.exit(1)

    company = sys.argv[1]
    refresh_token = sys.argv[2]

    try:
        employees, new_refresh_token = search_company_employees_sync(company, refresh_token)
        print(f"\n✅ Поиск завершен! Найдено {len(employees)} сотрудников:")
        for last_name, first_name, position in employees:
            print(f"  - {last_name} {first_name}: {position}")

        print(f"\n🔑 Новый refresh токен: {new_refresh_token}")
        print("💡 Сохраните этот токен для следующего использования!")

    except Exception as e:
        print(f"\n❌ Ошибка: {e}")
        sys.exit(1)
