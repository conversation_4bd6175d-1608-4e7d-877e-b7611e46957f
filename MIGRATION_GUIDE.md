# 🔄 Руководство по миграции: Переход на refresh токены

## 📋 Что изменилось

Функция поиска сотрудников была обновлена для работы с **refresh токенами** вместо access токенов, что обеспечивает автоматическое управление токенами и улучшенную безопасность.

## 🔄 Основные изменения

### Было (версия с access токеном):
```python
# Старая версия
employees = search_company_employees_sync(
    company_name="Автоваз",
    access_token="your_access_token_here",  # ❌ access_token
    output_dir="./reports"
)
# Возвращал: List[Tuple[str, str, str]]
```

### Стало (версия с refresh токеном):
```python
# Новая версия
employees, new_refresh_token = search_company_employees_sync(
    company_name="Автоваз",
    refresh_token="your_refresh_token_here",  # ✅ refresh_token
    output_dir="./reports"
)
# Возвращает: Tuple[List[Tuple[str, str, str]], str]
```

## 📝 Пошаговая миграция

### Шаг 1: Обновите вызовы функций

**Было:**
```python
from company_search import search_company_employees_sync

employees = search_company_employees_sync(
    company_name="Компания",
    access_token="access_token_value"
)

for employee in employees:
    print(employee)
```

**Стало:**
```python
from company_search import search_company_employees_sync

employees, new_refresh_token = search_company_employees_sync(
    company_name="Компания",
    refresh_token="refresh_token_value"  # Используйте refresh_token
)

# Сохраните новый токен для следующего использования
print(f"Новый refresh токен: {new_refresh_token}")

for employee in employees:
    print(employee)
```

### Шаг 2: Обновите переменные окружения

**Было:**
```bash
export SETKA_ACCESS_TOKEN="your_access_token"
```

**Стало:**
```bash
export SETKA_REFRESH_TOKEN="your_refresh_token"
```

### Шаг 3: Обновите обработку множественных запросов

**Было:**
```python
companies = ["Компания1", "Компания2", "Компания3"]
access_token = "your_access_token"

for company in companies:
    employees = search_company_employees_sync(
        company_name=company,
        access_token=access_token  # Один и тот же токен
    )
    process_employees(employees)
```

**Стало:**
```python
companies = ["Компания1", "Компания2", "Компания3"]
current_refresh_token = "your_refresh_token"

for company in companies:
    employees, new_refresh_token = search_company_employees_sync(
        company_name=company,
        refresh_token=current_refresh_token
    )
    
    # Обновляем токен для следующего запроса
    current_refresh_token = new_refresh_token
    
    process_employees(employees)

# Сохраняем финальный токен
save_refresh_token(current_refresh_token)
```

### Шаг 4: Обновите асинхронные вызовы

**Было:**
```python
import asyncio
from company_search import search_company_employees

async def main():
    employees = await search_company_employees(
        company_name="Компания",
        access_token="access_token"
    )
    return employees

result = asyncio.run(main())
```

**Стало:**
```python
import asyncio
from company_search import search_company_employees

async def main():
    employees, new_refresh_token = await search_company_employees(
        company_name="Компания",
        refresh_token="refresh_token"
    )
    return employees, new_refresh_token

employees, new_token = asyncio.run(main())
print(f"Сохраните токен: {new_token}")
```

## 🔧 Обновление скриптов

### Командная строка

**Было:**
```bash
python3 company_search.py "Автоваз" "access_token_here"
```

**Стало:**
```bash
python3 company_search.py "Автоваз" "refresh_token_here"
```

### Пример скрипта

**Было:**
```python
#!/usr/bin/env python3
import os
from company_search import search_company_employees_sync

def main():
    access_token = os.getenv("SETKA_ACCESS_TOKEN")
    
    employees = search_company_employees_sync(
        company_name="Автоваз",
        access_token=access_token
    )
    
    print(f"Найдено {len(employees)} сотрудников")

if __name__ == "__main__":
    main()
```

**Стало:**
```python
#!/usr/bin/env python3
import os
from company_search import search_company_employees_sync

def main():
    refresh_token = os.getenv("SETKA_REFRESH_TOKEN")
    
    employees, new_refresh_token = search_company_employees_sync(
        company_name="Автоваз",
        refresh_token=refresh_token
    )
    
    print(f"Найдено {len(employees)} сотрудников")
    print(f"Новый refresh токен: {new_refresh_token}")
    
    # Опционально: сохранить новый токен
    # os.environ["SETKA_REFRESH_TOKEN"] = new_refresh_token

if __name__ == "__main__":
    main()
```

## ✅ Преимущества новой версии

### 🔐 Безопасность
- **Автоматическое обновление токенов** - не нужно вручную следить за сроком действия
- **Короткоживущие access токены** - снижение риска компрометации
- **Долгоживущие refresh токены** - удобство использования

### 🚀 Удобство
- **Автоматическая обработка истекших токенов** - функция сама обновляет токены при ошибке 401
- **Прозрачное управление** - пользователь работает только с refresh токенами
- **Обратная совместимость** - API функций остался похожим

### 🛠️ Надежность
- **Восстановление после ошибок** - автоматическое восстановление при истечении токенов
- **Улучшенная обработка ошибок** - более информативные сообщения
- **Консистентность токенов** - всегда актуальные токены

## ⚠️ Важные моменты

### 1. Сохранение токенов
```python
# ✅ Правильно - сохраняйте новый токен
employees, new_refresh_token = search_company_employees_sync(...)
save_token_to_config(new_refresh_token)

# ❌ Неправильно - игнорирование нового токена
employees, _ = search_company_employees_sync(...)  # Токен потерян!
```

### 2. Множественные запросы
```python
# ✅ Правильно - передавайте обновленный токен
current_token = initial_refresh_token
for company in companies:
    employees, current_token = search_company_employees_sync(
        company_name=company,
        refresh_token=current_token
    )

# ❌ Неправильно - использование одного токена
for company in companies:
    employees, _ = search_company_employees_sync(
        company_name=company,
        refresh_token=initial_refresh_token  # Может устареть!
    )
```

### 3. Обработка ошибок
```python
try:
    employees, new_token = search_company_employees_sync(...)
    # Обработка успешного результата
    process_employees(employees)
    save_token(new_token)
except Exception as e:
    # Даже при ошибке токен может быть обновлен
    if "Refresh токен:" in str(e):
        # Извлечь и сохранить токен из сообщения об ошибке
        pass
```

## 🧪 Тестирование миграции

После обновления кода протестируйте:

1. **Базовый поиск:**
```bash
python3 company_search.py "Автоваз" "your_refresh_token"
```

2. **Проверка возврата токена:**
```python
employees, new_token = search_company_employees_sync("Автоваз", "refresh_token")
assert new_token != "refresh_token"  # Токен должен обновиться
```

3. **Множественные запросы:**
```python
token = "initial_refresh_token"
for i in range(3):
    employees, token = search_company_employees_sync(f"Компания{i}", token)
    print(f"Итерация {i}: новый токен {token[:20]}...")
```

## 📞 Поддержка

При возникновении проблем с миграцией:

1. Убедитесь, что используете refresh_token, а не access_token
2. Проверьте, что сохраняете возвращаемый токен
3. Запустите тесты: `python3 test_company_search.py --test`
4. Изучите примеры в `example_usage.py`

## 🎯 Чек-лист миграции

- [ ] Заменил `access_token` на `refresh_token` в вызовах функций
- [ ] Обновил обработку возвращаемых значений (теперь кортеж)
- [ ] Изменил переменные окружения с `SETKA_ACCESS_TOKEN` на `SETKA_REFRESH_TOKEN`
- [ ] Обновил логику сохранения токенов в множественных запросах
- [ ] Протестировал обновленный код
- [ ] Обновил документацию/комментарии в коде

✅ **Миграция завершена!** Теперь ваш код использует автоматическое управление токенами.
